import { WorkerRepository } from '@/db/repositories/WorkerRepository';
import { Worker, WorkerSkill } from '@/types/domain/worker.types';
import { createError } from '@/errors/AppError';
import { ForeignKeyError } from '@/db/errors/DatabaseError';

export class WorkerService {
  private workerRepo: WorkerRepository;

  constructor() {
    this.workerRepo = new WorkerRepository();
  }

  async getAllWorkers(): Promise<Worker[]> {
    try {
      return await this.workerRepo.findAll();
    } catch (error: any) {
      throw createError(
        'Failed to fetch workers',
        'WORKER_FETCH_ERROR',
        500,
        error
      );
    }
  }

  async getWorkerById(id: string): Promise<Worker> {
    try {
      return await this.workerRepo.findById(id);
    } catch (error: any) {
      throw createError(
        'Failed to fetch worker',
        'WORKER_FETCH_ERROR',
        500,
        error
      );
    }
  }

  async createWorker(worker: Omit<Worker, 'worker_id'>): Promise<Worker> {
    try {
      // Double assertion to satisfy TypeScript's overlap check
      return await this.workerRepo.create(worker as unknown as Omit<Worker, 'id'>); 
    } catch (error: any) {
      throw createError(
        'Failed to create worker',
        'WORKER_CREATE_ERROR',
        500,
        error
      );
    }
  }

  async updateWorker(id: string, worker: Partial<Worker>): Promise<Worker> {
    try {
      return await this.workerRepo.update(id, worker);
    } catch (error: any) {
      throw createError(
        'Failed to update worker',
        'WORKER_UPDATE_ERROR',
        500,
        error
      );
    }
  }

  /**
   * Deletes a worker and all related data in a single transaction
   * This handles all foreign key constraints safely by first removing related records
   * 
   * @param id - Worker ID to delete
   * @throws AppError with type WORKER_DELETE_ERROR if delete fails
   * @throws AppError with type WORKER_CONSTRAINT_ERROR if worker has active processes
   */
  async deleteWorker(id: string): Promise<void> {
    try {
      await this.workerRepo.safeDelete(id);
    } catch (error: any) {
      if (error instanceof ForeignKeyError) {
        throw createError(
          error.message || 'Cannot delete worker with active processes',
          'WORKER_CONSTRAINT_ERROR',
          400,
          error
        );
      }
      throw createError(
        'Failed to delete worker',
        'WORKER_DELETE_ERROR',
        500,
        error
      );
    }
  }

  async addWorkerSkill(
    workerId: string,
    skill: Omit<WorkerSkill, 'id'>
  ): Promise<WorkerSkill> {
    try {
      return await this.workerRepo.addSkill(workerId, skill);
    } catch (error: any) {
      throw createError(
        'Failed to add worker skill',
        'WORKER_SKILL_ERROR',
        500,
        error
      );
    }
  }

  async updateWorkerSkill(
    skillId: string,
    skill: Partial<WorkerSkill>
  ): Promise<WorkerSkill> {
    try {
      return await this.workerRepo.updateSkill(skillId, skill);
    } catch (error: any) {
      throw createError(
        'Failed to update worker skill',
        'WORKER_SKILL_ERROR',
        500,
        error
      );
    }
  }

  async removeWorkerSkill(skillId: string): Promise<void> {
    try {
      await this.workerRepo.removeSkill(skillId);
    } catch (error: any) {
      throw createError(
        'Failed to remove worker skill',
        'WORKER_SKILL_ERROR',
        500,
        error
      );
    }
  }
}
