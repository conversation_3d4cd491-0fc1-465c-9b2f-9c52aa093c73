'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Search, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/lib/db';
import { MetalColorMaster, MetalTypeMaster } from '@/types/masters';

export default function MetalColorsPage() {
  const [metalColors, setMetalColors] = useState<MetalColorMaster[]>([]);
  const [metalTypes, setMetalTypes] = useState<MetalTypeMaster[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMetalType, setSelectedMetalType] = useState<string>('all');
  const [showForm, setShowForm] = useState(false);
  const [editingColor, setEditingColor] = useState<MetalColorMaster | null>(null);
  const [formData, setFormData] = useState({
    metal_type_id: '',
    description: '',
    processing_complexity_factor: 1.0,
    is_active: true,
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch metal types
      const { data: metalTypesData, error: metalTypesError } = await supabase
        .from('metal_type_mast')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (metalTypesError) throw metalTypesError;
      setMetalTypes(metalTypesData || []);

      // Fetch metal colors with metal type information
      const { data: metalColorsData, error: metalColorsError } = await supabase
        .from('metal_colour_mast')
        .select(`
          *,
          metal_type:metal_type_mast(
            metal_type_id,
            name,
            description
          )
        `)
        .order('metal_type_id, description');

      if (metalColorsError) throw metalColorsError;
      setMetalColors(metalColorsData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingColor) {
        // Update existing color
        const { error } = await supabase
          .from('metal_colour_mast')
          .update({
            ...formData,
            updated_at: new Date().toISOString(),
          })
          .eq('metal_colour_id', editingColor.metal_colour_id);

        if (error) throw error;
        toast({
          title: 'Success',
          description: 'Metal color updated successfully',
        });
      } else {
        // Create new color
        const { error } = await supabase
          .from('metal_colour_mast')
          .insert([{
            ...formData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }]);

        if (error) throw error;
        toast({
          title: 'Success',
          description: 'Metal color created successfully',
        });
      }

      setShowForm(false);
      setEditingColor(null);
      setFormData({
        metal_type_id: '',
        description: '',
        processing_complexity_factor: 1.0,
        is_active: true,
      });
      fetchData();
    } catch (error) {
      console.error('Error saving metal color:', error);
      toast({
        title: 'Error',
        description: 'Failed to save metal color',
        variant: 'destructive',
      });
    }
  };

  const handleEdit = (color: MetalColorMaster) => {
    setEditingColor(color);
    setFormData({
      metal_type_id: color.metal_type_id,
      description: color.description,
      processing_complexity_factor: color.processing_complexity_factor,
      is_active: color.is_active,
    });
    setShowForm(true);
  };

  const handleDelete = async (colorId: string) => {
    if (!confirm('Are you sure you want to delete this metal color?')) return;

    try {
      const { error } = await supabase
        .from('metal_colour_mast')
        .delete()
        .eq('metal_colour_id', colorId);

      if (error) throw error;
      toast({
        title: 'Success',
        description: 'Metal color deleted successfully',
      });
      fetchData();
    } catch (error) {
      console.error('Error deleting metal color:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete metal color',
        variant: 'destructive',
      });
    }
  };

  const filteredColors = metalColors.filter(color => {
    const matchesSearch = color.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         color.metal_type?.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedMetalType === 'all' || color.metal_type_id === selectedMetalType;
    return matchesSearch && matchesType;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Metal Colors Management</h1>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Metal Color
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search colors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={selectedMetalType}
          onChange={(e) => setSelectedMetalType(e.target.value)}
          className="w-48 h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Metal Types</option>
          {metalTypes.map((type) => (
            <option key={type.metal_type_id} value={type.metal_type_id}>
              {type.name}
            </option>
          ))}
        </select>
      </div>

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingColor ? 'Edit Metal Color' : 'Add New Metal Color'}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Metal Type *</label>
                  <select
                    value={formData.metal_type_id}
                    onChange={(e) => setFormData({ ...formData, metal_type_id: e.target.value })}
                    required
                    className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select metal type</option>
                    {metalTypes.map((type) => (
                      <option key={type.metal_type_id} value={type.metal_type_id}>
                        {type.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Color Description *</label>
                  <Input
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="e.g., Yellow, White, Rose, Natural Silver"
                    required
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Processing Complexity Factor *</label>
                <Input
                  type="number"
                  step="0.1"
                  min="0.1"
                  value={formData.processing_complexity_factor}
                  onChange={(e) => setFormData({ ...formData, processing_complexity_factor: Number(e.target.value) })}
                  required
                />
                <p className="text-sm text-gray-500 mt-1">
                  Factor affecting processing time (1.0 = standard, higher = more complex)
                </p>
              </div>
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    className="mr-2"
                  />
                  Active
                </label>
              </div>
              <div className="flex space-x-2">
                <Button type="submit">
                  {editingColor ? 'Update' : 'Create'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowForm(false);
                    setEditingColor(null);
                    setFormData({
                      metal_type_id: '',
                      description: '',
                      processing_complexity_factor: 1.0,
                      is_active: true,
                    });
                  }}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4">
        {filteredColors.map((color) => (
          <Card key={color.metal_colour_id}>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-lg font-semibold">{color.description}</h3>
                    <Badge variant="secondary">
                      {color.metal_type?.name || 'Unknown Metal'}
                    </Badge>
                    <Badge variant={color.is_active ? 'default' : 'secondary'}>
                      {color.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-500">
                    Processing Complexity: {color.processing_complexity_factor}x
                  </p>
                  {color.metal_type?.description && (
                    <p className="text-sm text-gray-400 mt-1">
                      {color.metal_type.description}
                    </p>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(color)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(color.metal_colour_id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredColors.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No metal colors found</p>
        </div>
      )}
    </div>
  );
}
