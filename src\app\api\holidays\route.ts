import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/db';

// GET - Fetch all holidays
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const year = searchParams.get('year');
    
    let query = supabase
      .from('holiday_calendar')
      .select('*')
      .order('holiday_date');

    // Filter by year if provided
    if (year) {
      const startDate = `${year}-01-01`;
      const endDate = `${year}-12-31`;
      query = query.gte('holiday_date', startDate).lte('holiday_date', endDate);
    }

    const { data, error } = await query;

    if (error) throw error;

    return NextResponse.json({ data: data || [] });
  } catch (error) {
    console.error('Error fetching holidays:', error);
    return NextResponse.json(
      { error: 'Failed to fetch holidays' },
      { status: 500 }
    );
  }
}

// POST - Create new holiday
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.holiday_date || !body.description) {
      return NextResponse.json(
        { error: 'holiday_date and description are required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('holiday_calendar')
      .insert([body])
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error creating holiday:', error);
    return NextResponse.json(
      { error: 'Failed to create holiday' },
      { status: 500 }
    );
  }
}

// PUT - Update holiday
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { holiday_id, ...updateData } = body;
    
    if (!holiday_id) {
      return NextResponse.json(
        { error: 'holiday_id is required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('holiday_calendar')
      .update(updateData)
      .eq('holiday_id', holiday_id)
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error updating holiday:', error);
    return NextResponse.json(
      { error: 'Failed to update holiday' },
      { status: 500 }
    );
  }
}

// DELETE - Delete holiday
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const holiday_id = searchParams.get('holiday_id');

    if (!holiday_id) {
      return NextResponse.json(
        { error: 'holiday_id is required' },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from('holiday_calendar')
      .delete()
      .eq('holiday_id', holiday_id);

    if (error) throw error;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting holiday:', error);
    return NextResponse.json(
      { error: 'Failed to delete holiday' },
      { status: 500 }
    );
  }
}
