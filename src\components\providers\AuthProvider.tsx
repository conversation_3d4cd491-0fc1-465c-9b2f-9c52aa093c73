'use client';

import { useAuthStore } from '@/store';
import { useEffect, useState } from 'react';
import { supabase } from '@/lib/db';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { setUser, setUserRole } = useAuthStore();
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Mark as hydrated on client side
    setIsHydrated(true);

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session?.user) {
        // Fetch user role from user_roles table
        supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', session.user.id)
          .single()
          .then(({ data: roleData }) => {
            setUser(session.user);
            setUserRole(roleData?.role ?? null);
          });
      } else {
        setUser(null);
        setUserRole(null);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        // Fetch user role from user_roles table
        const { data: roleData } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', session.user.id)
          .single();

        setUser(session.user);
        setUserRole(roleData?.role ?? null);
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setUserRole(null);
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [setUser, setUserRole]);

  // Prevent hydration mismatch by not rendering until client-side
  if (!isHydrated) {
    return <>{children}</>;
  }

  return <>{children}</>;
}
