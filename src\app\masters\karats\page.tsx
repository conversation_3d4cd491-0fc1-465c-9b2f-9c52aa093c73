'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2, ArrowRight } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function KaratsPage() {
  const router = useRouter();

  useEffect(() => {
    // Auto-redirect after 3 seconds
    const timer = setTimeout(() => {
      router.push('/masters/purities');
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  const handleRedirectNow = () => {
    router.push('/masters/purities');
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-gray-900">
            Page Moved
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <div className="flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          </div>

          <div className="space-y-2">
            <p className="text-gray-600">
              The <strong>Karats</strong> page has been moved to <strong>Purities</strong>
            </p>
            <p className="text-sm text-gray-500">
              This supports all metal types, not just gold
            </p>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-center space-x-2 text-blue-800">
              <span className="font-medium">Karats</span>
              <ArrowRight className="h-4 w-4" />
              <span className="font-medium">Purities</span>
            </div>
            <p className="text-xs text-blue-600 mt-1">
              Now supports Gold, Silver, Platinum, and more!
            </p>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-gray-500">
              Redirecting automatically in 3 seconds...
            </p>
            <Button
              onClick={handleRedirectNow}
              className="w-full"
            >
              Go to Purities Now
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
