export interface Process {
  id: string;
  type: string;
  complexity: number;  // 1-5 scale
  estimatedDuration: number;
  actualDuration?: number;
  status: ProcessStatus;
  workerId?: string;
  startedAt?: Date;
  completedAt?: Date;
}

export interface Worker {
  id: string;
  name: string;
  type: 'internal' | 'vendor';
  skills: WorkerSkill[];
  availability: WorkerAvailability;
  currentWorkload: number;  // Percentage 0-100
}

export interface WorkerSkill {
  processType: string;
  level: number;  // 1-5 scale
  certifiedAt: Date;
}

export interface WorkerAvailability {
  availableFrom: Date;
  availableTo: Date;
  shifts: WorkerShift[];
}

export interface WorkerShift {
  day: number;  // 0-6 (Sunday-Saturday)
  startTime: string;  // HH:mm format
  endTime: string;   // HH:mm format
}

export interface ItemType {
  id: string;
  name: string;
  category: string;
  baseComplexity: number;  // 1-5 scale
  standardProcesses: string[];  // List of required process types
}

export type ProcessStatus = 
  | 'pending'
  | 'assigned'
  | 'in_progress'
  | 'completed'
  | 'delayed'
  | 'on_hold';
