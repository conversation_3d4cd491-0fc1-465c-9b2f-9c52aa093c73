/**
 * @module components/reports/LossAnalysisDashboard
 * @description Loss Analysis Dashboard - Core business reporting for order-based loss tracking
 * 
 * BUSINESS FOCUS:
 * - Monthly loss reports by order, worker, process
 * - Order-specific loss tracking
 * - Worker performance analysis
 * - Process efficiency metrics
 * - Loss trend analysis for decision making
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  TrendingDownIcon,
  TrendingUpIcon,
  CalendarIcon,
  UserIcon,
  SettingsIcon,
  PackageIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  DownloadIcon,
  FilterIcon
} from 'lucide-react';

interface OrderLossData {
  order_id: string;
  order_no: string;
  customer_name: string;
  total_issued_weight: number;
  total_received_weight: number;
  total_loss_weight: number;
  loss_percentage: number;
  processes_completed: number;
  status: 'in_progress' | 'completed';
  last_updated: string;
}

interface WorkerLossData {
  worker_id: string;
  worker_name: string;
  orders_handled: number;
  total_loss_percentage: number;
  average_loss_percentage: number;
  best_process: string;
  worst_process: string;
  trend: 'improving' | 'declining' | 'stable';
}

interface ProcessLossData {
  process_id: string;
  process_name: string;
  expected_loss_percentage: number;
  actual_loss_percentage: number;
  variance: number;
  orders_processed: number;
  efficiency_score: number;
}

interface MonthlyLossData {
  month: string;
  total_orders: number;
  total_loss_weight: number;
  average_loss_percentage: number;
  best_performing_worker: string;
  worst_performing_process: string;
  improvement_vs_previous: number;
}

export function LossAnalysisDashboard() {
  const [selectedMonth, setSelectedMonth] = useState('2025-07');
  const [selectedWorker, setSelectedWorker] = useState('all');
  const [selectedProcess, setSelectedProcess] = useState('all');
  const [isLoading, setIsLoading] = useState(true);

  // Data states
  const [monthlyData, setMonthlyData] = useState<MonthlyLossData | null>(null);
  const [orderLossData, setOrderLossData] = useState<OrderLossData[]>([]);
  const [workerLossData, setWorkerLossData] = useState<WorkerLossData[]>([]);
  const [processLossData, setProcessLossData] = useState<ProcessLossData[]>([]);

  useEffect(() => {
    loadLossAnalysisData();
  }, [selectedMonth, selectedWorker, selectedProcess]);

  const loadLossAnalysisData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - in real implementation, these would be API calls
      setMonthlyData({
        month: selectedMonth,
        total_orders: 156,
        total_loss_weight: 45.8,
        average_loss_percentage: 2.3,
        best_performing_worker: 'John Smith',
        worst_performing_process: 'Filing',
        improvement_vs_previous: -0.2 // Negative means improvement
      });

      setOrderLossData([
        {
          order_id: '1',
          order_no: 'ORD-2025-001',
          customer_name: 'Customer A',
          total_issued_weight: 125.5,
          total_received_weight: 122.8,
          total_loss_weight: 2.7,
          loss_percentage: 2.15,
          processes_completed: 3,
          status: 'in_progress',
          last_updated: '2025-07-02T14:30:00Z'
        },
        {
          order_id: '2',
          order_no: 'ORD-2025-002',
          customer_name: 'Customer B',
          total_issued_weight: 89.2,
          total_received_weight: 86.1,
          total_loss_weight: 3.1,
          loss_percentage: 3.47,
          processes_completed: 4,
          status: 'completed',
          last_updated: '2025-07-01T16:45:00Z'
        }
      ]);

      setWorkerLossData([
        {
          worker_id: '1',
          worker_name: 'John Smith',
          orders_handled: 24,
          total_loss_percentage: 48.5,
          average_loss_percentage: 2.02,
          best_process: 'Setting',
          worst_process: 'Filing',
          trend: 'improving'
        },
        {
          worker_id: '2',
          worker_name: 'Jane Doe',
          orders_handled: 18,
          total_loss_percentage: 52.3,
          average_loss_percentage: 2.91,
          best_process: 'Polishing',
          worst_process: 'Sprue Cutting',
          trend: 'stable'
        }
      ]);

      setProcessLossData([
        {
          process_id: '1',
          process_name: 'Filing',
          expected_loss_percentage: 2.5,
          actual_loss_percentage: 2.8,
          variance: 0.3,
          orders_processed: 45,
          efficiency_score: 88
        },
        {
          process_id: '2',
          process_name: 'Setting',
          expected_loss_percentage: 1.5,
          actual_loss_percentage: 1.3,
          variance: -0.2,
          orders_processed: 38,
          efficiency_score: 113
        }
      ]);

    } catch (error) {
      console.error('Error loading loss analysis data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getLossStatusBadge = (lossPercentage: number, processType?: string) => {
    // Define acceptable loss ranges by process
    const acceptableRanges: Record<string, number> = {
      'Filing': 3.0,
      'Setting': 2.0,
      'Polishing': 2.5,
      'Sprue Cutting': 4.0
    };

    const threshold = acceptableRanges[processType || 'default'] || 2.5;

    if (lossPercentage <= threshold) {
      return <Badge className="bg-green-100 text-green-800">Acceptable</Badge>;
    } else if (lossPercentage <= threshold * 1.5) {
      return <Badge className="bg-yellow-100 text-yellow-800">High</Badge>;
    } else {
      return <Badge className="bg-red-100 text-red-800">Excessive</Badge>;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <TrendingDownIcon className="w-4 h-4 text-green-600" />;
      case 'declining':
        return <TrendingUpIcon className="w-4 h-4 text-red-600" />;
      default:
        return <div className="w-4 h-4 bg-gray-400 rounded-full" />;
    }
  };

  const exportReport = () => {
    // In real implementation, generate and download Excel/PDF report
    console.log('Exporting loss analysis report for', selectedMonth);
  };

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Loss Analysis Dashboard</h2>
          <p className="text-gray-600">
            Order-based material loss tracking and analysis
          </p>
        </div>
        <div className="flex items-center gap-4">
          <select
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(e.target.value)}
            className="w-40 h-10 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="2025-07">July 2025</option>
            <option value="2025-06">June 2025</option>
            <option value="2025-05">May 2025</option>
          </select>
          <Button onClick={exportReport} variant="outline">
            <DownloadIcon className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Monthly Summary */}
      {monthlyData && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Orders</p>
                  <p className="text-2xl font-bold">{monthlyData.total_orders}</p>
                </div>
                <PackageIcon className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Loss</p>
                  <p className="text-2xl font-bold">{monthlyData.total_loss_weight}g</p>
                </div>
                <TrendingDownIcon className="w-8 h-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Avg Loss %</p>
                  <p className="text-2xl font-bold">{monthlyData.average_loss_percentage}%</p>
                </div>
                <div className="flex items-center">
                  {monthlyData.improvement_vs_previous < 0 ? (
                    <TrendingDownIcon className="w-8 h-8 text-green-500" />
                  ) : (
                    <TrendingUpIcon className="w-8 h-8 text-red-500" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div>
                <p className="text-sm text-gray-600">Best Worker</p>
                <p className="text-lg font-bold">{monthlyData.best_performing_worker}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div>
                <p className="text-sm text-gray-600">Needs Attention</p>
                <p className="text-lg font-bold text-red-600">{monthlyData.worst_performing_process}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Analysis Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Order Loss Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PackageIcon className="w-5 h-5" />
              Order Loss Analysis
            </CardTitle>
            <CardDescription>
              Loss tracking by individual orders
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Loss %</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orderLossData.map((order) => (
                  <TableRow key={order.order_id}>
                    <TableCell className="font-medium">{order.order_no}</TableCell>
                    <TableCell>{order.customer_name}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{order.loss_percentage.toFixed(2)}%</span>
                        {getLossStatusBadge(order.loss_percentage)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={order.status === 'completed' ? 'default' : 'secondary'}>
                        {order.status}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Worker Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserIcon className="w-5 h-5" />
              Worker Performance
            </CardTitle>
            <CardDescription>
              Loss performance by worker
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Worker</TableHead>
                  <TableHead>Orders</TableHead>
                  <TableHead>Avg Loss %</TableHead>
                  <TableHead>Trend</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {workerLossData.map((worker) => (
                  <TableRow key={worker.worker_id}>
                    <TableCell className="font-medium">{worker.worker_name}</TableCell>
                    <TableCell>{worker.orders_handled}</TableCell>
                    <TableCell>
                      <span className="font-medium">{worker.average_loss_percentage.toFixed(2)}%</span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTrendIcon(worker.trend)}
                        <span className="text-sm capitalize">{worker.trend}</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Process Efficiency */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SettingsIcon className="w-5 h-5" />
            Process Efficiency Analysis
          </CardTitle>
          <CardDescription>
            Expected vs actual loss by process type
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Process</TableHead>
                <TableHead>Expected Loss %</TableHead>
                <TableHead>Actual Loss %</TableHead>
                <TableHead>Variance</TableHead>
                <TableHead>Orders Processed</TableHead>
                <TableHead>Efficiency Score</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {processLossData.map((process) => (
                <TableRow key={process.process_id}>
                  <TableCell className="font-medium">{process.process_name}</TableCell>
                  <TableCell>{process.expected_loss_percentage.toFixed(2)}%</TableCell>
                  <TableCell>{process.actual_loss_percentage.toFixed(2)}%</TableCell>
                  <TableCell>
                    <span className={process.variance > 0 ? 'text-red-600' : 'text-green-600'}>
                      {process.variance > 0 ? '+' : ''}{process.variance.toFixed(2)}%
                    </span>
                  </TableCell>
                  <TableCell>{process.orders_processed}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{process.efficiency_score}</span>
                      {process.efficiency_score >= 100 ? (
                        <CheckCircleIcon className="w-4 h-4 text-green-600" />
                      ) : (
                        <AlertTriangleIcon className="w-4 h-4 text-yellow-600" />
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
