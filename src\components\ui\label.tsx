/**
 * Label component for form fields
 *
 * A styled, accessible label component for use with form inputs.
 *
 * @param {React.LabelHTMLAttributes<HTMLLabelElement>} props - Standard label props
 * @returns {JSX.Element} The rendered label element
 *
 * @example
 * <Label htmlFor="username">Username</Label>
 */
import * as React from 'react';

export interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {}

export const Label: React.FC<LabelProps> = ({ className, ...props }) => {
  return (
    <label
      className={
        [
          'block text-sm font-medium text-gray-700',
          className
        ].filter(Boolean).join(' ')
      }
      {...props}
    />
  );
};
