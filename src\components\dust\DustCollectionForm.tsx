/**
 * @module components/dust/DustCollectionForm
 * @description Dust Collection Form Component - Create dust parcels and manage refining batches
 */

'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CircleIcon, PackageIcon, FlaskConicalIcon } from 'lucide-react';
import { 
  createDustParcel, 
  getDustParcelsForRefining, 
  createRefiningBatch,
  completeRefiningBatch 
} from '@/services/dustManagementService';

const dustCollectionSchema = z.object({
  worker_id: z.string().min(1, 'Worker is required'),
  process_id: z.string().min(1, 'Process is required'),
  customer_id: z.string().min(1, 'Customer is required'),
  weight_grams: z.number().min(0.001, 'Weight must be greater than 0'),
  purity_estimate: z.number().min(0).max(100).optional(),
  collection_notes: z.string().optional()
});

const refiningBatchSchema = z.object({
  batch_name: z.string().min(1, 'Batch name is required'),
  expected_recovery_rate: z.number().min(0).max(100),
  refinery_name: z.string().optional(),
  notes: z.string().optional()
});

interface DustCollectionFormData {
  worker_id: string;
  process_id: string;
  customer_id: string;
  weight_grams: number;
  purity_estimate?: number;
  collection_notes?: string;
}

interface RefiningBatchFormData {
  batch_name: string;
  expected_recovery_rate: number;
  refinery_name?: string;
  notes?: string;
}

interface DustParcel {
  parcel_id: string;
  worker_id: string;
  process_id: string;
  customer_id: string;
  weight_grams: number;
  purity_estimate: number;
  collection_date: string;
  status: string;
  worker?: { name: string };
  process?: { name: string };
  customer?: { description: string };
}

export function DustCollectionForm() {
  const [customers, setCustomers] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [processes, setProcesses] = useState([]);
  const [dustParcels, setDustParcels] = useState<DustParcel[]>([]);
  const [selectedParcels, setSelectedParcels] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const collectionForm = useForm<DustCollectionFormData>({
    resolver: zodResolver(dustCollectionSchema),
    defaultValues: {
      worker_id: '',
      process_id: '',
      customer_id: '',
      weight_grams: 0,
      purity_estimate: 85,
      collection_notes: ''
    }
  });

  const batchForm = useForm<RefiningBatchFormData>({
    resolver: zodResolver(refiningBatchSchema),
    defaultValues: {
      batch_name: '',
      expected_recovery_rate: 80,
      refinery_name: '',
      notes: ''
    }
  });

  // Load master data on component mount
  useEffect(() => {
    loadMasterData();
    loadDustParcels();
  }, []);

  const loadMasterData = async () => {
    try {
      setIsLoading(true);
      
      const [customersRes, workersRes, processesRes] = await Promise.all([
        fetch('/api/masters/customers'),
        fetch('/api/masters/workers'),
        fetch('/api/masters/processes')
      ]);

      const [customersData, workersData, processesData] = await Promise.all([
        customersRes.json(),
        workersRes.json(),
        processesRes.json()
      ]);

      setCustomers(customersData);
      setWorkers(workersData);
      setProcesses(processesData);
    } catch (error) {
      console.error('Error loading master data:', error);
      toast.error('Failed to load master data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadDustParcels = async () => {
    try {
      const parcels = await getDustParcelsForRefining();
      setDustParcels(parcels);
    } catch (error) {
      console.error('Error loading dust parcels:', error);
      toast.error('Failed to load dust parcels');
    }
  };

  const onSubmitCollection = async (data: DustCollectionFormData) => {
    setIsSubmitting(true);
    try {
      await createDustParcel({
        worker_id: data.worker_id,
        process_id: data.process_id,
        customer_id: data.customer_id,
        weight_grams: data.weight_grams,
        purity_estimate: data.purity_estimate,
        collection_notes: data.collection_notes
      });

      toast.success('Dust parcel created successfully');
      collectionForm.reset();
      loadDustParcels(); // Refresh the list
      
    } catch (error) {
      console.error('Error creating dust parcel:', error);
      toast.error('Failed to create dust parcel');
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSubmitBatch = async (data: RefiningBatchFormData) => {
    if (selectedParcels.length === 0) {
      toast.error('Please select at least one dust parcel for the batch');
      return;
    }

    setIsSubmitting(true);
    try {
      await createRefiningBatch(selectedParcels, {
        batch_name: data.batch_name,
        expected_recovery_rate: data.expected_recovery_rate,
        refinery_name: data.refinery_name,
        notes: data.notes
      });

      toast.success('Refining batch created successfully');
      batchForm.reset();
      setSelectedParcels([]);
      loadDustParcels(); // Refresh the list
      
    } catch (error) {
      console.error('Error creating refining batch:', error);
      toast.error('Failed to create refining batch');
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleParcelSelection = (parcelId: string) => {
    setSelectedParcels(prev => 
      prev.includes(parcelId) 
        ? prev.filter(id => id !== parcelId)
        : [...prev, parcelId]
    );
  };

  const getSelectedParcelsWeight = () => {
    return dustParcels
      .filter(parcel => selectedParcels.includes(parcel.parcel_id))
      .reduce((sum, parcel) => sum + parcel.weight_grams, 0);
  };

  const getEstimatedRecovery = () => {
    const totalWeight = getSelectedParcelsWeight();
    const recoveryRate = batchForm.watch('expected_recovery_rate') || 80;
    return (totalWeight * recoveryRate) / 100;
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="collection" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="collection" className="flex items-center gap-2">
            <CircleIcon className="w-4 h-4" />
            Dust Collection
          </TabsTrigger>
          <TabsTrigger value="refining" className="flex items-center gap-2">
            <FlaskConicalIcon className="w-4 h-4" />
            Refining Batches
          </TabsTrigger>
        </TabsList>

        <TabsContent value="collection">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CircleIcon className="w-5 h-5" />
                Create Dust Parcel
              </CardTitle>
              <CardDescription>
                Record dust collected from manufacturing processes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={collectionForm.handleSubmit(onSubmitCollection)} className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="customer_id">Customer</Label>
                    <select
                      onChange={(e) => collectionForm.setValue('customer_id', e.target.value)}
                      className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select customer</option>
                      {customers.map((customer: any) => (
                        <option key={customer.customer_id} value={customer.customer_id}>
                          {customer.description}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="worker_id">Worker</Label>
                    <select
                      onChange={(e) => collectionForm.setValue('worker_id', e.target.value)}
                      className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select worker</option>
                      {workers.map((worker: any) => (
                        <option key={worker.worker_id} value={worker.worker_id}>
                          {worker.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="process_id">Process</Label>
                    <select
                      onChange={(e) => collectionForm.setValue('process_id', e.target.value)}
                      className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select process</option>
                      {processes.map((process: any) => (
                        <option key={process.process_id} value={process.process_id}>
                          {process.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="weight_grams">Weight (grams)</Label>
                    <Input
                      type="number"
                      step="0.001"
                      min="0"
                      {...collectionForm.register('weight_grams', { valueAsNumber: true })}
                      placeholder="0.000"
                    />
                  </div>

                  <div>
                    <Label htmlFor="purity_estimate">Purity Estimate (%)</Label>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      {...collectionForm.register('purity_estimate', { valueAsNumber: true })}
                      placeholder="85"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="collection_notes">Notes (Optional)</Label>
                  <Textarea
                    {...collectionForm.register('collection_notes')}
                    placeholder="Additional notes about the dust collection"
                  />
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? 'Creating...' : 'Create Dust Parcel'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="refining">
          <div className="space-y-6">
            {/* Available Dust Parcels */}
            <Card>
              <CardHeader>
                <CardTitle>Available Dust Parcels</CardTitle>
                <CardDescription>
                  Select dust parcels to include in a refining batch
                </CardDescription>
              </CardHeader>
              <CardContent>
                {dustParcels.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">Select</TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Worker</TableHead>
                        <TableHead>Process</TableHead>
                        <TableHead>Weight (g)</TableHead>
                        <TableHead>Purity (%)</TableHead>
                        <TableHead>Collection Date</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {dustParcels.map((parcel) => (
                        <TableRow key={parcel.parcel_id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedParcels.includes(parcel.parcel_id)}
                              onCheckedChange={() => toggleParcelSelection(parcel.parcel_id)}
                            />
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {parcel.customer?.description || 'Unknown'}
                            </Badge>
                          </TableCell>
                          <TableCell>{parcel.worker?.name || 'Unknown'}</TableCell>
                          <TableCell>{parcel.process?.name || 'Unknown'}</TableCell>
                          <TableCell className="font-medium">
                            {parcel.weight_grams.toFixed(3)}
                          </TableCell>
                          <TableCell>{parcel.purity_estimate}%</TableCell>
                          <TableCell>
                            {new Date(parcel.collection_date).toLocaleDateString()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No dust parcels available for refining
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Create Refining Batch */}
            {selectedParcels.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PackageIcon className="w-5 h-5" />
                    Create Refining Batch
                  </CardTitle>
                  <CardDescription>
                    Create a batch for refining selected dust parcels
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={batchForm.handleSubmit(onSubmitBatch)} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="batch_name">Batch Name</Label>
                        <Input
                          {...batchForm.register('batch_name')}
                          placeholder="e.g., BATCH-2025-001"
                        />
                      </div>

                      <div>
                        <Label htmlFor="expected_recovery_rate">Expected Recovery Rate (%)</Label>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          {...batchForm.register('expected_recovery_rate', { valueAsNumber: true })}
                          placeholder="80"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="refinery_name">Refinery Name (Optional)</Label>
                      <Input
                        {...batchForm.register('refinery_name')}
                        placeholder="External refinery name"
                      />
                    </div>

                    <div>
                      <Label htmlFor="notes">Notes (Optional)</Label>
                      <Textarea
                        {...batchForm.register('notes')}
                        placeholder="Additional notes about the refining batch"
                      />
                    </div>

                    {/* Batch Summary */}
                    <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="text-center">
                        <div className="text-sm text-gray-500">Selected Parcels</div>
                        <div className="font-semibold">{selectedParcels.length}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-gray-500">Total Weight</div>
                        <div className="font-semibold">{getSelectedParcelsWeight().toFixed(3)}g</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-gray-500">Estimated Recovery</div>
                        <div className="font-semibold">{getEstimatedRecovery().toFixed(3)}g</div>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setSelectedParcels([])}
                      >
                        Clear Selection
                      </Button>
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? 'Creating...' : 'Create Refining Batch'}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
