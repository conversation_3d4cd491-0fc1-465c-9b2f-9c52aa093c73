import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ProductionSchedule, CapacitySlot } from '@/types/scheduling';
import { toast } from 'react-hot-toast';

export function useProductionScheduling(startDate?: string, endDate?: string) {
  const queryClient = useQueryClient();

  // Fetch production schedule
  const {
    data: schedule,
    isLoading: isLoadingSchedule,
    error: scheduleError,
  } = useQuery<ProductionSchedule[]>({
    queryKey: ['production-schedule', startDate, endDate],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (startDate) params.append('start_date', startDate);
      if (endDate) params.append('end_date', endDate);
      
      const response = await fetch(`/api/scheduling/schedule?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch production schedule');
      }
      return response.json();
    },
  });

  // Fetch capacity slots
  const {
    data: capacitySlots,
    isLoading: isLoadingCapacity,
    error: capacityError,
  } = useQuery<CapacitySlot[]>({
    queryKey: ['capacity-slots', startDate, endDate],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (startDate) params.append('start_date', startDate);
      if (endDate) params.append('end_date', endDate);
      
      const response = await fetch(`/api/scheduling/capacity?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch capacity slots');
      }
      return response.json();
    },
  });

  // Schedule order
  const scheduleOrder = useMutation({
    mutationFn: async (data: {
      order_id: string;
      process_id: string;
      worker_id: string;
      planned_start_date: string;
      planned_end_date: string;
      priority: number;
    }) => {
      const response = await fetch('/api/scheduling/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        throw new Error('Failed to schedule order');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['production-schedule'] });
      queryClient.invalidateQueries({ queryKey: ['capacity-slots'] });
      toast.success('Order scheduled successfully');
    },
    onError: (error) => {
      toast.error('Failed to schedule order');
      console.error('Error scheduling order:', error);
    },
  });

  // Update schedule
  const updateSchedule = useMutation({
    mutationFn: async (data: {
      schedule_id: string;
      updates: Partial<ProductionSchedule>;
    }) => {
      const response = await fetch(`/api/scheduling/schedule/${data.schedule_id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data.updates),
      });
      if (!response.ok) {
        throw new Error('Failed to update schedule');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['production-schedule'] });
      queryClient.invalidateQueries({ queryKey: ['capacity-slots'] });
      toast.success('Schedule updated successfully');
    },
    onError: (error) => {
      toast.error('Failed to update schedule');
      console.error('Error updating schedule:', error);
    },
  });

  return {
    schedule,
    capacitySlots,
    isLoading: isLoadingSchedule || isLoadingCapacity,
    error: scheduleError || capacityError,
    scheduleOrder,
    updateSchedule,
  };
}
