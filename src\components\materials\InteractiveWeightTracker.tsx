/**
 * @module components/materials/InteractiveWeightTracker
 * @description Interactive Weight Tracker with visual loss indicators and real-time feedback
 * 
 * UX Features:
 * - Visual weight comparison bars with animations
 * - Color-coded loss status (green/yellow/red)
 * - Process-specific loss thresholds
 * - Interactive sliders for weight input
 * - Historical comparison data
 * - Smart dust collection suggestions
 */

'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ScaleIcon, 
  TrendingUpIcon, 
  TrendingDownIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  InfoIcon,
  SparklesIcon
} from 'lucide-react';

interface WeightTrackerProps {
  issuedWeight: number;
  receivedWeight: number;
  dustCollected: number;
  processId: string;
  materialType: 'stone' | 'finding' | 'metal';
  workerId?: string;
  onWeightChange: (weight: number) => void;
  onDustChange: (dust: number) => void;
  className?: string;
}

interface LossAnalysis {
  actualLoss: number;
  expectedLoss: number;
  lossPercentage: number;
  status: 'excellent' | 'good' | 'acceptable' | 'high' | 'excessive';
  recommendations: string[];
  workerAverage?: number;
  processAverage?: number;
}

interface ProcessThresholds {
  excellent: number;
  good: number;
  acceptable: number;
  high: number;
}

export function InteractiveWeightTracker({
  issuedWeight,
  receivedWeight,
  dustCollected,
  processId,
  materialType,
  workerId,
  onWeightChange,
  onDustChange,
  className = ''
}: WeightTrackerProps) {
  const [inputMode, setInputMode] = useState<'slider' | 'input'>('slider');
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Process-specific loss thresholds (in percentage)
  const processThresholds: Record<string, ProcessThresholds> = {
    'filing': { excellent: 1.0, good: 2.0, acceptable: 3.0, high: 5.0 },
    'setting': { excellent: 0.5, good: 1.0, acceptable: 2.0, high: 3.0 },
    'polishing': { excellent: 1.5, good: 2.5, acceptable: 4.0, high: 6.0 },
    'sprue_cutting': { excellent: 2.0, good: 3.5, acceptable: 5.0, high: 7.0 },
    'default': { excellent: 1.0, good: 2.0, acceptable: 3.0, high: 5.0 }
  };

  // Calculate loss analysis
  const lossAnalysis: LossAnalysis = useMemo(() => {
    const actualLoss = issuedWeight - receivedWeight;
    const lossPercentage = issuedWeight > 0 ? (actualLoss / issuedWeight) * 100 : 0;
    const thresholds = processThresholds[processId] || processThresholds.default;
    
    let status: LossAnalysis['status'] = 'excellent';
    let recommendations: string[] = [];

    if (lossPercentage < 0) {
      status = 'excessive';
      recommendations.push('Negative loss detected - please verify weights');
    } else if (lossPercentage <= thresholds.excellent) {
      status = 'excellent';
      recommendations.push('Excellent work! Loss is well within expected range');
    } else if (lossPercentage <= thresholds.good) {
      status = 'good';
      recommendations.push('Good performance, loss is within normal range');
    } else if (lossPercentage <= thresholds.acceptable) {
      status = 'acceptable';
      recommendations.push('Loss is acceptable but could be improved');
      if (dustCollected === 0) {
        recommendations.push('Consider collecting dust to improve recovery');
      }
    } else if (lossPercentage <= thresholds.high) {
      status = 'high';
      recommendations.push('Loss is higher than expected - review process');
      recommendations.push('Ensure proper dust collection');
    } else {
      status = 'excessive';
      recommendations.push('Excessive loss detected - immediate review required');
      recommendations.push('Check for material handling issues');
    }

    return {
      actualLoss,
      expectedLoss: (issuedWeight * thresholds.good) / 100,
      lossPercentage,
      status,
      recommendations,
      workerAverage: 2.1, // TODO: Fetch from historical data
      processAverage: 2.3  // TODO: Fetch from historical data
    };
  }, [issuedWeight, receivedWeight, dustCollected, processId]);

  // Get status color
  const getStatusColor = (status: LossAnalysis['status']) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'good':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'acceptable':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'excessive':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Get status icon
  const getStatusIcon = (status: LossAnalysis['status']) => {
    switch (status) {
      case 'excellent':
      case 'good':
        return <CheckCircleIcon className="w-5 h-5" />;
      case 'acceptable':
        return <InfoIcon className="w-5 h-5" />;
      case 'high':
      case 'excessive':
        return <AlertTriangleIcon className="w-5 h-5" />;
      default:
        return <InfoIcon className="w-5 h-5" />;
    }
  };

  // Calculate suggested dust amount
  const suggestedDust = useMemo(() => {
    const loss = issuedWeight - receivedWeight;
    if (loss <= 0) return 0;
    
    // Suggest collecting 60-80% of loss as dust
    const dustPercentage = materialType === 'metal' ? 0.7 : 0.5;
    return Math.max(0, loss * dustPercentage);
  }, [issuedWeight, receivedWeight, materialType]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Weight Comparison */}
      <Card className={`border-2 ${getStatusColor(lossAnalysis.status)}`}>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ScaleIcon className="w-5 h-5" />
              <CardTitle className="text-lg">Weight Analysis</CardTitle>
              {getStatusIcon(lossAnalysis.status)}
            </div>
            <Badge className={getStatusColor(lossAnalysis.status)}>
              {lossAnalysis.status.toUpperCase()}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Visual Weight Bars */}
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">Weight Comparison</span>
              <span className="text-gray-500">
                Loss: {lossAnalysis.lossPercentage.toFixed(2)}%
              </span>
            </div>
            
            {/* Issued Weight Bar */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Issued Weight</span>
                <span className="font-medium">{issuedWeight.toFixed(3)}g</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-blue-500 h-3 rounded-full transition-all duration-500"
                  style={{ width: '100%' }}
                />
              </div>
            </div>

            {/* Received Weight Bar */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Received Weight</span>
                <span className="font-medium">{receivedWeight.toFixed(3)}g</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className={`h-3 rounded-full transition-all duration-500 ${
                    lossAnalysis.status === 'excellent' || lossAnalysis.status === 'good' 
                      ? 'bg-green-500' 
                      : lossAnalysis.status === 'acceptable' 
                      ? 'bg-yellow-500' 
                      : 'bg-red-500'
                  }`}
                  style={{ 
                    width: `${issuedWeight > 0 ? (receivedWeight / issuedWeight) * 100 : 0}%` 
                  }}
                />
              </div>
            </div>

            {/* Dust Collection Bar */}
            {dustCollected > 0 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-1">
                    <SparklesIcon className="w-3 h-3" />
                    Dust Collected
                  </span>
                  <span className="font-medium">{dustCollected.toFixed(3)}g</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-amber-500 h-2 rounded-full transition-all duration-500"
                    style={{ 
                      width: `${issuedWeight > 0 ? (dustCollected / issuedWeight) * 100 : 0}%` 
                    }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Interactive Controls */}
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Button
                variant={inputMode === 'slider' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setInputMode('slider')}
              >
                Slider
              </Button>
              <Button
                variant={inputMode === 'input' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setInputMode('input')}
              >
                Input
              </Button>
            </div>

            {/* Received Weight Control */}
            <div className="space-y-2">
              <Label htmlFor="received-weight">Received Weight (g)</Label>
              {inputMode === 'slider' ? (
                <div className="space-y-2">
                  <Slider
                    value={[receivedWeight]}
                    onValueChange={(value) => onWeightChange(value[0])}
                    max={issuedWeight * 1.1} // Allow 10% over for error correction
                    min={0}
                    step={0.001}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>0g</span>
                    <span>{receivedWeight.toFixed(3)}g</span>
                    <span>{(issuedWeight * 1.1).toFixed(3)}g</span>
                  </div>
                </div>
              ) : (
                <Input
                  id="received-weight"
                  type="number"
                  step="0.001"
                  value={receivedWeight}
                  onChange={(e) => onWeightChange(parseFloat(e.target.value) || 0)}
                  className="w-full"
                />
              )}
            </div>

            {/* Dust Collection Control */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="dust-collected">Dust Collected (g)</Label>
                {suggestedDust > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDustChange(suggestedDust)}
                    className="text-xs"
                  >
                    Use Suggested: {suggestedDust.toFixed(3)}g
                  </Button>
                )}
              </div>
              {inputMode === 'slider' ? (
                <div className="space-y-2">
                  <Slider
                    value={[dustCollected]}
                    onValueChange={(value) => onDustChange(value[0])}
                    max={Math.max(issuedWeight - receivedWeight, 1)}
                    min={0}
                    step={0.001}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>0g</span>
                    <span>{dustCollected.toFixed(3)}g</span>
                    <span>{Math.max(issuedWeight - receivedWeight, 1).toFixed(3)}g</span>
                  </div>
                </div>
              ) : (
                <Input
                  id="dust-collected"
                  type="number"
                  step="0.001"
                  value={dustCollected}
                  onChange={(e) => onDustChange(parseFloat(e.target.value) || 0)}
                  className="w-full"
                />
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Loss Analysis & Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <TrendingUpIcon className="w-5 h-5" />
            Loss Analysis & Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Key Metrics */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold">{lossAnalysis.lossPercentage.toFixed(2)}%</div>
              <div className="text-sm text-gray-600">Actual Loss</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold">{((lossAnalysis.expectedLoss / issuedWeight) * 100).toFixed(2)}%</div>
              <div className="text-sm text-gray-600">Expected Loss</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold">
                {dustCollected > 0 ? ((dustCollected / lossAnalysis.actualLoss) * 100).toFixed(0) : 0}%
              </div>
              <div className="text-sm text-gray-600">Recovery Rate</div>
            </div>
          </div>

          {/* Recommendations */}
          <div className="space-y-2">
            <h4 className="font-medium">Recommendations:</h4>
            {lossAnalysis.recommendations.map((rec, index) => (
              <Alert key={index} className="py-2">
                <InfoIcon className="w-4 h-4" />
                <AlertDescription className="text-sm">{rec}</AlertDescription>
              </Alert>
            ))}
          </div>

          {/* Historical Comparison */}
          {showAdvanced && (
            <div className="space-y-3 pt-4 border-t">
              <h4 className="font-medium">Historical Comparison:</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span className="text-sm">Worker Average:</span>
                  <span className="font-medium">{lossAnalysis.workerAverage?.toFixed(2)}%</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span className="text-sm">Process Average:</span>
                  <span className="font-medium">{lossAnalysis.processAverage?.toFixed(2)}%</span>
                </div>
              </div>
            </div>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="w-full"
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced Analysis
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
