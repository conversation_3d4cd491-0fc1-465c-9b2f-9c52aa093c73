/**
 * API Routes for Material Receipts
 * Handles CRUD operations for customer material receipts in the inventory system
 * 
 * @module api/inventory/material-receipts
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/client';
import { CustomerMaterialReceipt } from '@/types/inventory';

/**
 * GET handler for material receipts
 * Retrieves all material receipts or a specific receipt by ID
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    // First, check if the customer_material_receipt table exists
    const { data: tableInfo, error: tableError } = await supabase
      .from('customer_material_receipt')
      .select('receipt_id')
      .limit(1);

    if (tableError) {
      console.error('Error checking material receipts table:', tableError);
      return NextResponse.json({ error: 'The customer_material_receipt table may not exist or is inaccessible' }, { status: 500 });
    }

    // Now let's query with proper error handling for the join
    let query = supabase.from('customer_material_receipt');

    // First try with the safer approach - get customer data in a separate query if needed
    const { data, error } = await query
      .select('*')
      .order('receipt_date', { ascending: false });

    if (error) {
      console.error('Error fetching material receipts:', error);
      return NextResponse.json({ error: 'Failed to fetch material receipts' }, { status: 500 });
    }

    // If we need customer details, let's get them separately for each receipt
    const receiptsWithCustomerNames = await Promise.all(
      data.map(async (receipt: CustomerMaterialReceipt) => {
        if (!receipt.customer_id) {
          return {
            ...receipt,
            customer_name: 'Unknown Customer'
          };
        }

        const { data: customerData, error: customerError } = await supabase
          .from('customer_mast')
          .select('name')
          .eq('customer_id', receipt.customer_id)
          .single();

        if (customerError || !customerData) {
          console.log(`Could not fetch customer name for ID ${receipt.customer_id}:`, customerError);
          return {
            ...receipt,
            customer_name: 'Unknown Customer'
          };
        }

        return {
          ...receipt,
          customer_name: customerData.name
        };
      })
    );

    return NextResponse.json(id ? 
      receiptsWithCustomerNames.find((r: any) => r.receipt_id === id) : 
      receiptsWithCustomerNames);
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

/**
 * POST handler for material receipts
 * Creates a new material receipt
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const body = await request.json();

    // Validate required fields
    if (!body.customer_id) {
      return NextResponse.json({ error: 'Customer ID is required' }, { status: 400 });
    }

    if (!body.receipt_date) {
      return NextResponse.json({ error: 'Receipt date is required' }, { status: 400 });
    }

    // Set default values if not provided
    const receipt: Partial<CustomerMaterialReceipt> = {
      customer_id: body.customer_id,
      order_id: body.order_id || null,
      receipt_date: body.receipt_date,
      notes: body.notes || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('customer_material_receipt')
      .insert(receipt)
      .select();

    if (error) {
      console.error('Error creating material receipt:', error);
      return NextResponse.json({ error: 'Failed to create material receipt' }, { status: 500 });
    }

    return NextResponse.json(data[0], { status: 201 });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

/**
 * PUT handler for material receipts
 * Updates an existing material receipt
 */
export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const body = await request.json();

    // Validate required fields
    if (!body.receipt_id) {
      return NextResponse.json({ error: 'Receipt ID is required' }, { status: 400 });
    }

    if (!body.customer_id) {
      return NextResponse.json({ error: 'Customer ID is required' }, { status: 400 });
    }

    if (!body.receipt_date) {
      return NextResponse.json({ error: 'Receipt date is required' }, { status: 400 });
    }

    const updates = {
      customer_id: body.customer_id,
      order_id: body.order_id || null,
      receipt_date: body.receipt_date,
      notes: body.notes || null,
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('customer_material_receipt')
      .update(updates)
      .eq('receipt_id', body.receipt_id)
      .select();

    if (error) {
      console.error('Error updating material receipt:', error);
      return NextResponse.json({ error: 'Failed to update material receipt' }, { status: 500 });
    }

    return NextResponse.json(data[0]);
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

/**
 * DELETE handler for material receipts
 * Deletes a material receipt by ID
 */
export async function DELETE(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Receipt ID is required' }, { status: 400 });
    }

    // Check if there are material items associated with this receipt
    const tables = ['material_receipt_metal', 'material_receipt_diamond', 'material_receipt_stone', 'material_receipt_polki'];
    
    for (const table of tables) {
      const { data: references, error: refError } = await supabase
        .from(table)
        .select('receipt_id')
        .eq('receipt_id', id)
        .limit(1);

      if (refError) {
        console.error(`Error checking references in ${table}:`, refError);
        return NextResponse.json({ error: 'Failed to check references' }, { status: 500 });
      }

      if (references && references.length > 0) {
        return NextResponse.json(
          { error: `This receipt cannot be deleted because it has associated ${table.replace('material_receipt_', '')} items` },
          { status: 400 }
        );
      }
    }

    const { error } = await supabase
      .from('customer_material_receipt')
      .delete()
      .eq('receipt_id', id);

    if (error) {
      console.error('Error deleting material receipt:', error);
      return NextResponse.json({ error: 'Failed to delete material receipt' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
