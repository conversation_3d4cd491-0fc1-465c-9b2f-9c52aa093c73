/**
 * Toast Hook Module
 * Provides a hook interface for creating and managing toast notifications
 * 
 * @module hooks/useToast
 */

import * as React from "react";
import {
  Toast,
  ToastTitle,
  ToastDescription,
  ToastProvider,
  ToastViewport,
} from "@/components/ui/toast";

type ToastType = "default" | "destructive" | "success";

/**
 * Toast options interface
 * @interface ToastOptions
 * @property {string} title - The title of the toast notification
 * @property {string} description - The description/body of the toast notification
 * @property {ToastType} type - The type of toast (default, destructive, success)
 * @property {number} duration - The duration in milliseconds the toast should be shown
 */
interface ToastOptions {
  title: string;
  description: string;
  type?: ToastType;
  duration?: number;
}

/**
 * Toast hook interface
 * @interface ToastHook
 * @property {function} showToast - Function to create a new toast notification
 */
interface ToastHook {
  (options: ToastOptions): void;
}

/**
 * Creates toast notifications using the Radix UI toast components
 * 
 * @returns {ToastHook} A function to create toast notifications
 * 
 * @example
 * ```typescript
 * const { toast } = useToast();
 * 
 * // Show a success toast
 * toast({
 *   title: 'Success',
 *   description: 'Item saved successfully',
 *   type: 'success'
 * });
 * 
 * // Show an error toast
 * toast({
 *   title: 'Error',
 *   description: 'Failed to save item',
 *   type: 'destructive'
 * });
 * ```
 */
export const useToast = (): { showToast: ToastHook } => {
  // We'll use a state array to store active toasts
  const [toasts, setToasts] = React.useState<ToastOptions[]>([]);

  const showToast: ToastHook = React.useCallback((options: ToastOptions) => {
    const id = Date.now().toString();
    const newToast = {
      id,
      ...options,
      duration: options.duration || 5000 // Default 5 seconds
    };
    
    setToasts((prevToasts) => [...prevToasts, newToast as any]);

    // Remove the toast after duration
    setTimeout(() => {
      setToasts((prevToasts) => 
        prevToasts.filter((toast) => (toast as any).id !== id)
      );
    }, options.duration || 5000);
  }, []);

  // Create a ToastProvider component that renders all active toasts
  React.useEffect(() => {
    // This effect will render toasts into the DOM
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
      const container = document.createElement('div');
      container.id = 'toast-container';
      document.body.appendChild(container);
    }
    
    return () => {
      const container = document.getElementById('toast-container');
      if (container) {
        document.body.removeChild(container);
      }
    };
  }, []);

  return { showToast };
};

// Create a ToastContainer component for use in layout
export const ToastContainer: React.FC = () => {
  return (
    <ToastProvider>
      <ToastViewport />
    </ToastProvider>
  );
};

// Export a simple version of the hook for direct use
export default useToast;
