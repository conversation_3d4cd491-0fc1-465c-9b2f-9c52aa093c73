/**
 * Date Utilities Module
 * 
 * Provides common date formatting and manipulation functions
 * @module lib/date-utils
 */

/**
 * Format a date string or Date object into a human-readable format
 * 
 * @param {string | Date} date - The date to format
 * @param {Intl.DateTimeFormatOptions} options - Formatting options
 * @returns {string} Formatted date string
 */
export function formatDate(
  date: string | Date,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }
): string {
  if (!date) return 'N/A';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Check if date is valid
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }
  
  return new Intl.DateTimeFormat('en-US', options).format(dateObj);
}

/**
 * Format a date string or Date object into a date-only format (no time)
 * 
 * @param {string | Date} date - The date to format
 * @returns {string} Formatted date string
 */
export function formatDateOnly(date: string | Date): string {
  return formatDate(date, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

/**
 * Format a date string or Date object into a time-only format
 * 
 * @param {string | Date} date - The date to format
 * @returns {string} Formatted time string
 */
export function formatTimeOnly(date: string | Date): string {
  return formatDate(date, {
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Get the difference between two dates in days
 * 
 * @param {string | Date} date1 - First date
 * @param {string | Date} date2 - Second date
 * @returns {number} Difference in days
 */
export function getDaysDifference(date1: string | Date, date2: string | Date): number {
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  
  // Check if dates are valid
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    return 0;
  }
  
  // Convert to UTC to avoid timezone issues
  const utc1 = Date.UTC(d1.getFullYear(), d1.getMonth(), d1.getDate());
  const utc2 = Date.UTC(d2.getFullYear(), d2.getMonth(), d2.getDate());
  
  // Calculate difference in days
  return Math.floor((utc2 - utc1) / (1000 * 60 * 60 * 24));
}

/**
 * Check if a date is in the past
 * 
 * @param {string | Date} date - Date to check
 * @returns {boolean} True if date is in the past
 */
export function isDateInPast(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Check if date is valid
  if (isNaN(dateObj.getTime())) {
    return false;
  }
  
  return dateObj < new Date();
}

/**
 * Check if a date is in the future
 * 
 * @param {string | Date} date - Date to check
 * @returns {boolean} True if date is in the future
 */
export function isDateInFuture(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Check if date is valid
  if (isNaN(dateObj.getTime())) {
    return false;
  }
  
  return dateObj > new Date();
}

/**
 * Get a date that is a specified number of days from a given date
 * 
 * @param {string | Date} date - Base date
 * @param {number} days - Number of days to add (negative for days before)
 * @returns {Date} Resulting date
 */
export function addDays(date: string | Date, days: number): Date {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
  dateObj.setDate(dateObj.getDate() + days);
  return dateObj;
}
