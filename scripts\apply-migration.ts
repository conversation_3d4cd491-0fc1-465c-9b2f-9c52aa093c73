/**
 * @script apply-migration.ts
 * @description Apply database migrations to Supabase
 */

import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigrations() {
  try {
    console.log('🚀 Starting database migration...');
    
    // Get all migration files
    const migrationsDir = path.join(process.cwd(), 'supabase', 'migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Apply in chronological order

    console.log(`📁 Found ${migrationFiles.length} migration files`);

    // Create migrations tracking table if it doesn't exist
    const { error: trackingError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.schema_migrations (
          version varchar PRIMARY KEY,
          applied_at timestamptz DEFAULT CURRENT_TIMESTAMP
        );
      `
    });

    if (trackingError) {
      console.error('❌ Error creating migrations tracking table:', trackingError);
      return;
    }

    // Get already applied migrations
    const { data: appliedMigrations, error: fetchError } = await supabase
      .from('schema_migrations')
      .select('version');

    if (fetchError) {
      console.error('❌ Error fetching applied migrations:', fetchError);
      return;
    }

    const appliedVersions = new Set(appliedMigrations?.map(m => m.version) || []);

    // Apply each migration
    for (const migrationFile of migrationFiles) {
      const version = migrationFile.replace('.sql', '');
      
      if (appliedVersions.has(version)) {
        console.log(`⏭️  Skipping already applied migration: ${version}`);
        continue;
      }

      console.log(`📝 Applying migration: ${version}`);
      
      // Read migration file
      const migrationPath = path.join(migrationsDir, migrationFile);
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

      // Apply migration
      const { error: migrationError } = await supabase.rpc('exec_sql', {
        sql: migrationSQL
      });

      if (migrationError) {
        console.error(`❌ Error applying migration ${version}:`, migrationError);
        throw migrationError;
      }

      // Record migration as applied
      const { error: recordError } = await supabase
        .from('schema_migrations')
        .insert({ version });

      if (recordError) {
        console.error(`❌ Error recording migration ${version}:`, recordError);
        throw recordError;
      }

      console.log(`✅ Successfully applied migration: ${version}`);
    }

    console.log('🎉 All migrations applied successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Alternative method using direct SQL execution
async function applyMigrationsDirectSQL() {
  try {
    console.log('🚀 Starting database migration (Direct SQL)...');
    
    // Get the latest migration file
    const migrationsDir = path.join(process.cwd(), 'supabase', 'migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();

    const latestMigration = migrationFiles[migrationFiles.length - 1];
    console.log(`📝 Applying latest migration: ${latestMigration}`);
    
    // Read migration file
    const migrationPath = path.join(migrationsDir, latestMigration);
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Split SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📊 Executing ${statements.length} SQL statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          const { error } = await supabase.rpc('exec_sql', {
            sql: statement + ';'
          });

          if (error) {
            console.error(`❌ Error in statement ${i + 1}:`, error);
            console.error(`Statement: ${statement.substring(0, 100)}...`);
            throw error;
          }

          console.log(`✅ Statement ${i + 1}/${statements.length} executed`);
        } catch (err) {
          console.error(`❌ Failed to execute statement ${i + 1}:`, err);
          throw err;
        }
      }
    }

    console.log('🎉 Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Check if we can use RPC function, otherwise use direct approach
async function main() {
  try {
    // Test if we can use RPC
    const { error: rpcTest } = await supabase.rpc('exec_sql', {
      sql: 'SELECT 1 as test;'
    });

    if (rpcTest) {
      console.log('⚠️  RPC not available, using alternative approach...');
      await applyMigrationsDirectSQL();
    } else {
      await applyMigrations();
    }
  } catch (error) {
    console.error('❌ Migration process failed:', error);
    process.exit(1);
  }
}

// Run the migration
main();
