'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import {
  Save,
  ArrowLeft,
  Image as ImageIcon,
  Loader2,
  Check,
  X,
} from 'lucide-react';
import { Order } from '@/types/common';
import { useMasterData } from '@/hooks/useMasterData';
import {
  ItemTypeMaster,
  PurityMaster,
  MetalColorMaster,
  CustomerMaster,
  OrderCategoryMaster, 
  StyleMaster 
} from '@/types/masters';
import { supabase } from '@/lib/db';

export default function OrderPage() {
  const params = useParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load master data
  const { data: itemTypes } = useMasterData<ItemTypeMaster>({ 
    tableName: 'item_type_mast',
    idField: 'item_type_id'
  });
  const { data: purities } = useMasterData<PurityMaster>({
    tableName: 'purity_mast',
    idField: 'purity_id'
  });
  const { data: metalColors } = useMasterData<MetalColorMaster>({
    tableName: 'metal_colour_mast',
    idField: 'metal_colour_id'
  });
  const { data: customers } = useMasterData<CustomerMaster>({ 
    tableName: 'customer_mast',
    idField: 'customer_id'
  });
  const { data: orderCategories } = useMasterData<OrderCategoryMaster>({ 
    tableName: 'order_category_mast',
    idField: 'order_category_id'
  });
  const { data: styles } = useMasterData<StyleMaster>({ 
    tableName: 'styles_mast',
    idField: 'style_id'
  });

  const [order, setOrder] = useState<Partial<Order>>({
    metal_received: false,
    diamonds_received: false,
    polki_received: false,
  });

  // Fetch order data
  useEffect(() => {
    const fetchOrder = async () => {
      if (!params.id || params.id === 'new') {
        setIsLoading(false);
        return;
      }

      try {
        const { data: order, error } = await supabase
          .from('orders')
          .select(`
            *,
            customer:customer_id(description),
            third_party_customer:third_party_cust_id(description),
            item_type:item_type_id(description),
            karat:karat_id(description),
            metal_colour:metal_colour_id(description),
            order_category:order_category_id(description)
          `)
          .eq('order_id', params.id)
          .single();

        if (error) throw error;
        
        if (order) {
          setOrder(order);
        } else {
          router.push('/orders'); // Redirect if order not found
        }
      } catch (error) {
        console.error('Error fetching order:', error);
        // Handle error (show message, redirect, etc.)
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrder();
  }, [params.id, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Order Details: {order.order_id}
          </h1>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-8">
          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Order ID</dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-white">{order.order_id}</dd>
          </div>

          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Customer</dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
              {order.customer?.description || 'N/A'}
            </dd>
          </div>

          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Third Party Customer</dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
              {order.third_party_customer?.description || 'N/A'}
            </dd>
          </div>

          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Issue Date</dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
              {order.issue_date ? new Date(order.issue_date).toLocaleDateString() : 'N/A'}
            </dd>
          </div>

          {/* Removed estimated_completion_date display as it's not in the DB schema */}
          {/* <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Expected Delivery Date</dt> 
            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
              {order.estimated_completion_date ? new Date(order.estimated_completion_date).toLocaleDateString() : 'N/A'}
            </dd>
          </div> */}

          {/* Displaying actual expected_delivery_date from schema */}
          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Expected Delivery Date</dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
              {order.expected_delivery_date ? new Date(order.expected_delivery_date).toLocaleDateString() : 'N/A'}
            </dd>
          </div>


          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
            <dd className="mt-1">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                order.order_status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                order.order_status === 'COMPLETED' ? 'bg-blue-100 text-blue-800' :
                order.order_status === 'CANCELLED' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {order.order_status ? (order.order_status.charAt(0).toUpperCase() + order.order_status.slice(1).toLowerCase()) : 'N/A'} 
              </span>
            </dd>
          </div>

          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Item Type</dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
              {order.item_type?.description || 'N/A'}
            </dd>
          </div>

          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Karat</dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
              {order.karat?.description || 'N/A'}
            </dd>
          </div>

          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Metal Color</dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
              {order.metal_colour?.description || 'N/A'}
            </dd>
          </div>

          <div className="sm:col-span-2">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Remarks</dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
              {order.remarks || 'N/A'}
            </dd>
          </div>
        </dl>
      </div>
    </div>
  );
}
