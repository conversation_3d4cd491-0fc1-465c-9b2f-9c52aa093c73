/**
 * Edit Material Receipt Page
 * Page for editing existing customer material receipts
 * 
 * @module app/inventory/material-receipt/[id]/edit
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { CustomerMaterialReceiptForm } from '@/components/inventory/receipt/CustomerMaterialReceiptForm';
import { CustomerMaterialReceipt } from '@/types/inventory';
import { useToast } from '@/hooks/useToast';

/**
 * Edit Material Receipt Page Component
 * Provides interface for editing existing material receipts
 */
export default function EditMaterialReceiptPage() {
  const [receipt, setReceipt] = useState<CustomerMaterialReceipt | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const params = useParams();
  const router = useRouter();
  const { showToast } = useToast();
  const receiptId = params?.id as string;

  useEffect(() => {
    if (receiptId) {
      fetchReceipt(receiptId);
    }
  }, [receiptId]);

  /**
   * Fetches the material receipt by ID
   * @param id - The receipt ID to fetch
   */
  const fetchReceipt = async (id: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/inventory/material-receipts?id=${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch material receipt');
      }
      
      const data = await response.json();
      
      if (!data) {
        throw new Error('Material receipt not found');
      }
      
      setReceipt(data);
      setError(null);
    } catch (err) {
      setError('Error loading material receipt. It may have been deleted or does not exist.');
      console.error('Error fetching material receipt:', err);
      showToast({
        title: 'Error loading material receipt',
        description: '', 
        type: 'destructive' 
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error || !receipt) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
          <p>{error || 'Material receipt not found'}</p>
        </div>
        <button
          onClick={() => router.push('/inventory/material-receipt')}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Back to Material Receipts
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Edit Material Receipt</h1>
      <CustomerMaterialReceiptForm initialData={receipt} />
    </div>
  );
}
