/**
 * @module types/stone-inventory
 * @description TypeScript types for stone inventory management, polki findings, and material transactions
 */

import { BaseEntity } from './common';

// =====================================================
// STONE QUALITY TYPES
// =====================================================

export interface StoneQuality extends BaseEntity {
  quality_id: string;
  name: string;
  description?: string;
  grade_order?: number; // 1=highest, 5=lowest
  is_active: boolean;
}

// =====================================================
// STONE INVENTORY TYPES
// =====================================================

export type StoneLocation = 'Customers' | 'Safe' | 'Central' | 'External Vendors' | 'Floor';
export type StoneStatus = 'available' | 'allocated' | 'issued' | 'consumed' | 'returned' | 'damaged' | 'lost';

export interface StoneInventory extends BaseEntity {
  inventory_id: string;
  customer_id: string;
  order_id?: string; // Can be allocated to specific order
  stone_type_id: string;
  stone_shape_id: string;
  stone_size_id: string;
  stone_quality_id: string;
  quantity: number;
  total_carat_weight: number;
  location: StoneLocation;
  status: StoneStatus;
  notes?: string;
  
  // Relations (populated when needed)
  customer?: { customer_id: string; description: string };
  order?: { order_id: string; order_reference_no: string };
  stone_type?: { stone_type_id: string; name: string };
  stone_shape?: { shape_id: string; name: string };
  stone_size?: { size_id: string; size_description: string };
  stone_quality?: { quality_id: string; name: string };
}

// =====================================================
// POLKI FINDINGS TYPES
// =====================================================

export type FindingStatus = 'available' | 'allocated' | 'issued' | 'consumed' | 'returned';

export interface Finding extends BaseEntity {
  finding_id: string;
  customer_id: string;
  order_id?: string;
  finding_type: string; // Default: 'Polki Assembly'
  description: string;
  gross_weight_grams: number; // Including foil
  location: StoneLocation;
  status: FindingStatus;
  notes?: string;
  
  // Relations
  customer?: { customer_id: string; description: string };
  order?: { order_id: string; order_reference_no: string };
  stone_details?: FindingStoneDetail[];
}

export interface FindingStoneDetail extends BaseEntity {
  detail_id: string;
  finding_id: string;
  stone_type_id: string;
  stone_shape_id: string;
  stone_size_id: string;
  pieces: number;
  carat_weight: number; // Stone weight only, excluding foil
  notes?: string;
  
  // Relations
  stone_type?: { stone_type_id: string; name: string };
  stone_shape?: { shape_id: string; name: string };
  stone_size?: { size_id: string; size_description: string };
}

// =====================================================
// MATERIAL TRANSACTION TYPES
// =====================================================

export type TransactionType = 'issue' | 'receipt';

export interface MaterialTransaction extends BaseEntity {
  transaction_id: string;
  order_id: string;
  process_id: string;
  worker_id?: string;
  transaction_type: TransactionType;
  transaction_date: string;
  gross_weight_before?: number;
  gross_weight_after?: number;
  net_weight_before?: number;
  net_weight_after?: number;
  notes?: string;
  created_by?: string;
  
  // Relations
  order?: { order_id: string; order_reference_no: string };
  process?: { process_id: string; name: string };
  worker?: { worker_id: string; name: string };
  stone_details?: StoneTransactionDetail[];
  finding_details?: FindingTransactionDetail[];
}

export interface StoneTransactionDetail extends BaseEntity {
  detail_id: string;
  transaction_id: string;
  stone_inventory_id?: string;
  stone_type_id: string;
  stone_shape_id: string;
  stone_size_id: string;
  quantity_issued: number;
  quantity_returned: number;
  quantity_consumed: number;
  quantity_damaged: number;
  quantity_lost: number;
  carat_weight_issued: number;
  carat_weight_returned: number;
  notes?: string;
  
  // Relations
  stone_inventory?: StoneInventory;
  stone_type?: { stone_type_id: string; name: string };
  stone_shape?: { shape_id: string; name: string };
  stone_size?: { size_id: string; size_description: string };
}

export interface FindingTransactionDetail extends BaseEntity {
  detail_id: string;
  transaction_id: string;
  finding_id?: string;
  quantity_issued: number;
  quantity_returned: number;
  weight_issued_grams: number;
  weight_returned_grams: number;
  notes?: string;

  // Relations
  finding?: Finding;
}

// =====================================================
// DUST MANAGEMENT TYPES
// =====================================================

export type DustType = 'filing' | 'setting' | 'polish' | 'bob' | 'mixed';
export type DustStatus = 'collected' | 'batched' | 'refined';

export interface DustParcel extends BaseEntity {
  parcel_id: string;
  parcel_number: string; // Sequential: 0001, 0002, etc.
  transaction_id?: string;
  worker_id?: string;
  process_id?: string;
  dust_type: DustType;
  weight_grams: number;
  estimated_recovery_pct: number;
  actual_recovery_pct?: number; // Set after refining
  refine_batch_id?: string;
  status: DustStatus;
  collection_date: string;
  notes?: string;

  // Relations
  transaction?: MaterialTransaction;
  worker?: { worker_id: string; name: string };
  process?: { process_id: string; name: string };
  refine_batch?: { batch_id: string; refine_date: string };
}

// =====================================================
// STYLE CODE MANAGEMENT TYPES
// =====================================================

export interface StyleCodeInfo {
  style_id: string;
  style_code?: string;
  is_repeat_style: boolean;
  parent_style_id?: string;
  complexity_level?: number; // 1-5
  design_notes?: string;
  cad_required: boolean;
  cam_required: boolean;

  // Relations
  parent_style?: { style_id: string; style_code: string };
}

export interface OrderStyleInfo {
  order_id: string;
  style_code?: string;
  complexity_level?: number;
  is_repeat_order: boolean;
  reference_order_id?: string;

  // Relations
  reference_order?: { order_id: string; order_reference_no: string };
  style?: StyleCodeInfo;
}

// =====================================================
// FORM DATA TYPES FOR UI
// =====================================================

export interface StoneInventoryForm {
  customer_id: string;
  order_id?: string;
  stone_type_id: string;
  stone_shape_id: string;
  stone_size_id: string;
  stone_quality_id: string;
  quantity: number;
  total_carat_weight: number;
  location: StoneLocation;
  notes?: string;
}

export interface FindingForm {
  customer_id: string;
  order_id?: string;
  finding_type: string;
  description: string;
  gross_weight_grams: number;
  location: StoneLocation;
  notes?: string;
  stone_details: FindingStoneDetailForm[];
}

export interface FindingStoneDetailForm {
  stone_type_id: string;
  stone_shape_id: string;
  stone_size_id: string;
  pieces: number;
  carat_weight: number;
  notes?: string;
}

export interface MaterialIssueForm {
  order_id: string;
  process_id: string;
  worker_id?: string;
  gross_weight_before?: number;
  net_weight_before?: number;
  notes?: string;
  stone_details: StoneIssueDetailForm[];
  finding_details: FindingIssueDetailForm[];
}

export interface StoneIssueDetailForm {
  stone_inventory_id?: string;
  stone_type_id: string;
  stone_shape_id: string;
  stone_size_id: string;
  quantity_issued: number;
  carat_weight_issued: number;
  notes?: string;
}

export interface FindingIssueDetailForm {
  finding_id?: string;
  quantity_issued: number;
  weight_issued_grams: number;
  notes?: string;
}

export interface MaterialReceiptForm {
  transaction_id: string; // Reference to the original issue transaction
  gross_weight_after?: number;
  net_weight_after?: number;
  notes?: string;
  stone_details: StoneReceiptDetailForm[];
  finding_details: FindingReceiptDetailForm[];
  dust_parcels: DustParcelForm[];
}

export interface StoneReceiptDetailForm {
  detail_id: string; // Reference to the original issue detail
  quantity_returned: number;
  quantity_consumed: number;
  quantity_damaged: number;
  quantity_lost: number;
  carat_weight_returned: number;
  notes?: string;
}

export interface FindingReceiptDetailForm {
  detail_id: string; // Reference to the original issue detail
  quantity_returned: number;
  weight_returned_grams: number;
  notes?: string;
}

export interface DustParcelForm {
  dust_type: DustType;
  weight_grams: number;
  estimated_recovery_pct: number;
  notes?: string;
}
