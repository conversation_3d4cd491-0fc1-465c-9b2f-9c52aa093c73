import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/db';

// GET - Fetch all processes
export async function GET() {
  try {
    const { data, error } = await supabase
      .from('process_mast')
      .select('*')
      .eq('is_active', true)
      .order('description');

    if (error) throw error;

    return NextResponse.json({ data: data || [] });
  } catch (error) {
    console.error('Error fetching processes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch processes' },
      { status: 500 }
    );
  }
}

// POST - Create new process
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const { data, error } = await supabase
      .from('process_mast')
      .insert([body])
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error creating process:', error);
    return NextResponse.json(
      { error: 'Failed to create process' },
      { status: 500 }
    );
  }
}

// PUT - Update process
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { process_id, ...updateData } = body;
    
    if (!process_id) {
      return NextResponse.json(
        { error: 'process_id is required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('process_mast')
      .update(updateData)
      .eq('process_id', process_id)
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error updating process:', error);
    return NextResponse.json(
      { error: 'Failed to update process' },
      { status: 500 }
    );
  }
}

// DELETE - Delete process
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const process_id = searchParams.get('process_id');

    if (!process_id) {
      return NextResponse.json(
        { error: 'process_id is required' },
        { status: 400 }
      );
    }

    // Soft delete by setting is_active to false
    const { error } = await supabase
      .from('process_mast')
      .update({ is_active: false })
      .eq('process_id', process_id);

    if (error) throw error;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting process:', error);
    return NextResponse.json(
      { error: 'Failed to delete process' },
      { status: 500 }
    );
  }
}
