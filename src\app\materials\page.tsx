/**
 * Materials Dashboard Page
 * Central hub for all material operations with quick actions and status overview
 * 
 * @module app/materials
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  PlusIcon, 
  ClipboardCheckIcon, 
  ListChecksIcon, 
  CircleIcon, 
  WorkflowIcon,
  AlertTriangleIcon,
  TrendingUpIcon,
  PackageIcon,
  ScaleIcon
} from 'lucide-react';

interface MaterialStats {
  pending_issues: number;
  pending_receipts: number;
  dust_parcels_ready: number;
  high_loss_alerts: number;
  total_materials_in_process: number;
}

interface RecentActivity {
  id: string;
  type: 'issue' | 'receipt' | 'dust_collection';
  description: string;
  timestamp: string;
  status: 'completed' | 'pending' | 'alert';
}

export default function MaterialsPage() {
  const [stats, setStats] = useState<MaterialStats>({
    pending_issues: 0,
    pending_receipts: 0,
    dust_parcels_ready: 0,
    high_loss_alerts: 0,
    total_materials_in_process: 0
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // In a real implementation, these would be API calls
      // For now, using mock data
      setStats({
        pending_issues: 12,
        pending_receipts: 8,
        dust_parcels_ready: 15,
        high_loss_alerts: 3,
        total_materials_in_process: 45
      });

      setRecentActivity([
        {
          id: '1',
          type: 'issue',
          description: 'Issued 25.5g gold to Worker A for Setting process',
          timestamp: '2 hours ago',
          status: 'completed'
        },
        {
          id: '2',
          type: 'receipt',
          description: 'Received materials from Worker B - High loss detected',
          timestamp: '3 hours ago',
          status: 'alert'
        },
        {
          id: '3',
          type: 'dust_collection',
          description: 'Created dust parcel - 2.3g from Filing process',
          timestamp: '4 hours ago',
          status: 'completed'
        }
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const quickActions = [
    {
      title: 'Loss Analysis',
      description: 'Monthly loss reports by order, worker, process',
      href: '/reports/loss-analysis',
      icon: <TrendingUpIcon className="w-6 h-6" />,
      color: 'bg-gradient-to-r from-red-500 to-orange-500',
      badge: 'Core Business'
    },
    {
      title: 'Issue Materials',
      description: 'Issue stones, findings, or metal to workers',
      href: '/materials/issue',
      icon: <PlusIcon className="w-6 h-6" />,
      color: 'bg-blue-500'
    },
    {
      title: 'Universal Receipt',
      description: 'Smart receipt for all material types in one form',
      href: '/materials/universal-receipt',
      icon: <ClipboardCheckIcon className="w-6 h-6" />,
      color: 'bg-gradient-to-r from-green-500 to-emerald-500',
      badge: 'Enhanced'
    },
    {
      title: 'Standard Receipt',
      description: 'Traditional separate forms for each material type',
      href: '/materials/receipt',
      icon: <ListChecksIcon className="w-6 h-6" />,
      color: 'bg-green-500'
    },
    {
      title: 'Dust Management',
      description: 'Manage dust collection and refining',
      href: '/materials/dust',
      icon: <CircleIcon className="w-6 h-6" />,
      color: 'bg-yellow-500'
    },
    {
      title: 'Location Transfers',
      description: 'Transfer materials between locations',
      href: '/materials/transfers',
      icon: <WorkflowIcon className="w-6 h-6" />,
      color: 'bg-purple-500'
    },
    {
      title: 'Flow Dashboard',
      description: 'Real-time overview and management dashboard',
      href: '/materials/dashboard',
      icon: <TrendingUpIcon className="w-6 h-6" />,
      color: 'bg-gradient-to-r from-indigo-500 to-purple-500',
      badge: 'Live'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      case 'alert':
        return <Badge variant="destructive">Alert</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'issue':
        return <PlusIcon className="w-4 h-4" />;
      case 'receipt':
        return <ClipboardCheckIcon className="w-4 h-4" />;
      case 'dust_collection':
        return <CircleIcon className="w-4 h-4" />;
      default:
        return <PackageIcon className="w-4 h-4" />;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Materials Management</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Central hub for all material operations and tracking
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        {quickActions.map((action, index) => (
          <Link key={index} href={action.href}>
            <Card className="hover:shadow-lg transition-shadow cursor-pointer relative">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-lg ${action.color} text-white`}>
                    {action.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{action.title}</h3>
                      {action.badge && (
                        <Badge className="bg-green-100 text-green-800 text-xs">
                          {action.badge}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {action.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Pending Issues</p>
                <p className="text-2xl font-bold">{stats.pending_issues}</p>
              </div>
              <PlusIcon className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Pending Receipts</p>
                <p className="text-2xl font-bold">{stats.pending_receipts}</p>
              </div>
              <ClipboardCheckIcon className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Dust Parcels</p>
                <p className="text-2xl font-bold">{stats.dust_parcels_ready}</p>
              </div>
              <CircleIcon className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Loss Alerts</p>
                <p className="text-2xl font-bold text-red-600">{stats.high_loss_alerts}</p>
              </div>
              <AlertTriangleIcon className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">In Process</p>
                <p className="text-2xl font-bold">{stats.total_materials_in_process}</p>
              </div>
              <ScaleIcon className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ListChecksIcon className="w-5 h-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest material operations and updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                  <div className="flex-shrink-0 mt-1">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">{activity.description}</p>
                    <p className="text-xs text-gray-500">{activity.timestamp}</p>
                  </div>
                  <div className="flex-shrink-0">
                    {getStatusBadge(activity.status)}
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Link href="/materials/status">
                <Button variant="outline" className="w-full">
                  View All Activity
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Quick Status Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUpIcon className="w-5 h-5" />
              Status Overview
            </CardTitle>
            <CardDescription>
              Current material operation status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                <div className="flex items-center space-x-2">
                  <PlusIcon className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">Materials Issued Today</span>
                </div>
                <Badge variant="secondary">24 items</Badge>
              </div>

              <div className="flex items-center justify-between p-3 rounded-lg bg-green-50 dark:bg-green-900/20">
                <div className="flex items-center space-x-2">
                  <ClipboardCheckIcon className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium">Materials Received Today</span>
                </div>
                <Badge variant="secondary">18 items</Badge>
              </div>

              <div className="flex items-center justify-between p-3 rounded-lg bg-yellow-50 dark:bg-yellow-900/20">
                <div className="flex items-center space-x-2">
                  <CircleIcon className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm font-medium">Dust Collected Today</span>
                </div>
                <Badge variant="secondary">12.5g</Badge>
              </div>

              <div className="flex items-center justify-between p-3 rounded-lg bg-red-50 dark:bg-red-900/20">
                <div className="flex items-center space-x-2">
                  <AlertTriangleIcon className="w-4 h-4 text-red-600" />
                  <span className="text-sm font-medium">High Loss Transactions</span>
                </div>
                <Badge variant="destructive">3 alerts</Badge>
              </div>
            </div>

            <div className="mt-4 space-y-2">
              <Link href="/materials/status">
                <Button variant="outline" className="w-full">
                  View Detailed Status
                </Button>
              </Link>
              <Link href="/reports/loss">
                <Button variant="outline" className="w-full">
                  Loss Analysis Report
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
