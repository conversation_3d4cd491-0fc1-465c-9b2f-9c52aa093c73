'use client';

import React from 'react';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/data-display/table';
import { Progress } from '@/components/feedback/progress';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/data-display/card';
import { useProductionScheduling } from '@/hooks/useProductionScheduling';

interface CapacityPlanningProps {
  startDate: Date;
  endDate: Date;
}

export function CapacityPlanning({
  startDate,
  endDate,
}: CapacityPlanningProps) {
  const { capacitySlots, isLoading } = useProductionScheduling(
    startDate.toISOString(),
    endDate.toISOString()
  );

  const getUtilizationColor = (utilization: number) => {
    if (utilization > 90) return 'text-destructive';
    if (utilization > 75) return 'text-warning';
    return 'text-primary';
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Group capacity slots by process
  const processSummary = capacitySlots?.reduce((acc, slot) => {
    if (!acc[slot.process_id]) {
      acc[slot.process_id] = {
        totalCapacity: 0,
        allocatedCapacity: 0,
        dailyUtilization: {},
      };
    }
    
    acc[slot.process_id].totalCapacity += slot.total_capacity_hours;
    acc[slot.process_id].allocatedCapacity += slot.allocated_hours;
    acc[slot.process_id].dailyUtilization[slot.date] = {
      total: slot.total_capacity_hours,
      allocated: slot.allocated_hours,
      workers: slot.worker_allocations,
    };
    
    return acc;
  }, {} as Record<string, {
    totalCapacity: number;
    allocatedCapacity: number;
    dailyUtilization: Record<string, {
      total: number;
      allocated: number;
      workers: {
        worker_id: string;
        allocated_hours: number;
        available_hours: number;
      }[];
    }>;
  }>);

  return (
    <div className="space-y-8">
      {/* Overall Capacity Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {Object.entries(processSummary || {}).map(([processId, summary]) => {
          const utilization = (summary.allocatedCapacity / summary.totalCapacity) * 100;
          return (
            <Card key={processId}>
              <CardHeader>
                <CardTitle>Process {processId}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Total Capacity:</span>
                    <span>{summary.totalCapacity.toFixed(1)} hours</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Allocated:</span>
                    <span>{summary.allocatedCapacity.toFixed(1)} hours</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Utilization:</span>
                    <span className={getUtilizationColor(utilization)}>
                      {utilization.toFixed(1)}%
                    </span>
                  </div>
                  <Progress value={utilization} />
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Daily Capacity Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Daily Capacity Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Process</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Total Capacity</TableHead>
                <TableHead>Allocated</TableHead>
                <TableHead>Available</TableHead>
                <TableHead>Utilization</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {capacitySlots?.map((slot) => {
                const utilization = (slot.allocated_hours / slot.total_capacity_hours) * 100;
                return (
                  <TableRow key={`${slot.process_id}-${slot.date}`}>
                    <TableCell>Process {slot.process_id}</TableCell>
                    <TableCell>{format(new Date(slot.date), 'MMM d, yyyy')}</TableCell>
                    <TableCell>{slot.total_capacity_hours.toFixed(1)} hrs</TableCell>
                    <TableCell>{slot.allocated_hours.toFixed(1)} hrs</TableCell>
                    <TableCell>{slot.available_hours.toFixed(1)} hrs</TableCell>
                    <TableCell>
                      <div className="w-full">
                        <div className="flex justify-between mb-1">
                          <span className={getUtilizationColor(utilization)}>
                            {utilization.toFixed(1)}%
                          </span>
                        </div>
                        <Progress value={utilization} />
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
