'use client';

import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { MasterTable } from '@/components/common/Table/MasterTable';
import { MasterForm } from '@/components/common/Form/MasterForm';
import { useMasterData } from '@/hooks/useMasterData';
import { MASTER_CONFIGS } from '@/config/constants';
import type { OrderCategoryMaster } from '@/types/masters';

export default function OrderCategoriesPage() {
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<OrderCategoryMaster | null>(null);
  const { data, loading, error, add, update, remove } = useMasterData<OrderCategoryMaster>({
    tableName: 'order_category_mast',
  });

  const handleSubmit = async (values: Partial<OrderCategoryMaster>) => {
    try {
      if (editingItem) {
        await update(editingItem.order_category_id, values);
      } else {
        await add(values);
      }
      setShowForm(false);
      setEditingItem(null);
    } catch (error) {
      console.error('Failed to save order category:', error);
      alert('Failed to save order category. Please try again.');
    }
  };

  const handleEdit = (item: OrderCategoryMaster) => {
    setEditingItem(item);
    setShowForm(true);
  };

  const handleDelete = async (item: OrderCategoryMaster) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      try {
        await remove(item.order_category_id);
      } catch (error) {
        console.error('Failed to delete order category:', error);
        alert('Failed to delete order category. Please try again.');
      }
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          {MASTER_CONFIGS.orderCategory.title}
        </h1>
        <button
          onClick={() => setShowForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <MasterTable
          data={data}
          config={MASTER_CONFIGS.orderCategory}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      </div>

      {showForm && (
        <MasterForm
          config={MASTER_CONFIGS.orderCategory}
          onSubmit={handleSubmit}
          onCancel={() => {
            setShowForm(false);
            setEditingItem(null);
          }}
          initialData={editingItem}
        />
      )}
    </div>
  );
}
