import { BaseRepository } from './BaseRepository';
import { WorkshopConfig as WorkshopConfigDB } from '@/types/database';
import { NotFoundError } from '../errors/DatabaseError';

type WorkshopConfig = Omit<WorkshopConfigDB, 'config_id'> & { id: string };

export class WorkshopConfigRepository extends BaseRepository<WorkshopConfig> {
  protected tableName = 'workshop_config';
  protected entityName = 'WorkshopConfig';

  async getActiveConfig(): Promise<WorkshopConfig> {
    const result = await this.executeQuery<WorkshopConfig>(async () => { // Added async/await and return object
      const response = await this.supabase
        .from(this.tableName)
        .select('*')
        .eq('is_active', true)
        .single();
      return { data: response.data, error: response.error };
    });

    if (!result) {
      throw new NotFoundError('Active Workshop Configuration', 'N/A');
    }

    return result;
  }

  async updateConfig(
    configId: string,
    updates: Partial<WorkshopConfig>
  ): Promise<WorkshopConfig> {
    return this.executeQuery<WorkshopConfig>(async () => { // Added async/await and return object
      const response = await this.supabase
        .from(this.tableName)
        .update(updates)
        .eq('config_id', configId)
        .select()
        .single();
      return { data: response.data, error: response.error };
    });
  }

  async createConfig(config: Omit<WorkshopConfig, 'config_id'>): Promise<WorkshopConfig> {
    // Deactivate current active config if exists
    await this.supabase
      .from(this.tableName)
      .update({ is_active: false })
      .eq('is_active', true);

    // Create new active config
    return this.executeQuery<WorkshopConfig>(async () => { // Added async/await and return object
      const response = await this.supabase
        .from(this.tableName)
        .insert({ ...config, is_active: true })
        .select()
        .single();
      return { data: response.data, error: response.error };
    });
  }
}
