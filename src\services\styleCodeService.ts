/**
 * Style Code Service
 * 
 * Handles style code management for new vs repeat orders
 * Implements logic for automatic style code assignment and repeat order detection
 */

import { supabase } from '@/lib/db';
import { StyleMaster } from '@/types/masters';
import { Order } from '@/types/orders';

export interface StyleCodeSearchCriteria {
  item_type_id: string;
  customer_id: string;
  karat_id: string;
  gold_colour_id: string;
  diamond_quality?: string;
  polki_quality?: string;
  // Add more criteria as needed for matching
}

export interface StyleCodeMatch {
  style_id: string;
  style_code: string;
  order_id: string;
  order_reference_no: string;
  similarity_score: number; // 0-100, how closely it matches
  differences: string[]; // What's different from the search criteria
}

/**
 * Search for existing style codes that match the given criteria
 * Used to detect if an order is a repeat of an existing style
 */
export async function searchExistingStyleCodes(
  criteria: StyleCodeSearchCriteria
): Promise<StyleCodeMatch[]> {
  try {
    const { data: orders, error } = await supabase
      .from('orders')
      .select(`
        order_id,
        order_reference_no,
        style_code,
        item_type_id,
        karat_id,
        gold_colour_id,
        diamond_quality,
        polki_quality,
        customer_id,
        styles_mast!inner(
          style_id,
          style_code,
          complexity_level,
          design_notes
        )
      `)
      .eq('item_type_id', criteria.item_type_id)
      .eq('customer_id', criteria.customer_id)
      .not('style_code', 'is', null)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Calculate similarity scores and filter matches
    const matches: StyleCodeMatch[] = orders
      .map(order => {
        let score = 70; // Base score for matching item type and customer
        const differences: string[] = [];

        // Check karat match
        if (order.karat_id === criteria.karat_id) {
          score += 15;
        } else {
          differences.push('Different karat');
        }

        // Check gold color match
        if (order.gold_colour_id === criteria.gold_colour_id) {
          score += 10;
        } else {
          differences.push('Different gold color');
        }

        // Check diamond quality match
        if (criteria.diamond_quality && order.diamond_quality) {
          if (order.diamond_quality === criteria.diamond_quality) {
            score += 3;
          } else {
            differences.push('Different diamond quality');
          }
        }

        // Check polki quality match
        if (criteria.polki_quality && order.polki_quality) {
          if (order.polki_quality === criteria.polki_quality) {
            score += 2;
          } else {
            differences.push('Different polki quality');
          }
        }

        const styleData = Array.isArray(order.styles_mast) ? order.styles_mast[0] : order.styles_mast;

        return {
          style_id: styleData?.style_id || '',
          style_code: order.style_code!,
          order_id: order.order_id,
          order_reference_no: order.order_reference_no,
          similarity_score: Math.min(score, 100),
          differences
        };
      })
      .filter(match => match.similarity_score >= 80) // Only return high-confidence matches
      .sort((a, b) => b.similarity_score - a.similarity_score);

    return matches;
  } catch (error) {
    console.error('Error searching existing style codes:', error);
    throw error;
  }
}

/**
 * Create a new style record for an order
 * Used when no existing style matches or user chooses to create new style
 */
export async function createNewStyle(
  orderData: Partial<Order>,
  styleCode: string,
  complexity_level?: number
): Promise<StyleMaster> {
  try {
    const styleData = {
      style_code: styleCode,
      item_type_id: orderData.item_type_id!,
      party_id: orderData.customer_id!,
      net_wt: orderData.gold_wt_expected || 0,
      net_wt_kt: orderData.karat_id!, // Fixed: should be net_wt_kt not net_wt_karat_id
      estimated_processing_time: calculateEstimatedTime(complexity_level || 3),
      processing_notes: orderData.remarks || null,
      is_repeat_style: false,
      complexity_level: complexity_level,
      design_notes: null,
      cad_required: true,
      cam_required: true
    };

    const { data, error } = await supabase
      .from('styles_mast')
      .insert([styleData])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating new style:', error);
    throw error;
  }
}

/**
 * Link an order to an existing style code
 * Used when user selects an existing style for a repeat order
 */
export async function linkOrderToExistingStyle(
  orderId: string,
  styleCode: string,
  referenceOrderId: string
): Promise<void> {
  try {
    const { error } = await supabase
      .from('orders')
      .update({
        style_code: styleCode,
        is_repeat_order: true,
        reference_order_id: referenceOrderId
      })
      .eq('order_id', orderId);

    if (error) throw error;
  } catch (error) {
    console.error('Error linking order to existing style:', error);
    throw error;
  }
}

/**
 * Assign a new style code to an order (same as order number)
 * Used when creating a completely new style
 */
export async function assignNewStyleCode(
  orderId: string,
  orderReferenceNo: string,
  complexity_level?: number
): Promise<string> {
  try {
    const styleCode = orderReferenceNo; // Style code = order number for new styles

    // Update the order with the new style code
    const { error: orderError } = await supabase
      .from('orders')
      .update({
        style_code: styleCode,
        is_repeat_order: false,
        complexity_level: complexity_level
      })
      .eq('order_id', orderId);

    if (orderError) throw orderError;

    return styleCode;
  } catch (error) {
    console.error('Error assigning new style code:', error);
    throw error;
  }
}

/**
 * Get style details by style code
 */
export async function getStyleByCode(styleCode: string): Promise<StyleMaster | null> {
  try {
    const { data, error } = await supabase
      .from('styles_mast')
      .select('*')
      .eq('style_code', styleCode)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // No rows found
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error getting style by code:', error);
    throw error;
  }
}

/**
 * Calculate estimated processing time based on complexity level
 * Base calculation: 10g item with different complexity levels
 */
function calculateEstimatedTime(complexity_level: number): number {
  const baseTimeHours = {
    1: 3.0,   // Easy
    2: 4.5,   // Medium-Easy  
    3: 7.5,   // Medium
    4: 12.0,  // Hard
    5: 15.0   // Very Hard
  };

  return (baseTimeHours[complexity_level as keyof typeof baseTimeHours] || 7.5) * 60; // Convert to minutes
}

/**
 * Get order details for style code assignment
 */
export async function getOrderForStyleAssignment(orderId: string): Promise<Order | null> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customer:customer_mast!inner(customer_id, description),
        item_type:item_type_mast!inner(item_type_id, description, suffix),
        karat:karat_mast!inner(karat_id, description),
        gold_color:gold_colour_mast!inner(gold_colour_id, description)
      `)
      .eq('order_id', orderId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error getting order for style assignment:', error);
    throw error;
  }
}
