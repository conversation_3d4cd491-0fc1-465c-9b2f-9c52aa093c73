/**
 * Supabase server client module
 * Provides a createClient function for server-side Supabase operations
 * 
 * @module lib/supabase/server
 */

import { createServerClient } from './client'

/**
 * Creates a Supabase client for server-side operations
 * Re-exports the createServerClient function for consistency in API routes
 * 
 * @returns Supabase client instance configured for server-side use
 */
export const createClient = createServerClient
