/**
 * Base repository class for database operations
 * Provides common CRUD operations and error handling for all repositories
 * 
 * @template T - The entity type, must include an id field
 * @template R - The return type, defaults to T
 * 
 * @example
 * ```typescript
 * class UserRepository extends BaseRepository<User> {
 *   protected tableName = 'users';
 *   protected entityName = 'User';
 * }
 * ```
 */

import { createClient, SupabaseClient, PostgrestError } from '@supabase/supabase-js';
import { 
  DatabaseError, 
  NotFoundError, 
  UniqueConstraintError,
  ForeignKeyError,
  ValidationError 
} from '@/errors/DatabaseError';

/** Interface for database query results */
interface QueryResult<R> {
  data: R | null;
  error: PostgrestError | null;
}

export abstract class BaseRepository<T extends { id: string }, R = T> {
  protected supabase: SupabaseClient;
  protected abstract tableName: string;
  protected abstract entityName: string;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
  }

  /**
   * Handles database errors by converting them to specific error types
   * 
   * @param error - The error to handle
   * @param context - Optional context for the error
   * @throws {DatabaseError} Various types of database errors
   */
  protected handleError(error: PostgrestError | Error, context?: string): never {
    if ('code' in error) {
      // Handle Postgres-specific errors
      switch (error.code) {
        case '23505':
          throw new UniqueConstraintError(error.details || 'Unique constraint violated');
        case '23503':
          throw new ForeignKeyError(error.details || 'Foreign key constraint violated');
        case '23514':
          throw new ValidationError(error.details || 'Check constraint violated');
        default:
          throw new DatabaseError(`${this.entityName} operation failed: ${error.message}`);
      }
    }
    
    throw new DatabaseError(
      context 
        ? `${this.entityName} ${context}: ${error.message}`
        : `${this.entityName} operation failed: ${error.message}`,
      { cause: error }
    );
  }

  /**
   * Executes a database query with error handling
   * 
   * @template R - The return type of the query
   * @param operation - The query operation to execute
   * @param errorContext - Optional context for error messages
   * @returns Promise resolving to the query result
   * @throws {DatabaseError} If the query fails
   * @throws {NotFoundError} If no data is returned
   */
  protected async executeQuery<R>(
    operation: () => Promise<QueryResult<R>>,
    errorContext?: string
  ): Promise<R> {
    try {
      const { data, error } = await operation();
      if (error) throw error;
      if (!data) throw new NotFoundError(this.entityName, 'unknown');
      return data;
    } catch (error) {
      if (error instanceof Error) {
        this.handleError(error, errorContext);
      }
      throw new DatabaseError('Unknown error occurred');
    }
  }

  /**
   * Retrieves all records from the table
   * 
   * @returns Promise resolving to array of all records
   * @throws {DatabaseError} If the query fails
   */
  async findAll(): Promise<T[]> {
    return this.executeQuery(
      async () => {
        // Properly handle the Supabase response to match the expected QueryResult type
        const response = await this.supabase.from(this.tableName).select('*');
        return {
          data: response.data as T[] | null,
          error: response.error
        };
      },
      'findAll'
    );
  }

  /**
   * Retrieves a single record by its ID
   * 
   * @param id - The ID of the record to find
   * @returns Promise resolving to the found record
   * @throws {NotFoundError} If the record is not found
   * @throws {DatabaseError} If the query fails
   */
  async findById(id: string): Promise<T> {
    const result = await this.executeQuery(
      async () => {
        // Properly handle the Supabase response to match the expected QueryResult type
        const response = await this.supabase
          .from(this.tableName)
          .select('*')
          .eq('id', id)
          .single();
        return {
          data: response.data as T | null,
          error: response.error
        };
      },
      `findById(${id})`
    );

    if (!result) {
      throw new NotFoundError(this.entityName, id);
    }

    return result;
  }

  /**
   * Creates a new record
   * 
   * @param data - The data to insert
   * @returns Promise resolving to the created record
   * @throws {DatabaseError} If the insert fails
   */
  async create(data: Omit<T, 'id'>): Promise<T> {
    return this.executeQuery(
      async () => {
        // Properly handle the Supabase response to match the expected QueryResult type
        const response = await this.supabase
          .from(this.tableName)
          .insert(data)
          .select()
          .single();
        return {
          data: response.data as T | null,
          error: response.error
        };
      },
      'create'
    );
  }

  /**
   * Updates an existing record
   * 
   * @param id - The ID of the record to update
   * @param data - The data to update
   * @returns Promise resolving to the updated record
   * @throws {NotFoundError} If the record is not found
   * @throws {DatabaseError} If the update fails
   */
  async update(id: string, data: Partial<T>): Promise<T> {
    // First check if record exists
    await this.findById(id);

    return this.executeQuery(
      async () => {
        // Properly handle the Supabase response to match the expected QueryResult type
        const response = await this.supabase
          .from(this.tableName)
          .update(data)
          .eq('id', id)
          .select()
          .single();
        return {
          data: response.data as T | null,
          error: response.error
        };
      },
      `update(${id})`
    );
  }

  /**
   * Deletes a record
   * 
   * @param id - The ID of the record to delete
   * @throws {NotFoundError} If the record is not found
   * @throws {DatabaseError} If the delete fails
   */
  async delete(id: string): Promise<void> {
    // First check if record exists
    await this.findById(id);

    // Execute the delete operation and handle the result
    await this.executeQuery<void>( // Specify void as the expected data type for delete
      async () => {
        // Await the Supabase delete operation
        const response = await this.supabase
          .from(this.tableName)
          .delete()
          .eq('id', id);
        
        // Return the response object which matches QueryResult structure { data: null, error: PostgrestError | null }
        return {
          data: null, // Delete operations don't return data
          error: response.error
        };
      },
      `delete(${id})`
    );
  }

  /**
   * Checks if a record exists
   * 
   * @param id - The ID to check
   * @returns Promise resolving to boolean indicating existence
   * @throws {DatabaseError} If the query fails
   */
  async validateExists(id: string): Promise<boolean> {
    try {
      await this.findById(id);
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        return false;
      }
      throw error;
    }
  }
}
