/**
 * Stone Inventory Service
 * 
 * Handles stone-specific inventory operations including issue, receipt, and tracking
 */

import { supabase } from '@/lib/db';
import { Database } from '@/types/db';
import { issueMaterialToWorker, receiveMaterialFromWorker } from './materialTransactionService';

type StoneInventory = Database['public']['Tables']['stone_inventory']['Row'];
type StoneInventoryInsert = Database['public']['Tables']['stone_inventory']['Insert'];
type StoneInventoryUpdate = Database['public']['Tables']['stone_inventory']['Update'];

export interface StoneIssueRequest {
  customer_id: string;
  order_id: string;
  stone_type_id: string;
  stone_shape_id: string;
  stone_size_id: string;
  stone_quality_id: string;
  quantity: number;
  total_weight_carats: number;
  worker_id: string;
  process_id: string;
  issued_by: string;
  notes?: string;
}

export interface StoneReceiptRequest {
  transaction_id: string;
  stones_received: {
    stone_inventory_id: string;
    returned_quantity: number;
    returned_weight_carats: number;
    condition: 'good' | 'damaged' | 'lost';
    notes?: string;
  }[];
  received_by: string;
  notes?: string;
}

/**
 * Issue stones to worker for processing
 */
export async function issueStones(request: StoneIssueRequest): Promise<{
  transaction_id: string;
  stones_issued: StoneInventory[];
}> {
  try {
    // 1. Find available stones matching criteria
    const availableStones = await getAvailableStones({
      customer_id: request.customer_id,
      stone_type_id: request.stone_type_id,
      stone_shape_id: request.stone_shape_id,
      stone_size_id: request.stone_size_id,
      stone_quality_id: request.stone_quality_id,
      required_quantity: request.quantity
    });

    if (availableStones.length === 0) {
      throw new Error('No stones available matching the specified criteria');
    }

    // 2. Calculate total available quantity
    const totalAvailable = availableStones.reduce((sum, stone) => sum + stone.quantity, 0);
    if (totalAvailable < request.quantity) {
      throw new Error(`Insufficient quantity. Required: ${request.quantity}, Available: ${totalAvailable}`);
    }

    // 3. Create material transaction record
    const materialTransaction = await issueMaterialToWorker({
      order_id: request.order_id,
      worker_id: request.worker_id,
      process_id: request.process_id,
      customer_id: request.customer_id,
      material_type: 'stone',
      issued_by: request.issued_by,
      notes: request.notes
    });

    // 4. Update stone inventory status and create stone transaction details
    const stonesIssued: StoneInventory[] = [];
    let remainingQuantity = request.quantity;

    for (const stone of availableStones) {
      if (remainingQuantity <= 0) break;

      const quantityToIssue = Math.min(stone.quantity, remainingQuantity);
      
      // Update stone inventory
      const { data: updatedStone, error: updateError } = await supabase
        .from('stone_inventory')
        .update({
          status: quantityToIssue === stone.quantity ? 'issued' : 'partially_issued',
          quantity: stone.quantity - quantityToIssue,
          updated_at: new Date().toISOString()
        })
        .eq('inventory_id', stone.inventory_id)
        .select()
        .single();

      if (updateError) throw updateError;

      // Create stone transaction detail record
      await supabase.from('stone_transaction_details').insert({
        transaction_id: materialTransaction.transaction_id,
        stone_inventory_id: stone.inventory_id,
        quantity_issued: quantityToIssue,
        carat_weight_issued: (stone.total_carat_weight / stone.quantity) * quantityToIssue,
        issue_date: new Date().toISOString()
      });

      stonesIssued.push({ ...stone, quantity: quantityToIssue });
      remainingQuantity -= quantityToIssue;
    }

    return {
      transaction_id: materialTransaction.transaction_id!,
      stones_issued: stonesIssued
    };

  } catch (error) {
    console.error('Error issuing stones:', error);
    throw error;
  }
}

/**
 * Receive stones back from worker
 */
export async function receiveStones(request: StoneReceiptRequest): Promise<void> {
  try {
    for (const stone of request.stones_received) {
      // 1. Update stone transaction details
      const { error: updateError } = await supabase
        .from('stone_transaction_details')
        .update({
          quantity_received: stone.returned_quantity,
          weight_carats_received: stone.returned_weight_carats,
          return_condition: stone.condition,
          return_date: new Date().toISOString(),
          return_notes: stone.notes
        })
        .eq('transaction_id', request.transaction_id)
        .eq('stone_inventory_id', stone.stone_inventory_id);

      if (updateError) throw updateError;

      // 2. Update stone inventory based on condition
      if (stone.condition === 'good' && stone.returned_quantity > 0) {
        // Return to inventory
        const { data: currentStone, error: fetchError } = await supabase
          .from('stone_inventory')
          .select('*')
          .eq('stone_inventory_id', stone.stone_inventory_id)
          .single();

        if (fetchError) throw fetchError;

        await supabase
          .from('stone_inventory')
          .update({
            quantity: currentStone.quantity + stone.returned_quantity,
            weight_carats: currentStone.weight_carats + stone.returned_weight_carats,
            status: 'available',
            updated_at: new Date().toISOString()
          })
          .eq('stone_inventory_id', stone.stone_inventory_id);
      } else if (stone.condition === 'lost') {
        // Record loss
        await supabase.from('stone_loss_log').insert({
          stone_inventory_id: stone.stone_inventory_id,
          transaction_id: request.transaction_id,
          quantity_lost: stone.returned_quantity,
          weight_carats_lost: stone.returned_weight_carats,
          loss_reason: 'processing_loss',
          loss_date: new Date().toISOString(),
          notes: stone.notes
        });
      }
    }

    // 3. Update main material transaction
    await receiveMaterialFromWorker({
      transaction_id: request.transaction_id,
      received_weight_grams: request.stones_received.reduce((sum, stone) => 
        sum + (stone.returned_weight_carats * 0.2), 0), // Convert carats to grams
      loss_percentage: 0, // Will be calculated based on stone details
      received_by: request.received_by,
      notes: request.notes
    });

  } catch (error) {
    console.error('Error receiving stones:', error);
    throw error;
  }
}

/**
 * Get available stones matching criteria
 */
export async function getAvailableStones({
  customer_id,
  stone_type_id,
  stone_shape_id,
  stone_size_id,
  stone_quality_id,
  required_quantity
}: {
  customer_id: string;
  stone_type_id?: string;
  stone_shape_id?: string;
  stone_size_id?: string;
  stone_quality_id?: string;
  required_quantity?: number;
}): Promise<(StoneInventory & { stone_details: any })[]> {
  try {
    let query = supabase
      .from('stone_inventory')
      .select(`
        *,
        stone_details:stone_type_mast(type_name),
        stone_shape:stone_shape_mast(shape_name),
        stone_size:stone_size_mast(size_name),
        stone_quality:stone_quality_mast(quality_name)
      `)
      .eq('customer_id', customer_id)
      .eq('status', 'available')
      .gt('quantity', 0);

    if (stone_type_id) query = query.eq('stone_type_id', stone_type_id);
    if (stone_shape_id) query = query.eq('stone_shape_id', stone_shape_id);
    if (stone_size_id) query = query.eq('stone_size_id', stone_size_id);
    if (stone_quality_id) query = query.eq('stone_quality_id', stone_quality_id);

    const { data, error } = await query.order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching available stones:', error);
    throw error;
  }
}

/**
 * Get stone inventory by customer
 */
export async function getStoneInventoryByCustomer(customerId: string) {
  try {
    const { data, error } = await supabase
      .from('stone_inventory')
      .select(`
        *,
        stone_type:stone_type_mast(type_name),
        stone_shape:stone_shape_mast(shape_name),
        stone_size:stone_size_mast(size_name),
        stone_quality:stone_quality_mast(quality_name)
      `)
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching stone inventory:', error);
    throw error;
  }
}

/**
 * Get stone transaction history
 */
export async function getStoneTransactionHistory(stoneInventoryId: string) {
  try {
    const { data, error } = await supabase
      .from('stone_transaction_details')
      .select(`
        *,
        transaction:material_transactions(
          transaction_date,
          worker:workers_mast(worker_name),
          process:process_mast(process_name)
        )
      `)
      .eq('stone_inventory_id', stoneInventoryId)
      .order('issue_date', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching stone transaction history:', error);
    throw error;
  }
}

/**
 * Get issued stones for receipt
 */
export async function getIssuedStones(filters: {
  customer_id?: string;
  order_id?: string;
  worker_id?: string;
  process_id?: string;
}) {
  try {
    let query = supabase
      .from('stone_transaction_details')
      .select(`
        *,
        stone:stones_mast(*),
        worker:workers_mast(name),
        process:process_mast(name)
      `)
      .eq('status', 'issued');

    if (filters.customer_id) query = query.eq('customer_id', filters.customer_id);
    if (filters.order_id) query = query.eq('order_id', filters.order_id);
    if (filters.worker_id) query = query.eq('worker_id', filters.worker_id);
    if (filters.process_id) query = query.eq('process_id', filters.process_id);

    const { data, error } = await query.order('issue_date', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching issued stones:', error);
    throw error;
  }
}
