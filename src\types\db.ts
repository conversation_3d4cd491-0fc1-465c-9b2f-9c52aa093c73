export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activity_log: {
        Row: {
          activity_time: string | null
          activity_type: string
          created_at: string | null
          entity_id: string
          entity_type: string
          ip_address: string | null
          log_id: string
          new_value: Json | null
          old_value: Json | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          activity_time?: string | null
          activity_type: string
          created_at?: string | null
          entity_id: string
          entity_type: string
          ip_address?: string | null
          log_id?: string
          new_value?: Json | null
          old_value?: Json | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          activity_time?: string | null
          activity_type?: string
          created_at?: string | null
          entity_id?: string
          entity_type?: string
          ip_address?: string | null
          log_id?: string
          new_value?: Json | null
          old_value?: Json | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activity_log_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_roles"
            referencedColumns: ["user_id"]
          },
        ]
      }
      customer_finding_receipt_items: {
        Row: {
          base_karat_id: string | null
          base_metal_type_id: string | null
          created_at: string | null
          embedded_stone_shape_id: string | null
          embedded_stone_size_id: string | null
          embedded_stone_type_id: string | null
          finding_description_customer: string
          item_id: string
          notes: string | null
          quantity: number
          quantity_of_embedded_stones_per_unit: number | null
          receipt_id: string | null
          total_metal_weight_grams_per_unit: number | null
          uom_id: string | null
          updated_at: string | null
        }
        Insert: {
          base_karat_id?: string | null
          base_metal_type_id?: string | null
          created_at?: string | null
          embedded_stone_shape_id?: string | null
          embedded_stone_size_id?: string | null
          embedded_stone_type_id?: string | null
          finding_description_customer: string
          item_id?: string
          notes?: string | null
          quantity: number
          quantity_of_embedded_stones_per_unit?: number | null
          receipt_id?: string | null
          total_metal_weight_grams_per_unit?: number | null
          uom_id?: string | null
          updated_at?: string | null
        }
        Update: {
          base_karat_id?: string | null
          base_metal_type_id?: string | null
          created_at?: string | null
          embedded_stone_shape_id?: string | null
          embedded_stone_size_id?: string | null
          embedded_stone_type_id?: string | null
          finding_description_customer?: string
          item_id?: string
          notes?: string | null
          quantity?: number
          quantity_of_embedded_stones_per_unit?: number | null
          receipt_id?: string | null
          total_metal_weight_grams_per_unit?: number | null
          uom_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_finding_receipt_items_base_karat_id_fkey"
            columns: ["base_karat_id"]
            isOneToOne: false
            referencedRelation: "karat_mast"
            referencedColumns: ["karat_id"]
          },
          {
            foreignKeyName: "customer_finding_receipt_items_base_metal_type_id_fkey"
            columns: ["base_metal_type_id"]
            isOneToOne: false
            referencedRelation: "metal_type_mast"
            referencedColumns: ["metal_type_id"]
          },
          {
            foreignKeyName: "customer_finding_receipt_items_embedded_stone_shape_id_fkey"
            columns: ["embedded_stone_shape_id"]
            isOneToOne: false
            referencedRelation: "stone_shape_mast"
            referencedColumns: ["shape_id"]
          },
          {
            foreignKeyName: "customer_finding_receipt_items_embedded_stone_size_id_fkey"
            columns: ["embedded_stone_size_id"]
            isOneToOne: false
            referencedRelation: "stone_size_mast"
            referencedColumns: ["size_id"]
          },
          {
            foreignKeyName: "customer_finding_receipt_items_embedded_stone_type_id_fkey"
            columns: ["embedded_stone_type_id"]
            isOneToOne: false
            referencedRelation: "stone_type_mast"
            referencedColumns: ["stone_type_id"]
          },
          {
            foreignKeyName: "customer_finding_receipt_items_receipt_id_fkey"
            columns: ["receipt_id"]
            isOneToOne: false
            referencedRelation: "customer_material_receipt"
            referencedColumns: ["receipt_id"]
          },
          {
            foreignKeyName: "customer_finding_receipt_items_uom_id_fkey"
            columns: ["uom_id"]
            isOneToOne: false
            referencedRelation: "uom_mast"
            referencedColumns: ["uom_id"]
          },
        ]
      }
      customer_mast: {
        Row: {
          created_at: string | null
          customer_code: string
          customer_id: string
          customer_name: string | null
          description: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          customer_code: string
          customer_id?: string
          customer_name?: string | null
          description: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          customer_code?: string
          customer_id?: string
          customer_name?: string | null
          description?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      customer_material_receipt: {
        Row: {
          created_at: string | null
          customer_id: string | null
          notes: string | null
          order_id: string | null
          receipt_date: string | null
          receipt_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          customer_id?: string | null
          notes?: string | null
          order_id?: string | null
          receipt_date?: string | null
          receipt_id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          customer_id?: string | null
          notes?: string | null
          order_id?: string | null
          receipt_date?: string | null
          receipt_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_material_receipt_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_mast"
            referencedColumns: ["customer_id"]
          },
          {
            foreignKeyName: "customer_material_receipt_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
        ]
      }
      customer_metal_receipt_items: {
        Row: {
          created_at: string | null
          form_received: string | null
          item_id: string
          karat_id: string | null
          metal_type_id: string | null
          notes: string | null
          purity_received: number | null
          receipt_id: string | null
          updated_at: string | null
          weight_grams: number
        }
        Insert: {
          created_at?: string | null
          form_received?: string | null
          item_id?: string
          karat_id?: string | null
          metal_type_id?: string | null
          notes?: string | null
          purity_received?: number | null
          receipt_id?: string | null
          updated_at?: string | null
          weight_grams: number
        }
        Update: {
          created_at?: string | null
          form_received?: string | null
          item_id?: string
          karat_id?: string | null
          metal_type_id?: string | null
          notes?: string | null
          purity_received?: number | null
          receipt_id?: string | null
          updated_at?: string | null
          weight_grams?: number
        }
        Relationships: [
          {
            foreignKeyName: "customer_metal_receipt_items_karat_id_fkey"
            columns: ["karat_id"]
            isOneToOne: false
            referencedRelation: "karat_mast"
            referencedColumns: ["karat_id"]
          },
          {
            foreignKeyName: "customer_metal_receipt_items_metal_type_id_fkey"
            columns: ["metal_type_id"]
            isOneToOne: false
            referencedRelation: "metal_type_mast"
            referencedColumns: ["metal_type_id"]
          },
          {
            foreignKeyName: "customer_metal_receipt_items_receipt_id_fkey"
            columns: ["receipt_id"]
            isOneToOne: false
            referencedRelation: "customer_material_receipt"
            referencedColumns: ["receipt_id"]
          },
        ]
      }
      customer_stone_receipt_items: {
        Row: {
          created_at: string | null
          customer_stone_reference: string | null
          item_id: string
          notes: string | null
          quality_description_customer: string | null
          quantity: number
          receipt_id: string | null
          stone_shape_id: string | null
          stone_size_id: string | null
          stone_type_id: string | null
          total_weight_carats: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          customer_stone_reference?: string | null
          item_id?: string
          notes?: string | null
          quality_description_customer?: string | null
          quantity: number
          receipt_id?: string | null
          stone_shape_id?: string | null
          stone_size_id?: string | null
          stone_type_id?: string | null
          total_weight_carats?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          customer_stone_reference?: string | null
          item_id?: string
          notes?: string | null
          quality_description_customer?: string | null
          quantity?: number
          receipt_id?: string | null
          stone_shape_id?: string | null
          stone_size_id?: string | null
          stone_type_id?: string | null
          total_weight_carats?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_stone_receipt_items_receipt_id_fkey"
            columns: ["receipt_id"]
            isOneToOne: false
            referencedRelation: "customer_material_receipt"
            referencedColumns: ["receipt_id"]
          },
          {
            foreignKeyName: "customer_stone_receipt_items_stone_shape_id_fkey"
            columns: ["stone_shape_id"]
            isOneToOne: false
            referencedRelation: "stone_shape_mast"
            referencedColumns: ["shape_id"]
          },
          {
            foreignKeyName: "customer_stone_receipt_items_stone_size_id_fkey"
            columns: ["stone_size_id"]
            isOneToOne: false
            referencedRelation: "stone_size_mast"
            referencedColumns: ["size_id"]
          },
          {
            foreignKeyName: "customer_stone_receipt_items_stone_type_id_fkey"
            columns: ["stone_type_id"]
            isOneToOne: false
            referencedRelation: "stone_type_mast"
            referencedColumns: ["stone_type_id"]
          },
        ]
      }
      diamond_cut_mast: {
        Row: {
          created_at: string | null
          cut_id: string
          description: string | null
          is_active: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          cut_id?: string
          description?: string | null
          is_active?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          cut_id?: string
          description?: string | null
          is_active?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      dust_parcels: {
        Row: {
          batch_id: string | null
          collection_date: string | null
          created_at: string | null
          estimated_purity: number | null
          notes: string | null
          origin_id: string | null
          origin_type: string | null
          parcel_id: string
          recovery_rate_pct: number | null
          refined: boolean | null
          updated_at: string | null
          weight_grams: number | null
        }
        Insert: {
          batch_id?: string | null
          collection_date?: string | null
          created_at?: string | null
          estimated_purity?: number | null
          notes?: string | null
          origin_id?: string | null
          origin_type?: string | null
          parcel_id?: string
          recovery_rate_pct?: number | null
          refined?: boolean | null
          updated_at?: string | null
          weight_grams?: number | null
        }
        Update: {
          batch_id?: string | null
          collection_date?: string | null
          created_at?: string | null
          estimated_purity?: number | null
          notes?: string | null
          origin_id?: string | null
          origin_type?: string | null
          parcel_id?: string
          recovery_rate_pct?: number | null
          refined?: boolean | null
          updated_at?: string | null
          weight_grams?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "dust_parcels_batch_id_fkey"
            columns: ["batch_id"]
            isOneToOne: false
            referencedRelation: "dust_refine_batches"
            referencedColumns: ["batch_id"]
          },
        ]
      }
      dust_parcels_enhanced: {
        Row: {
          actual_recovery_pct: number | null
          collection_date: string | null
          created_at: string | null
          dust_type: string
          estimated_recovery_pct: number
          notes: string | null
          parcel_id: string
          parcel_number: string
          process_id: string | null
          refine_batch_id: string | null
          status: string | null
          transaction_id: string | null
          updated_at: string | null
          weight_grams: number
          worker_id: string | null
        }
        Insert: {
          actual_recovery_pct?: number | null
          collection_date?: string | null
          created_at?: string | null
          dust_type: string
          estimated_recovery_pct: number
          notes?: string | null
          parcel_id?: string
          parcel_number: string
          process_id?: string | null
          refine_batch_id?: string | null
          status?: string | null
          transaction_id?: string | null
          updated_at?: string | null
          weight_grams: number
          worker_id?: string | null
        }
        Update: {
          actual_recovery_pct?: number | null
          collection_date?: string | null
          created_at?: string | null
          dust_type?: string
          estimated_recovery_pct?: number
          notes?: string | null
          parcel_id?: string
          parcel_number?: string
          process_id?: string | null
          refine_batch_id?: string | null
          status?: string | null
          transaction_id?: string | null
          updated_at?: string | null
          weight_grams?: number
          worker_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dust_parcels_enhanced_process_id_fkey"
            columns: ["process_id"]
            isOneToOne: false
            referencedRelation: "process_mast"
            referencedColumns: ["process_id"]
          },
          {
            foreignKeyName: "dust_parcels_enhanced_refine_batch_id_fkey"
            columns: ["refine_batch_id"]
            isOneToOne: false
            referencedRelation: "dust_refine_batches"
            referencedColumns: ["batch_id"]
          },
          {
            foreignKeyName: "dust_parcels_enhanced_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "material_transactions"
            referencedColumns: ["transaction_id"]
          },
          {
            foreignKeyName: "dust_parcels_enhanced_worker_id_fkey"
            columns: ["worker_id"]
            isOneToOne: false
            referencedRelation: "worker_mast"
            referencedColumns: ["worker_id"]
          },
        ]
      }
      dust_refine_batches: {
        Row: {
          batch_id: string
          created_at: string | null
          notes: string | null
          recovery_pct: number | null
          refine_date: string | null
          total_in_grams: number | null
          total_out_grams: number | null
          updated_at: string | null
        }
        Insert: {
          batch_id?: string
          created_at?: string | null
          notes?: string | null
          recovery_pct?: number | null
          refine_date?: string | null
          total_in_grams?: number | null
          total_out_grams?: number | null
          updated_at?: string | null
        }
        Update: {
          batch_id?: string
          created_at?: string | null
          notes?: string | null
          recovery_pct?: number | null
          refine_date?: string | null
          total_in_grams?: number | null
          total_out_grams?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      external_processor_mast: {
        Row: {
          address: string | null
          contact_person: string | null
          created_at: string | null
          email: string | null
          is_active: boolean | null
          name: string
          phone: string | null
          processor_id: string
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          contact_person?: string | null
          created_at?: string | null
          email?: string | null
          is_active?: boolean | null
          name: string
          phone?: string | null
          processor_id?: string
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          contact_person?: string | null
          created_at?: string | null
          email?: string | null
          is_active?: boolean | null
          name?: string
          phone?: string | null
          processor_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      finding_stone_details: {
        Row: {
          carat_weight: number
          created_at: string | null
          detail_id: string
          finding_id: string
          notes: string | null
          pieces: number
          stone_shape_id: string
          stone_size_id: string
          stone_type_id: string
          updated_at: string | null
        }
        Insert: {
          carat_weight: number
          created_at?: string | null
          detail_id?: string
          finding_id: string
          notes?: string | null
          pieces: number
          stone_shape_id: string
          stone_size_id: string
          stone_type_id: string
          updated_at?: string | null
        }
        Update: {
          carat_weight?: number
          created_at?: string | null
          detail_id?: string
          finding_id?: string
          notes?: string | null
          pieces?: number
          stone_shape_id?: string
          stone_size_id?: string
          stone_type_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "finding_stone_details_finding_id_fkey"
            columns: ["finding_id"]
            isOneToOne: false
            referencedRelation: "findings_mast"
            referencedColumns: ["finding_id"]
          },
          {
            foreignKeyName: "finding_stone_details_stone_shape_id_fkey"
            columns: ["stone_shape_id"]
            isOneToOne: false
            referencedRelation: "stone_shape_mast"
            referencedColumns: ["shape_id"]
          },
          {
            foreignKeyName: "finding_stone_details_stone_size_id_fkey"
            columns: ["stone_size_id"]
            isOneToOne: false
            referencedRelation: "stone_size_mast"
            referencedColumns: ["size_id"]
          },
          {
            foreignKeyName: "finding_stone_details_stone_type_id_fkey"
            columns: ["stone_type_id"]
            isOneToOne: false
            referencedRelation: "stone_type_mast"
            referencedColumns: ["stone_type_id"]
          },
        ]
      }
      finding_transaction_details: {
        Row: {
          condition_status: string | null
          created_at: string | null
          detail_id: string
          disposition_notes: string | null
          finding_id: string | null
          notes: string | null
          quantity_consumed: number | null
          quantity_damaged: number | null
          quantity_issued: number | null
          quantity_lost: number | null
          quantity_returned: number | null
          transaction_id: string
          weight_consumed_grams: number | null
          weight_damaged_grams: number | null
          weight_issued_grams: number | null
          weight_lost_grams: number | null
          weight_returned_grams: number | null
        }
        Insert: {
          condition_status?: string | null
          created_at?: string | null
          detail_id?: string
          disposition_notes?: string | null
          finding_id?: string | null
          notes?: string | null
          quantity_consumed?: number | null
          quantity_damaged?: number | null
          quantity_issued?: number | null
          quantity_lost?: number | null
          quantity_returned?: number | null
          transaction_id: string
          weight_consumed_grams?: number | null
          weight_damaged_grams?: number | null
          weight_issued_grams?: number | null
          weight_lost_grams?: number | null
          weight_returned_grams?: number | null
        }
        Update: {
          condition_status?: string | null
          created_at?: string | null
          detail_id?: string
          disposition_notes?: string | null
          finding_id?: string | null
          notes?: string | null
          quantity_consumed?: number | null
          quantity_damaged?: number | null
          quantity_issued?: number | null
          quantity_lost?: number | null
          quantity_returned?: number | null
          transaction_id?: string
          weight_consumed_grams?: number | null
          weight_damaged_grams?: number | null
          weight_issued_grams?: number | null
          weight_lost_grams?: number | null
          weight_returned_grams?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "finding_transaction_details_finding_id_fkey"
            columns: ["finding_id"]
            isOneToOne: false
            referencedRelation: "findings_mast"
            referencedColumns: ["finding_id"]
          },
          {
            foreignKeyName: "finding_transaction_details_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "material_transactions"
            referencedColumns: ["transaction_id"]
          },
        ]
      }
      findings_inventory: {
        Row: {
          created_at: string | null
          description: string | null
          finding_id: string
          is_active: boolean | null
          name: string
          quantity_on_hand: number
          uom_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          finding_id?: string
          is_active?: boolean | null
          name: string
          quantity_on_hand?: number
          uom_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          finding_id?: string
          is_active?: boolean | null
          name?: string
          quantity_on_hand?: number
          uom_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "findings_inventory_uom_id_fkey"
            columns: ["uom_id"]
            isOneToOne: false
            referencedRelation: "uom_mast"
            referencedColumns: ["uom_id"]
          },
        ]
      }
      findings_mast: {
        Row: {
          created_at: string | null
          customer_id: string
          description: string
          finding_id: string
          finding_type: string
          gross_weight_grams: number
          location: string
          notes: string | null
          order_id: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          customer_id: string
          description: string
          finding_id?: string
          finding_type?: string
          gross_weight_grams: number
          location: string
          notes?: string | null
          order_id?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          customer_id?: string
          description?: string
          finding_id?: string
          finding_type?: string
          gross_weight_grams?: number
          location?: string
          notes?: string | null
          order_id?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "findings_mast_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_mast"
            referencedColumns: ["customer_id"]
          },
          {
            foreignKeyName: "findings_mast_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
        ]
      }
      gold_colour_mast: {
        Row: {
          created_at: string | null
          description: string
          gold_colour_id: string
          processing_complexity_factor: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description: string
          gold_colour_id?: string
          processing_complexity_factor: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string
          gold_colour_id?: string
          processing_complexity_factor?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      holiday_calendar: {
        Row: {
          created_at: string | null
          description: string | null
          holiday_date: string
          holiday_id: string
          holiday_type: string
          is_recurring: boolean | null
          recurring_rule: Json | null
          updated_at: string | null
          working_hours: number | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          holiday_date: string
          holiday_id?: string
          holiday_type: string
          is_recurring?: boolean | null
          recurring_rule?: Json | null
          updated_at?: string | null
          working_hours?: number | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          holiday_date?: string
          holiday_id?: string
          holiday_type?: string
          is_recurring?: boolean | null
          recurring_rule?: Json | null
          updated_at?: string | null
          working_hours?: number | null
        }
        Relationships: []
      }
      inventory_locations: {
        Row: {
          created_at: string | null
          is_active: boolean | null
          location_id: string
          location_name: string
          location_type: string
          parent_location_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          is_active?: boolean | null
          location_id?: string
          location_name: string
          location_type: string
          parent_location_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          is_active?: boolean | null
          location_id?: string
          location_name?: string
          location_type?: string
          parent_location_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_locations_parent_location_id_fkey"
            columns: ["parent_location_id"]
            isOneToOne: false
            referencedRelation: "inventory_locations"
            referencedColumns: ["location_id"]
          },
        ]
      }
      item_type_mast: {
        Row: {
          average_processing_time: number
          created_at: string | null
          description: string
          item_type_id: string
          suffix: string
          updated_at: string | null
        }
        Insert: {
          average_processing_time: number
          created_at?: string | null
          description: string
          item_type_id?: string
          suffix: string
          updated_at?: string | null
        }
        Update: {
          average_processing_time?: number
          created_at?: string | null
          description?: string
          item_type_id?: string
          suffix?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      karat_mast: {
        Row: {
          created_at: string | null
          density: number
          description: string
          karat_id: string
          purity: number
          standard_wastage: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          density?: number
          description: string
          karat_id?: string
          purity: number
          standard_wastage: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          density?: number
          description?: string
          karat_id?: string
          purity?: number
          standard_wastage?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      material_transactions: {
        Row: {
          created_at: string | null
          created_by: string | null
          customer_id: string | null
          dust_collected_grams: number | null
          expected_loss_percentage: number | null
          gross_weight_after: number | null
          gross_weight_before: number | null
          issued_by: string | null
          issued_weight_grams: number | null
          loss_percentage: number | null
          material_type: string | null
          net_weight_after: number | null
          net_weight_before: number | null
          notes: string | null
          order_id: string
          process_id: string
          received_by: string | null
          received_date: string | null
          received_weight_grams: number | null
          status: string | null
          transaction_date: string | null
          transaction_id: string
          transaction_type: string
          updated_at: string | null
          worker_id: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          customer_id?: string | null
          dust_collected_grams?: number | null
          expected_loss_percentage?: number | null
          gross_weight_after?: number | null
          gross_weight_before?: number | null
          issued_by?: string | null
          issued_weight_grams?: number | null
          loss_percentage?: number | null
          material_type?: string | null
          net_weight_after?: number | null
          net_weight_before?: number | null
          notes?: string | null
          order_id: string
          process_id: string
          received_by?: string | null
          received_date?: string | null
          received_weight_grams?: number | null
          status?: string | null
          transaction_date?: string | null
          transaction_id?: string
          transaction_type: string
          updated_at?: string | null
          worker_id?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          customer_id?: string | null
          dust_collected_grams?: number | null
          expected_loss_percentage?: number | null
          gross_weight_after?: number | null
          gross_weight_before?: number | null
          issued_by?: string | null
          issued_weight_grams?: number | null
          loss_percentage?: number | null
          material_type?: string | null
          net_weight_after?: number | null
          net_weight_before?: number | null
          notes?: string | null
          order_id?: string
          process_id?: string
          received_by?: string | null
          received_date?: string | null
          received_weight_grams?: number | null
          status?: string | null
          transaction_date?: string | null
          transaction_id?: string
          transaction_type?: string
          updated_at?: string | null
          worker_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "material_transactions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_roles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "material_transactions_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_mast"
            referencedColumns: ["customer_id"]
          },
          {
            foreignKeyName: "material_transactions_issued_by_fkey"
            columns: ["issued_by"]
            isOneToOne: false
            referencedRelation: "user_roles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "material_transactions_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
          {
            foreignKeyName: "material_transactions_process_id_fkey"
            columns: ["process_id"]
            isOneToOne: false
            referencedRelation: "process_mast"
            referencedColumns: ["process_id"]
          },
          {
            foreignKeyName: "material_transactions_received_by_fkey"
            columns: ["received_by"]
            isOneToOne: false
            referencedRelation: "user_roles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "material_transactions_worker_id_fkey"
            columns: ["worker_id"]
            isOneToOne: false
            referencedRelation: "worker_mast"
            referencedColumns: ["worker_id"]
          },
        ]
      }
      metal_colour_mast: {
        Row: {
          created_at: string | null
          description: string
          is_active: boolean | null
          metal_colour_id: string
          metal_type_id: string
          processing_complexity_factor: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description: string
          is_active?: boolean | null
          metal_colour_id?: string
          metal_type_id: string
          processing_complexity_factor?: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string
          is_active?: boolean | null
          metal_colour_id?: string
          metal_type_id?: string
          processing_complexity_factor?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "metal_colour_mast_metal_type_id_fkey"
            columns: ["metal_type_id"]
            isOneToOne: false
            referencedRelation: "metal_type_mast"
            referencedColumns: ["metal_type_id"]
          },
        ]
      }
      metal_inventory_transactions: {
        Row: {
          created_at: string | null
          created_by: string | null
          from_location_id: string | null
          gold_colour_id: string | null
          karat_id: string
          notes: string | null
          order_id: string | null
          reference_number: string | null
          to_location_id: string | null
          transaction_date: string | null
          transaction_id: string
          transaction_type: string
          updated_at: string | null
          weight_in_grams: number
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          from_location_id?: string | null
          gold_colour_id?: string | null
          karat_id: string
          notes?: string | null
          order_id?: string | null
          reference_number?: string | null
          to_location_id?: string | null
          transaction_date?: string | null
          transaction_id?: string
          transaction_type: string
          updated_at?: string | null
          weight_in_grams: number
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          from_location_id?: string | null
          gold_colour_id?: string | null
          karat_id?: string
          notes?: string | null
          order_id?: string | null
          reference_number?: string | null
          to_location_id?: string | null
          transaction_date?: string | null
          transaction_id?: string
          transaction_type?: string
          updated_at?: string | null
          weight_in_grams?: number
        }
        Relationships: [
          {
            foreignKeyName: "metal_inventory_transactions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "worker_mast"
            referencedColumns: ["worker_id"]
          },
          {
            foreignKeyName: "metal_inventory_transactions_from_location_id_fkey"
            columns: ["from_location_id"]
            isOneToOne: false
            referencedRelation: "inventory_locations"
            referencedColumns: ["location_id"]
          },
          {
            foreignKeyName: "metal_inventory_transactions_gold_colour_id_fkey"
            columns: ["gold_colour_id"]
            isOneToOne: false
            referencedRelation: "gold_colour_mast"
            referencedColumns: ["gold_colour_id"]
          },
          {
            foreignKeyName: "metal_inventory_transactions_karat_id_fkey"
            columns: ["karat_id"]
            isOneToOne: false
            referencedRelation: "karat_mast"
            referencedColumns: ["karat_id"]
          },
          {
            foreignKeyName: "metal_inventory_transactions_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
          {
            foreignKeyName: "metal_inventory_transactions_to_location_id_fkey"
            columns: ["to_location_id"]
            isOneToOne: false
            referencedRelation: "inventory_locations"
            referencedColumns: ["location_id"]
          },
        ]
      }
      metal_pool: {
        Row: {
          created_at: string | null
          current_weight: number
          customer_id: string | null
          initial_weight: number
          is_active: boolean | null
          karat_id: string | null
          metal_type_id: string | null
          pool_id: string
          uom_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          current_weight: number
          customer_id?: string | null
          initial_weight: number
          is_active?: boolean | null
          karat_id?: string | null
          metal_type_id?: string | null
          pool_id?: string
          uom_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          current_weight?: number
          customer_id?: string | null
          initial_weight?: number
          is_active?: boolean | null
          karat_id?: string | null
          metal_type_id?: string | null
          pool_id?: string
          uom_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "metal_pool_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_mast"
            referencedColumns: ["customer_id"]
          },
          {
            foreignKeyName: "metal_pool_karat_id_fkey"
            columns: ["karat_id"]
            isOneToOne: false
            referencedRelation: "karat_mast"
            referencedColumns: ["karat_id"]
          },
          {
            foreignKeyName: "metal_pool_metal_type_id_fkey"
            columns: ["metal_type_id"]
            isOneToOne: false
            referencedRelation: "metal_type_mast"
            referencedColumns: ["metal_type_id"]
          },
          {
            foreignKeyName: "metal_pool_uom_id_fkey"
            columns: ["uom_id"]
            isOneToOne: false
            referencedRelation: "uom_mast"
            referencedColumns: ["uom_id"]
          },
        ]
      }
      metal_pools: {
        Row: {
          created_at: string | null
          current_weight_grams: number | null
          location_id: string | null
          metal_type_id: string
          pool_id: string
          purity: number
          reserved_weight_grams: number | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          current_weight_grams?: number | null
          location_id?: string | null
          metal_type_id: string
          pool_id?: string
          purity: number
          reserved_weight_grams?: number | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          current_weight_grams?: number | null
          location_id?: string | null
          metal_type_id?: string
          pool_id?: string
          purity?: number
          reserved_weight_grams?: number | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "metal_pools_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "inventory_locations"
            referencedColumns: ["location_id"]
          },
          {
            foreignKeyName: "metal_pools_metal_type_id_fkey"
            columns: ["metal_type_id"]
            isOneToOne: false
            referencedRelation: "metal_type_mast"
            referencedColumns: ["metal_type_id"]
          },
        ]
      }
      metal_transaction_type_mast: {
        Row: {
          affects_pool: boolean
          code: string
          created_at: string | null
          description: string | null
          is_active: boolean | null
          name: string
          type_id: string
          updated_at: string | null
        }
        Insert: {
          affects_pool: boolean
          code: string
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          name: string
          type_id?: string
          updated_at?: string | null
        }
        Update: {
          affects_pool?: boolean
          code?: string
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          name?: string
          type_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      metal_transactions: {
        Row: {
          created_at: string | null
          created_by: string | null
          external_entity_id: string | null
          fine_weight: number | null
          notes: string | null
          order_id: string | null
          pool_id: string | null
          process_id: string | null
          related_transaction_id: string | null
          transaction_date: string | null
          transaction_id: string
          transaction_type_id: string | null
          uom_id: string | null
          updated_at: string | null
          weight: number
          worker_id: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          external_entity_id?: string | null
          fine_weight?: number | null
          notes?: string | null
          order_id?: string | null
          pool_id?: string | null
          process_id?: string | null
          related_transaction_id?: string | null
          transaction_date?: string | null
          transaction_id?: string
          transaction_type_id?: string | null
          uom_id?: string | null
          updated_at?: string | null
          weight: number
          worker_id?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          external_entity_id?: string | null
          fine_weight?: number | null
          notes?: string | null
          order_id?: string | null
          pool_id?: string | null
          process_id?: string | null
          related_transaction_id?: string | null
          transaction_date?: string | null
          transaction_id?: string
          transaction_type_id?: string | null
          uom_id?: string | null
          updated_at?: string | null
          weight?: number
          worker_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "metal_transactions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "worker_mast"
            referencedColumns: ["worker_id"]
          },
          {
            foreignKeyName: "metal_transactions_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
          {
            foreignKeyName: "metal_transactions_pool_id_fkey"
            columns: ["pool_id"]
            isOneToOne: false
            referencedRelation: "metal_pool"
            referencedColumns: ["pool_id"]
          },
          {
            foreignKeyName: "metal_transactions_process_id_fkey"
            columns: ["process_id"]
            isOneToOne: false
            referencedRelation: "process_mast"
            referencedColumns: ["process_id"]
          },
          {
            foreignKeyName: "metal_transactions_related_transaction_id_fkey"
            columns: ["related_transaction_id"]
            isOneToOne: false
            referencedRelation: "metal_transactions"
            referencedColumns: ["transaction_id"]
          },
          {
            foreignKeyName: "metal_transactions_transaction_type_id_fkey"
            columns: ["transaction_type_id"]
            isOneToOne: false
            referencedRelation: "metal_transaction_type_mast"
            referencedColumns: ["type_id"]
          },
          {
            foreignKeyName: "metal_transactions_uom_id_fkey"
            columns: ["uom_id"]
            isOneToOne: false
            referencedRelation: "uom_mast"
            referencedColumns: ["uom_id"]
          },
          {
            foreignKeyName: "metal_transactions_worker_id_fkey"
            columns: ["worker_id"]
            isOneToOne: false
            referencedRelation: "worker_mast"
            referencedColumns: ["worker_id"]
          },
        ]
      }
      metal_transformation: {
        Row: {
          created_at: string | null
          created_by: string | null
          notes: string | null
          process_id: string | null
          source_pool_id: string | null
          source_weight: number
          target_pool_id: string | null
          target_weight: number
          transformation_date: string | null
          transformation_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          notes?: string | null
          process_id?: string | null
          source_pool_id?: string | null
          source_weight: number
          target_pool_id?: string | null
          target_weight: number
          transformation_date?: string | null
          transformation_id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          notes?: string | null
          process_id?: string | null
          source_pool_id?: string | null
          source_weight?: number
          target_pool_id?: string | null
          target_weight?: number
          transformation_date?: string | null
          transformation_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "metal_transformation_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "worker_mast"
            referencedColumns: ["worker_id"]
          },
          {
            foreignKeyName: "metal_transformation_process_id_fkey"
            columns: ["process_id"]
            isOneToOne: false
            referencedRelation: "process_mast"
            referencedColumns: ["process_id"]
          },
          {
            foreignKeyName: "metal_transformation_source_pool_id_fkey"
            columns: ["source_pool_id"]
            isOneToOne: false
            referencedRelation: "metal_pool"
            referencedColumns: ["pool_id"]
          },
          {
            foreignKeyName: "metal_transformation_target_pool_id_fkey"
            columns: ["target_pool_id"]
            isOneToOne: false
            referencedRelation: "metal_pool"
            referencedColumns: ["pool_id"]
          },
        ]
      }
      metal_type_mast: {
        Row: {
          created_at: string | null
          description: string | null
          is_active: boolean | null
          metal_type_id: string
          name: string
          symbol: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          metal_type_id?: string
          name: string
          symbol?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          metal_type_id?: string
          name?: string
          symbol?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      order_category_mast: {
        Row: {
          base_processing_time: number
          created_at: string | null
          description: string
          order_category_id: string
          updated_at: string | null
        }
        Insert: {
          base_processing_time: number
          created_at?: string | null
          description: string
          order_category_id?: string
          updated_at?: string | null
        }
        Update: {
          base_processing_time?: number
          created_at?: string | null
          description?: string
          order_category_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      order_metal_allocation: {
        Row: {
          allocated_by: string | null
          allocation_date: string | null
          allocation_id: string
          created_at: string | null
          notes: string | null
          order_id: string
          pool_id: string
          status: string | null
          uom_id: string
          updated_at: string | null
          weight: number
        }
        Insert: {
          allocated_by?: string | null
          allocation_date?: string | null
          allocation_id?: string
          created_at?: string | null
          notes?: string | null
          order_id: string
          pool_id: string
          status?: string | null
          uom_id: string
          updated_at?: string | null
          weight: number
        }
        Update: {
          allocated_by?: string | null
          allocation_date?: string | null
          allocation_id?: string
          created_at?: string | null
          notes?: string | null
          order_id?: string
          pool_id?: string
          status?: string | null
          uom_id?: string
          updated_at?: string | null
          weight?: number
        }
        Relationships: [
          {
            foreignKeyName: "order_metal_allocation_allocated_by_fkey"
            columns: ["allocated_by"]
            isOneToOne: false
            referencedRelation: "user_roles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "order_metal_allocation_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
          {
            foreignKeyName: "order_metal_allocation_pool_id_fkey"
            columns: ["pool_id"]
            isOneToOne: false
            referencedRelation: "metal_pools"
            referencedColumns: ["pool_id"]
          },
          {
            foreignKeyName: "order_metal_allocation_uom_id_fkey"
            columns: ["uom_id"]
            isOneToOne: false
            referencedRelation: "uom_mast"
            referencedColumns: ["uom_id"]
          },
        ]
      }
      order_process_flow: {
        Row: {
          actual_end: string | null
          actual_start: string | null
          created_at: string | null
          expected_end: string | null
          expected_start: string | null
          external_processor_id: string | null
          iteration_no: number | null
          order_id: string | null
          order_process_id: string
          parent_process_id: string | null
          process_id: string | null
          status: string | null
          updated_at: string | null
          worker_id: string | null
        }
        Insert: {
          actual_end?: string | null
          actual_start?: string | null
          created_at?: string | null
          expected_end?: string | null
          expected_start?: string | null
          external_processor_id?: string | null
          iteration_no?: number | null
          order_id?: string | null
          order_process_id?: string
          parent_process_id?: string | null
          process_id?: string | null
          status?: string | null
          updated_at?: string | null
          worker_id?: string | null
        }
        Update: {
          actual_end?: string | null
          actual_start?: string | null
          created_at?: string | null
          expected_end?: string | null
          expected_start?: string | null
          external_processor_id?: string | null
          iteration_no?: number | null
          order_id?: string | null
          order_process_id?: string
          parent_process_id?: string | null
          process_id?: string | null
          status?: string | null
          updated_at?: string | null
          worker_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "order_process_flow_external_processor_id_fkey"
            columns: ["external_processor_id"]
            isOneToOne: false
            referencedRelation: "external_processor_mast"
            referencedColumns: ["processor_id"]
          },
          {
            foreignKeyName: "order_process_flow_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
          {
            foreignKeyName: "order_process_flow_process_id_fkey"
            columns: ["process_id"]
            isOneToOne: false
            referencedRelation: "process_mast"
            referencedColumns: ["process_id"]
          },
          {
            foreignKeyName: "order_process_flow_worker_id_fkey"
            columns: ["worker_id"]
            isOneToOne: false
            referencedRelation: "worker_mast"
            referencedColumns: ["worker_id"]
          },
        ]
      }
      orders: {
        Row: {
          complexity_level: number | null
          created_at: string | null
          customer_id: string
          diamond_quality: string | null
          diamond_wt_expected: number | null
          diamonds_received: boolean | null
          expected_delivery_date: string
          gold_colour_id: string
          gold_wt_expected: number | null
          is_repeat_order: boolean | null
          issue_date: string
          item_type_id: string
          karat_id: string
          metal_received: boolean | null
          order_category_id: string
          order_id: string
          order_reference_no: string
          order_status: string | null
          party_delivery_date: string | null
          party_order_no: string | null
          polki_quality: string | null
          polki_received: boolean | null
          qr_code: string | null
          reference_image_url: string | null
          reference_order_id: string | null
          remarks: string | null
          style_code: string | null
          tags: string[] | null
          third_party_cust_id: string | null
          updated_at: string | null
        }
        Insert: {
          complexity_level?: number | null
          created_at?: string | null
          customer_id: string
          diamond_quality?: string | null
          diamond_wt_expected?: number | null
          diamonds_received?: boolean | null
          expected_delivery_date: string
          gold_colour_id: string
          gold_wt_expected?: number | null
          is_repeat_order?: boolean | null
          issue_date: string
          item_type_id: string
          karat_id: string
          metal_received?: boolean | null
          order_category_id: string
          order_id?: string
          order_reference_no: string
          order_status?: string | null
          party_delivery_date?: string | null
          party_order_no?: string | null
          polki_quality?: string | null
          polki_received?: boolean | null
          qr_code?: string | null
          reference_image_url?: string | null
          reference_order_id?: string | null
          remarks?: string | null
          style_code?: string | null
          tags?: string[] | null
          third_party_cust_id?: string | null
          updated_at?: string | null
        }
        Update: {
          complexity_level?: number | null
          created_at?: string | null
          customer_id?: string
          diamond_quality?: string | null
          diamond_wt_expected?: number | null
          diamonds_received?: boolean | null
          expected_delivery_date?: string
          gold_colour_id?: string
          gold_wt_expected?: number | null
          is_repeat_order?: boolean | null
          issue_date?: string
          item_type_id?: string
          karat_id?: string
          metal_received?: boolean | null
          order_category_id?: string
          order_id?: string
          order_reference_no?: string
          order_status?: string | null
          party_delivery_date?: string | null
          party_order_no?: string | null
          polki_quality?: string | null
          polki_received?: boolean | null
          qr_code?: string | null
          reference_image_url?: string | null
          reference_order_id?: string | null
          remarks?: string | null
          style_code?: string | null
          tags?: string[] | null
          third_party_cust_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "orders_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_mast"
            referencedColumns: ["customer_id"]
          },
          {
            foreignKeyName: "orders_gold_colour_id_fkey"
            columns: ["gold_colour_id"]
            isOneToOne: false
            referencedRelation: "gold_colour_mast"
            referencedColumns: ["gold_colour_id"]
          },
          {
            foreignKeyName: "orders_item_type_id_fkey"
            columns: ["item_type_id"]
            isOneToOne: false
            referencedRelation: "item_type_mast"
            referencedColumns: ["item_type_id"]
          },
          {
            foreignKeyName: "orders_karat_id_fkey"
            columns: ["karat_id"]
            isOneToOne: false
            referencedRelation: "karat_mast"
            referencedColumns: ["karat_id"]
          },
          {
            foreignKeyName: "orders_order_category_id_fkey"
            columns: ["order_category_id"]
            isOneToOne: false
            referencedRelation: "order_category_mast"
            referencedColumns: ["order_category_id"]
          },
          {
            foreignKeyName: "orders_reference_order_id_fkey"
            columns: ["reference_order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
          {
            foreignKeyName: "orders_third_party_cust_id_fkey"
            columns: ["third_party_cust_id"]
            isOneToOne: false
            referencedRelation: "third_party_cust_mast"
            referencedColumns: ["party_cust_id"]
          },
        ]
      }
      process_mast: {
        Row: {
          can_be_outsourced: boolean | null
          complexity_hours_json: Json | null
          created_at: string | null
          default_recovery_pct: number | null
          default_wastage_pct: number | null
          description: string | null
          dust_generated: boolean | null
          findings_required: boolean | null
          is_active: boolean | null
          materials_issued: Json | null
          materials_returned: Json | null
          name: string
          process_id: string
          quality_checkpoints: Json | null
          repeatable: boolean | null
          requires_skill: boolean | null
          standard_time: number
          stones_required: boolean | null
          updated_at: string | null
        }
        Insert: {
          can_be_outsourced?: boolean | null
          complexity_hours_json?: Json | null
          created_at?: string | null
          default_recovery_pct?: number | null
          default_wastage_pct?: number | null
          description?: string | null
          dust_generated?: boolean | null
          findings_required?: boolean | null
          is_active?: boolean | null
          materials_issued?: Json | null
          materials_returned?: Json | null
          name: string
          process_id?: string
          quality_checkpoints?: Json | null
          repeatable?: boolean | null
          requires_skill?: boolean | null
          standard_time: number
          stones_required?: boolean | null
          updated_at?: string | null
        }
        Update: {
          can_be_outsourced?: boolean | null
          complexity_hours_json?: Json | null
          created_at?: string | null
          default_recovery_pct?: number | null
          default_wastage_pct?: number | null
          description?: string | null
          dust_generated?: boolean | null
          findings_required?: boolean | null
          is_active?: boolean | null
          materials_issued?: Json | null
          materials_returned?: Json | null
          name?: string
          process_id?: string
          quality_checkpoints?: Json | null
          repeatable?: boolean | null
          requires_skill?: boolean | null
          standard_time?: number
          stones_required?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      process_templates: {
        Row: {
          complexity_level: number | null
          created_at: string | null
          estimated_total_hours: number | null
          is_active: boolean | null
          item_type_id: string
          process_sequence: Json
          template_id: string
          template_name: string
          updated_at: string | null
        }
        Insert: {
          complexity_level?: number | null
          created_at?: string | null
          estimated_total_hours?: number | null
          is_active?: boolean | null
          item_type_id: string
          process_sequence: Json
          template_id?: string
          template_name: string
          updated_at?: string | null
        }
        Update: {
          complexity_level?: number | null
          created_at?: string | null
          estimated_total_hours?: number | null
          is_active?: boolean | null
          item_type_id?: string
          process_sequence?: Json
          template_id?: string
          template_name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "process_templates_item_type_id_fkey"
            columns: ["item_type_id"]
            isOneToOne: false
            referencedRelation: "item_type_mast"
            referencedColumns: ["item_type_id"]
          },
        ]
      }
      process_tracking: {
        Row: {
          actual_duration_hours: number | null
          completed_at: string | null
          created_at: string | null
          estimated_duration_hours: number | null
          notes: string | null
          order_id: string
          process_id: string
          quality_score: number | null
          started_at: string | null
          status: string | null
          tracking_id: string
          updated_at: string | null
          worker_id: string | null
        }
        Insert: {
          actual_duration_hours?: number | null
          completed_at?: string | null
          created_at?: string | null
          estimated_duration_hours?: number | null
          notes?: string | null
          order_id: string
          process_id: string
          quality_score?: number | null
          started_at?: string | null
          status?: string | null
          tracking_id?: string
          updated_at?: string | null
          worker_id?: string | null
        }
        Update: {
          actual_duration_hours?: number | null
          completed_at?: string | null
          created_at?: string | null
          estimated_duration_hours?: number | null
          notes?: string | null
          order_id?: string
          process_id?: string
          quality_score?: number | null
          started_at?: string | null
          status?: string | null
          tracking_id?: string
          updated_at?: string | null
          worker_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "process_tracking_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
          {
            foreignKeyName: "process_tracking_process_id_fkey"
            columns: ["process_id"]
            isOneToOne: false
            referencedRelation: "process_mast"
            referencedColumns: ["process_id"]
          },
          {
            foreignKeyName: "process_tracking_worker_id_fkey"
            columns: ["worker_id"]
            isOneToOne: false
            referencedRelation: "worker_mast"
            referencedColumns: ["worker_id"]
          },
        ]
      }
      purity_mast: {
        Row: {
          created_at: string | null
          density: number
          description: string
          is_active: boolean | null
          metal_type_id: string
          purity_id: string
          purity_percentage: number
          standard_wastage_percentage: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          density?: number
          description: string
          is_active?: boolean | null
          metal_type_id: string
          purity_id?: string
          purity_percentage: number
          standard_wastage_percentage?: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          density?: number
          description?: string
          is_active?: boolean | null
          metal_type_id?: string
          purity_id?: string
          purity_percentage?: number
          standard_wastage_percentage?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "purity_mast_metal_type_id_fkey"
            columns: ["metal_type_id"]
            isOneToOne: false
            referencedRelation: "metal_type_mast"
            referencedColumns: ["metal_type_id"]
          },
        ]
      }
      setting_process_receipts: {
        Row: {
          created_at: string | null
          created_by: string | null
          gross_weight_received: number
          net_weight_received: number
          notes: string | null
          order_id: string
          process_id: string
          receipt_id: string
          scenario: string
          total_carats_broken: number | null
          total_carats_lost: number | null
          total_carats_returned: number | null
          total_carats_set: number | null
          total_stones_broken: number | null
          total_stones_lost: number | null
          total_stones_returned: number | null
          total_stones_set: number | null
          transaction_id: string
          updated_at: string | null
          worker_id: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          gross_weight_received: number
          net_weight_received: number
          notes?: string | null
          order_id: string
          process_id: string
          receipt_id?: string
          scenario: string
          total_carats_broken?: number | null
          total_carats_lost?: number | null
          total_carats_returned?: number | null
          total_carats_set?: number | null
          total_stones_broken?: number | null
          total_stones_lost?: number | null
          total_stones_returned?: number | null
          total_stones_set?: number | null
          transaction_id: string
          updated_at?: string | null
          worker_id: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          gross_weight_received?: number
          net_weight_received?: number
          notes?: string | null
          order_id?: string
          process_id?: string
          receipt_id?: string
          scenario?: string
          total_carats_broken?: number | null
          total_carats_lost?: number | null
          total_carats_returned?: number | null
          total_carats_set?: number | null
          total_stones_broken?: number | null
          total_stones_lost?: number | null
          total_stones_returned?: number | null
          total_stones_set?: number | null
          transaction_id?: string
          updated_at?: string | null
          worker_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "setting_process_receipts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_roles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "setting_process_receipts_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
          {
            foreignKeyName: "setting_process_receipts_process_id_fkey"
            columns: ["process_id"]
            isOneToOne: false
            referencedRelation: "process_mast"
            referencedColumns: ["process_id"]
          },
          {
            foreignKeyName: "setting_process_receipts_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "material_transactions"
            referencedColumns: ["transaction_id"]
          },
          {
            foreignKeyName: "setting_process_receipts_worker_id_fkey"
            columns: ["worker_id"]
            isOneToOne: false
            referencedRelation: "worker_mast"
            referencedColumns: ["worker_id"]
          },
        ]
      }
      stone_disposition_details: {
        Row: {
          carats_broken: number | null
          carats_issued: number
          carats_lost: number | null
          carats_returned: number | null
          carats_set: number | null
          created_at: string | null
          description: string
          disposition_id: string
          disposition_notes: string | null
          quantity_broken: number | null
          quantity_issued: number
          quantity_lost: number | null
          quantity_returned: number | null
          quantity_set: number | null
          receipt_id: string
          stone_shape_id: string
          stone_size_id: string
          stone_transaction_detail_id: string
          stone_type_id: string
        }
        Insert: {
          carats_broken?: number | null
          carats_issued: number
          carats_lost?: number | null
          carats_returned?: number | null
          carats_set?: number | null
          created_at?: string | null
          description: string
          disposition_id?: string
          disposition_notes?: string | null
          quantity_broken?: number | null
          quantity_issued: number
          quantity_lost?: number | null
          quantity_returned?: number | null
          quantity_set?: number | null
          receipt_id: string
          stone_shape_id: string
          stone_size_id: string
          stone_transaction_detail_id: string
          stone_type_id: string
        }
        Update: {
          carats_broken?: number | null
          carats_issued?: number
          carats_lost?: number | null
          carats_returned?: number | null
          carats_set?: number | null
          created_at?: string | null
          description?: string
          disposition_id?: string
          disposition_notes?: string | null
          quantity_broken?: number | null
          quantity_issued?: number
          quantity_lost?: number | null
          quantity_returned?: number | null
          quantity_set?: number | null
          receipt_id?: string
          stone_shape_id?: string
          stone_size_id?: string
          stone_transaction_detail_id?: string
          stone_type_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "stone_disposition_details_receipt_id_fkey"
            columns: ["receipt_id"]
            isOneToOne: false
            referencedRelation: "setting_process_receipts"
            referencedColumns: ["receipt_id"]
          },
          {
            foreignKeyName: "stone_disposition_details_receipt_id_fkey"
            columns: ["receipt_id"]
            isOneToOne: false
            referencedRelation: "setting_process_summary"
            referencedColumns: ["receipt_id"]
          },
          {
            foreignKeyName: "stone_disposition_details_stone_shape_id_fkey"
            columns: ["stone_shape_id"]
            isOneToOne: false
            referencedRelation: "stone_shape_mast"
            referencedColumns: ["shape_id"]
          },
          {
            foreignKeyName: "stone_disposition_details_stone_size_id_fkey"
            columns: ["stone_size_id"]
            isOneToOne: false
            referencedRelation: "stone_size_mast"
            referencedColumns: ["size_id"]
          },
          {
            foreignKeyName: "stone_disposition_details_stone_transaction_detail_id_fkey"
            columns: ["stone_transaction_detail_id"]
            isOneToOne: false
            referencedRelation: "stone_transaction_details"
            referencedColumns: ["detail_id"]
          },
          {
            foreignKeyName: "stone_disposition_details_stone_type_id_fkey"
            columns: ["stone_type_id"]
            isOneToOne: false
            referencedRelation: "stone_type_mast"
            referencedColumns: ["stone_type_id"]
          },
        ]
      }
      stone_inventory: {
        Row: {
          created_at: string | null
          customer_id: string
          inventory_id: string
          location: string
          notes: string | null
          order_id: string | null
          quantity: number
          status: string | null
          stone_quality_id: string
          stone_shape_id: string
          stone_size_id: string
          stone_type_id: string
          total_carat_weight: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          customer_id: string
          inventory_id?: string
          location: string
          notes?: string | null
          order_id?: string | null
          quantity: number
          status?: string | null
          stone_quality_id: string
          stone_shape_id: string
          stone_size_id: string
          stone_type_id: string
          total_carat_weight: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          customer_id?: string
          inventory_id?: string
          location?: string
          notes?: string | null
          order_id?: string | null
          quantity?: number
          status?: string | null
          stone_quality_id?: string
          stone_shape_id?: string
          stone_size_id?: string
          stone_type_id?: string
          total_carat_weight?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "stone_inventory_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_mast"
            referencedColumns: ["customer_id"]
          },
          {
            foreignKeyName: "stone_inventory_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
          {
            foreignKeyName: "stone_inventory_stone_quality_id_fkey"
            columns: ["stone_quality_id"]
            isOneToOne: false
            referencedRelation: "stone_quality_mast"
            referencedColumns: ["quality_id"]
          },
          {
            foreignKeyName: "stone_inventory_stone_shape_id_fkey"
            columns: ["stone_shape_id"]
            isOneToOne: false
            referencedRelation: "stone_shape_mast"
            referencedColumns: ["shape_id"]
          },
          {
            foreignKeyName: "stone_inventory_stone_size_id_fkey"
            columns: ["stone_size_id"]
            isOneToOne: false
            referencedRelation: "stone_size_mast"
            referencedColumns: ["size_id"]
          },
          {
            foreignKeyName: "stone_inventory_stone_type_id_fkey"
            columns: ["stone_type_id"]
            isOneToOne: false
            referencedRelation: "stone_type_mast"
            referencedColumns: ["stone_type_id"]
          },
        ]
      }
      stone_quality_mast: {
        Row: {
          created_at: string | null
          description: string | null
          grade_order: number | null
          is_active: boolean | null
          name: string
          quality_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          grade_order?: number | null
          is_active?: boolean | null
          name: string
          quality_id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          grade_order?: number | null
          is_active?: boolean | null
          name?: string
          quality_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      stone_shape_mast: {
        Row: {
          created_at: string | null
          description: string | null
          is_active: boolean | null
          name: string
          shape_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          name: string
          shape_id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          name?: string
          shape_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      stone_size_mast: {
        Row: {
          created_at: string | null
          diameter_mm: number | null
          max_carat_weight: number | null
          min_carat_weight: number | null
          size_description: string
          size_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          diameter_mm?: number | null
          max_carat_weight?: number | null
          min_carat_weight?: number | null
          size_description: string
          size_id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          diameter_mm?: number | null
          max_carat_weight?: number | null
          min_carat_weight?: number | null
          size_description?: string
          size_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      stone_transaction_details: {
        Row: {
          carat_weight_consumed: number | null
          carat_weight_damaged: number | null
          carat_weight_issued: number | null
          carat_weight_lost: number | null
          carat_weight_returned: number | null
          created_at: string | null
          detail_id: string
          disposition_notes: string | null
          notes: string | null
          quantity_consumed: number | null
          quantity_damaged: number | null
          quantity_issued: number | null
          quantity_lost: number | null
          quantity_returned: number | null
          stone_inventory_id: string | null
          stone_shape_id: string
          stone_size_id: string
          stone_type_id: string
          transaction_id: string
        }
        Insert: {
          carat_weight_consumed?: number | null
          carat_weight_damaged?: number | null
          carat_weight_issued?: number | null
          carat_weight_lost?: number | null
          carat_weight_returned?: number | null
          created_at?: string | null
          detail_id?: string
          disposition_notes?: string | null
          notes?: string | null
          quantity_consumed?: number | null
          quantity_damaged?: number | null
          quantity_issued?: number | null
          quantity_lost?: number | null
          quantity_returned?: number | null
          stone_inventory_id?: string | null
          stone_shape_id: string
          stone_size_id: string
          stone_type_id: string
          transaction_id: string
        }
        Update: {
          carat_weight_consumed?: number | null
          carat_weight_damaged?: number | null
          carat_weight_issued?: number | null
          carat_weight_lost?: number | null
          carat_weight_returned?: number | null
          created_at?: string | null
          detail_id?: string
          disposition_notes?: string | null
          notes?: string | null
          quantity_consumed?: number | null
          quantity_damaged?: number | null
          quantity_issued?: number | null
          quantity_lost?: number | null
          quantity_returned?: number | null
          stone_inventory_id?: string | null
          stone_shape_id?: string
          stone_size_id?: string
          stone_type_id?: string
          transaction_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "stone_transaction_details_stone_inventory_id_fkey"
            columns: ["stone_inventory_id"]
            isOneToOne: false
            referencedRelation: "stone_inventory"
            referencedColumns: ["inventory_id"]
          },
          {
            foreignKeyName: "stone_transaction_details_stone_shape_id_fkey"
            columns: ["stone_shape_id"]
            isOneToOne: false
            referencedRelation: "stone_shape_mast"
            referencedColumns: ["shape_id"]
          },
          {
            foreignKeyName: "stone_transaction_details_stone_size_id_fkey"
            columns: ["stone_size_id"]
            isOneToOne: false
            referencedRelation: "stone_size_mast"
            referencedColumns: ["size_id"]
          },
          {
            foreignKeyName: "stone_transaction_details_stone_type_id_fkey"
            columns: ["stone_type_id"]
            isOneToOne: false
            referencedRelation: "stone_type_mast"
            referencedColumns: ["stone_type_id"]
          },
          {
            foreignKeyName: "stone_transaction_details_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "material_transactions"
            referencedColumns: ["transaction_id"]
          },
        ]
      }
      stone_type_mast: {
        Row: {
          created_at: string | null
          description: string | null
          is_active: boolean | null
          is_diamond: boolean | null
          name: string
          stone_type_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          is_diamond?: boolean | null
          name: string
          stone_type_id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          is_diamond?: boolean | null
          name?: string
          stone_type_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      styles_mast: {
        Row: {
          cad_required: boolean | null
          cam_required: boolean | null
          complexity_level: number | null
          created_at: string | null
          design_notes: string | null
          estimated_processing_time: number
          is_repeat_style: boolean | null
          item_type_id: string | null
          net_wt: number
          net_wt_karat_id: string | null
          parent_style_id: string | null
          party_id: string | null
          processing_notes: string | null
          style_code: string | null
          style_id: string
          updated_at: string | null
        }
        Insert: {
          cad_required?: boolean | null
          cam_required?: boolean | null
          complexity_level?: number | null
          created_at?: string | null
          design_notes?: string | null
          estimated_processing_time: number
          is_repeat_style?: boolean | null
          item_type_id?: string | null
          net_wt: number
          net_wt_karat_id?: string | null
          parent_style_id?: string | null
          party_id?: string | null
          processing_notes?: string | null
          style_code?: string | null
          style_id?: string
          updated_at?: string | null
        }
        Update: {
          cad_required?: boolean | null
          cam_required?: boolean | null
          complexity_level?: number | null
          created_at?: string | null
          design_notes?: string | null
          estimated_processing_time?: number
          is_repeat_style?: boolean | null
          item_type_id?: string | null
          net_wt?: number
          net_wt_karat_id?: string | null
          parent_style_id?: string | null
          party_id?: string | null
          processing_notes?: string | null
          style_code?: string | null
          style_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "styles_mast_item_type_id_fkey"
            columns: ["item_type_id"]
            isOneToOne: false
            referencedRelation: "item_type_mast"
            referencedColumns: ["item_type_id"]
          },
          {
            foreignKeyName: "styles_mast_net_wt_kt_fkey"
            columns: ["net_wt_karat_id"]
            isOneToOne: false
            referencedRelation: "karat_mast"
            referencedColumns: ["karat_id"]
          },
          {
            foreignKeyName: "styles_mast_parent_style_id_fkey"
            columns: ["parent_style_id"]
            isOneToOne: false
            referencedRelation: "styles_mast"
            referencedColumns: ["style_id"]
          },
          {
            foreignKeyName: "styles_mast_party_id_fkey"
            columns: ["party_id"]
            isOneToOne: false
            referencedRelation: "third_party_cust_mast"
            referencedColumns: ["party_cust_id"]
          },
        ]
      }
      third_party_cust_mast: {
        Row: {
          created_at: string | null
          description: string
          party_cust_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description: string
          party_cust_id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string
          party_cust_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      uom_mast: {
        Row: {
          code: string
          created_at: string | null
          description: string | null
          is_active: boolean | null
          name: string
          uom_id: string
          updated_at: string | null
        }
        Insert: {
          code: string
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          name: string
          uom_id?: string
          updated_at?: string | null
        }
        Update: {
          code?: string
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          name?: string
          uom_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          created_at: string | null
          role: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          role: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          role?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      worker_mast: {
        Row: {
          available_from: string | null
          available_to: string | null
          created_at: string | null
          efficiency_factor: number | null
          is_active: boolean | null
          is_vendor: boolean | null
          name: string
          shift_end: string | null
          shift_start: string | null
          skills: Json | null
          updated_at: string | null
          worker_id: string
          worker_type: string | null
        }
        Insert: {
          available_from?: string | null
          available_to?: string | null
          created_at?: string | null
          efficiency_factor?: number | null
          is_active?: boolean | null
          is_vendor?: boolean | null
          name: string
          shift_end?: string | null
          shift_start?: string | null
          skills?: Json | null
          updated_at?: string | null
          worker_id?: string
          worker_type?: string | null
        }
        Update: {
          available_from?: string | null
          available_to?: string | null
          created_at?: string | null
          efficiency_factor?: number | null
          is_active?: boolean | null
          is_vendor?: boolean | null
          name?: string
          shift_end?: string | null
          shift_start?: string | null
          skills?: Json | null
          updated_at?: string | null
          worker_id?: string
          worker_type?: string | null
        }
        Relationships: []
      }
      worker_skills: {
        Row: {
          certification_date: string | null
          certified: boolean | null
          created_at: string | null
          notes: string | null
          proficiency_level: number | null
          skill_id: string
          skill_name: string
          updated_at: string | null
          worker_id: string
        }
        Insert: {
          certification_date?: string | null
          certified?: boolean | null
          created_at?: string | null
          notes?: string | null
          proficiency_level?: number | null
          skill_id?: string
          skill_name: string
          updated_at?: string | null
          worker_id: string
        }
        Update: {
          certification_date?: string | null
          certified?: boolean | null
          created_at?: string | null
          notes?: string | null
          proficiency_level?: number | null
          skill_id?: string
          skill_name?: string
          updated_at?: string | null
          worker_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "worker_skills_worker_id_fkey"
            columns: ["worker_id"]
            isOneToOne: false
            referencedRelation: "worker_mast"
            referencedColumns: ["worker_id"]
          },
        ]
      }
    }
    Views: {
      setting_process_summary: {
        Row: {
          created_at: string | null
          customer_name: string | null
          gross_weight_received: number | null
          net_weight_received: number | null
          order_id: string | null
          order_reference_no: string | null
          process_name: string | null
          receipt_id: string | null
          scenario: string | null
          total_carats_broken: number | null
          total_carats_lost: number | null
          total_carats_returned: number | null
          total_carats_set: number | null
          total_stones_broken: number | null
          total_stones_lost: number | null
          total_stones_returned: number | null
          total_stones_set: number | null
          worker_name: string | null
        }
        Relationships: [
          {
            foreignKeyName: "setting_process_receipts_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["order_id"]
          },
        ]
      }
    }
    Functions: {
      add_column_if_not_exists: {
        Args: {
          _table: string
          _column: string
          _type: string
          _default?: string
        }
        Returns: undefined
      }
      check_order_references: {
        Args: { order_ids: string[] }
        Returns: {
          order_id: string
          reference_type: string
          reference_count: number
        }[]
      }
      get_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_worker_workload: {
        Args: { worker_uuid: string; date_check: string }
        Returns: number
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
