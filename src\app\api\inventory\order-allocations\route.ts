/**
 * API Routes for Order Metal Allocations
 * Handles CRUD operations for metal allocations to orders in the inventory system
 * 
 * @module api/inventory/order-allocations
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { OrderMetalAllocation } from '@/types/inventory';

/**
 * GET handler for order metal allocations
 * Retrieves all allocations or filtered by order or allocation ID
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const orderId = searchParams.get('orderId');

    let query = supabase
      .from('order_metal_allocation')
      .select(`
        *,
        metal_pools:pool_id (
          metal_type_id,
          metal_types:metal_type_id (name),
          purity
        ),
        units:uom_id (code)
      `);
    
    if (id) {
      query = query.eq('allocation_id', id);
    }

    if (orderId) {
      query = query.eq('order_id', orderId);
    }

    const { data, error } = await query.order('allocation_date', { ascending: false });

    if (error) {
      console.error('Error fetching order metal allocations:', error);
      return NextResponse.json({ error: 'Failed to fetch order metal allocations' }, { status: 500 });
    }

    // Format the response data for easier consumption
    const formattedData = data.map((item: any) => ({
      ...item,
      metal_type_name: item.metal_pools?.metal_types?.name || 'Unknown',
      purity: item.metal_pools?.purity || 0,
      uom_code: item.units?.code || '',
      // Remove nested objects
      metal_pools: undefined,
      units: undefined
    }));

    return NextResponse.json(id ? formattedData[0] : formattedData);
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

/**
 * POST handler for order metal allocations
 * Creates a new metal allocation to an order
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const body = await request.json();

    // Validate required fields
    if (!body.order_id) {
      return NextResponse.json({ error: 'Order ID is required' }, { status: 400 });
    }

    if (!body.pool_id) {
      return NextResponse.json({ error: 'Metal Pool ID is required' }, { status: 400 });
    }

    if (!body.weight || body.weight <= 0) {
      return NextResponse.json({ error: 'Weight must be a positive number' }, { status: 400 });
    }

    if (!body.uom_id) {
      return NextResponse.json({ error: 'Unit of Measure ID is required' }, { status: 400 });
    }

    // Check if the pool has enough balance
    const weightToAllocate = body.allocated_weight; // Use consistent naming
    if (!weightToAllocate || weightToAllocate <= 0) {
      return NextResponse.json({ error: 'Allocated weight must be a positive number' }, { status: 400 });
    }

    const { data: poolData, error: poolError } = await supabase
      .from('metal_pool')
      .select('current_weight, uom_id')
      .eq('pool_id', body.pool_id)
      .single();

    if (poolError) {
      console.error('Error checking metal pool:', poolError);
      return NextResponse.json({ error: 'Failed to check metal pool balance' }, { status: 500 });
    }

    if (!poolData) {
      return NextResponse.json({ error: 'Metal pool not found' }, { status: 404 });
    }

    // If the UOM is different, we need to compare based on converted weights
    // This is a simplified check - in a real system, you'd convert between units
    if (poolData.uom_id !== body.uom_id) {
      return NextResponse.json({ 
        error: 'Unit mismatch. Please use the same unit as the pool or implement a conversion.' 
      }, { status: 400 });
    }

    if (poolData.current_weight < weightToAllocate) {
      return NextResponse.json({ 
        error: `Insufficient metal in pool. Available: ${poolData.current_weight}, Requested: ${weightToAllocate}` 
      }, { status: 400 });
    }

    // Begin a transaction
    const { data: client } = await supabase.rpc('begin_transaction');
    
    try {
      // 1. Create the allocation record
      // 1. Create the allocation record
      const allocation: Partial<OrderMetalAllocation> = {
        order_id: body.order_id,
        pool_id: body.pool_id,
        allocated_weight: weightToAllocate, // Use consistent variable
        uom_id: body.uom_id,
        status: body.status || 'ALLOCATED', // Include status from body (with default)
        allocation_date: body.allocation_date || new Date().toISOString(),
        notes: body.notes || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: allocData, error: allocError } = await supabase
        .from('order_metal_allocation')
        .insert(allocation)
        .select();

      if (allocError) {
        throw new Error(`Failed to create allocation: ${allocError.message}`);
      }

      // 2. Update the metal pool balance
      const { error: updateError } = await supabase
        .from('metal_pool')
        .update({
          current_weight: poolData.current_weight - weightToAllocate, // Use consistent variable
          updated_at: new Date().toISOString()
        })
        .eq('pool_id', body.pool_id);

      if (updateError) {
        throw new Error(`Failed to update pool balance: ${updateError.message}`);
      }

      // 3. Create a metal transaction record
      const { error: transError } = await supabase
        .from('metal_transactions')
        .insert({
          pool_id: body.pool_id,
          transaction_type: 'ORDER_ALLOCATION', // Consider using a constant or enum
          weight: weightToAllocate, // Use consistent variable
          uom_id: body.uom_id,
          transaction_date: body.allocation_date || new Date().toISOString(),
          reference_id: allocData[0].allocation_id, // Assuming allocData is the result from insert
          reference_type: 'order_metal_allocation',
          notes: `Metal allocated to order ${body.order_id}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (transError) {
        throw new Error(`Failed to create transaction record: ${transError.message}`);
      }

      // Commit the transaction
      await supabase.rpc('commit_transaction');
      
      return NextResponse.json(allocData[0], { status: 201 });
    } catch (error: any) {
      // Rollback the transaction on any error
      await supabase.rpc('rollback_transaction');
      
      console.error('Transaction error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

/**
 * PUT handler for order metal allocations
 * Updates an existing metal allocation
 */
export async function PUT(request: NextRequest) {
  try {
    const supabase = createClient();
    const body = await request.json();

    // Validate required fields
    if (!body.allocation_id) {
      return NextResponse.json({ error: 'Allocation ID is required' }, { status: 400 });
    }

    if (!body.order_id) {
      return NextResponse.json({ error: 'Order ID is required' }, { status: 400 });
    }

    if (!body.pool_id) {
      return NextResponse.json({ error: 'Metal Pool ID is required' }, { status: 400 });
    }

    if (!body.weight || body.weight <= 0) {
      return NextResponse.json({ error: 'Weight must be a positive number' }, { status: 400 });
    }

    if (!body.uom_id) {
      return NextResponse.json({ error: 'Unit of Measure ID is required' }, { status: 400 });
    }

    // Get the current allocation to calculate the weight difference
    const { data: currentAlloc, error: allocError } = await supabase
      .from('order_metal_allocation')
      .select('weight, pool_id, uom_id')
      .eq('allocation_id', body.allocation_id)
      .single();

    if (allocError) {
      console.error('Error fetching current allocation:', allocError);
      return NextResponse.json({ error: 'Failed to fetch current allocation' }, { status: 500 });
    }

    if (!currentAlloc) {
      return NextResponse.json({ error: 'Allocation not found' }, { status: 404 });
    }

    // Check if the pool has changed
    const isSamePool = currentAlloc.pool_id === body.pool_id;
    const weightDifference = body.weight - currentAlloc.weight;

    // If pool changed or weight increased, check pool balance
    if (!isSamePool || weightDifference > 0) {
      const { data: poolData, error: poolError } = await supabase
        .from('metal_pool')
        .select('current_weight, uom_id')
        .eq('pool_id', body.pool_id)
        .single();

      if (poolError) {
        console.error('Error checking metal pool:', poolError);
        return NextResponse.json({ error: 'Failed to check metal pool balance' }, { status: 500 });
      }

      if (!poolData) {
        return NextResponse.json({ error: 'Metal pool not found' }, { status: 404 });
      }

      // Check if the pool has enough balance for the additional weight
      if (poolData.uom_id !== body.uom_id) {
        return NextResponse.json({ 
          error: 'Unit mismatch. Please use the same unit as the pool or implement a conversion.' 
        }, { status: 400 });
      }

      // If changing pools or increasing amount, ensure sufficient balance
      if (!isSamePool) {
        // For a pool change, need full amount in new pool
        if (poolData.current_weight < body.weight) {
          return NextResponse.json({ 
            error: `Insufficient metal in new pool. Available: ${poolData.current_weight}, Requested: ${body.weight}` 
          }, { status: 400 });
        }
      } else if (weightDifference > 0) {
        // For same pool but increased amount, just need the difference
        if (poolData.current_weight < weightDifference) {
          return NextResponse.json({ 
            error: `Insufficient metal in pool for increase. Available: ${poolData.current_weight}, Additional needed: ${weightDifference}` 
          }, { status: 400 });
        }
      }
    }

    // Begin a transaction
    const { data: client } = await supabase.rpc('begin_transaction');
    
    try {
      // 1. Update the allocation record
      const updates = {
        order_id: body.order_id,
        pool_id: body.pool_id,
        allocated_weight: body.weight,
        uom_id: body.uom_id,
        allocation_date: body.allocation_date,
        notes: body.notes || null,
        updated_at: new Date().toISOString()
      };

      const { data: updatedAlloc, error: updateError } = await supabase
        .from('order_metal_allocation')
        .update(updates)
        .eq('allocation_id', body.allocation_id)
        .select();

      if (updateError) {
        throw new Error(`Failed to update allocation: ${updateError.message}`);
      }

      // 2. Handle pool balance updates
      if (isSamePool) {
        // Same pool, just adjust by the difference
        const { error: poolUpdateError } = await supabase
          .from('metal_pool')
          .update({
            current_weight: supabase.rpc('decrement_by_value', { 
              table_name: 'metal_pool', 
              row_id: body.pool_id,
              column_name: 'current_weight',
              decrement_value: weightDifference
            }),
            updated_at: new Date().toISOString()
          })
          .eq('pool_id', body.pool_id);

        if (poolUpdateError) {
          throw new Error(`Failed to update pool balance: ${poolUpdateError.message}`);
        }
      } else {
        // Different pools, return to old pool and take from new pool
        
        // Return to old pool
        const { error: oldPoolError } = await supabase
          .from('metal_pool')
          .update({
            current_weight: supabase.rpc('increment_by_value', { 
              table_name: 'metal_pool', 
              row_id: currentAlloc.pool_id,
              column_name: 'current_weight',
              increment_value: currentAlloc.weight
            }),
            updated_at: new Date().toISOString()
          })
          .eq('pool_id', currentAlloc.pool_id);

        if (oldPoolError) {
          throw new Error(`Failed to update old pool balance: ${oldPoolError.message}`);
        }

        // Take from new pool
        const { error: newPoolError } = await supabase
          .from('metal_pool')
          .update({
            current_weight: supabase.rpc('decrement_by_value', { 
              table_name: 'metal_pool', 
              row_id: body.pool_id,
              column_name: 'current_weight',
              decrement_value: body.weight
            }),
            updated_at: new Date().toISOString()
          })
          .eq('pool_id', body.pool_id);

        if (newPoolError) {
          throw new Error(`Failed to update new pool balance: ${newPoolError.message}`);
        }
      }

      // 3. Create a new metal transaction record for the adjustment
      const { error: transError } = await supabase
        .from('metal_transactions')
        .insert({
          pool_id: body.pool_id,
          transaction_type: 'ALLOCATION_ADJUSTMENT',
          weight: Math.abs(weightDifference),
          uom_id: body.uom_id,
          transaction_date: new Date().toISOString(),
          reference_id: body.allocation_id,
          reference_type: 'order_metal_allocation',
          notes: `Adjustment to allocation for order ${body.order_id}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (transError) {
        throw new Error(`Failed to create transaction record: ${transError.message}`);
      }

      // Commit the transaction
      await supabase.rpc('commit_transaction');
      
      return NextResponse.json(updatedAlloc[0]);
    } catch (error: any) {
      // Rollback the transaction on any error
      await supabase.rpc('rollback_transaction');
      
      console.error('Transaction error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

/**
 * DELETE handler for order metal allocations
 * Deletes a metal allocation and returns metal to the pool
 */
export async function DELETE(request: NextRequest) {
  try {
    const supabase = createClient();
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Allocation ID is required' }, { status: 400 });
    }

    // Get the allocation details before deletion
    const { data: allocation, error: fetchError } = await supabase
      .from('order_metal_allocation')
      .select('pool_id, allocated_weight, uom_id, order_id')
      .eq('allocation_id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching allocation:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch allocation' }, { status: 500 });
    }

    if (!allocation) {
      return NextResponse.json({ error: 'Allocation not found' }, { status: 404 });
    }

    // Begin a transaction
    const { data: client } = await supabase.rpc('begin_transaction');
    
    try {
      // 1. Delete the allocation
      const { error: deleteError } = await supabase
        .from('order_metal_allocation')
        .delete()
        .eq('allocation_id', id);

      if (deleteError) {
        throw new Error(`Failed to delete allocation: ${deleteError.message}`);
      }

      // 2. Return metal to the pool
      const { error: updateError } = await supabase
        .from('metal_pool')
        .update({
          current_weight: supabase.rpc('increment_by_value', { 
            table_name: 'metal_pool', 
            row_id: allocation.pool_id,
            column_name: 'current_weight',
            increment_value: allocation.allocated_weight
          }),
          updated_at: new Date().toISOString()
        })
        .eq('pool_id', allocation.pool_id);

      if (updateError) {
        throw new Error(`Failed to update pool balance: ${updateError.message}`);
      }

      // 3. Create a metal transaction record for the return
      const { error: transError } = await supabase
        .from('metal_transactions')
        .insert({
          pool_id: allocation.pool_id,
          transaction_type: 'ALLOCATION_RETURN',
          weight: allocation.allocated_weight,
          uom_id: allocation.uom_id,
          transaction_date: new Date().toISOString(),
          reference_id: id,
          reference_type: 'order_metal_allocation',
          notes: `Metal returned from deleted allocation for order ${allocation.order_id}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (transError) {
        throw new Error(`Failed to create transaction record: ${transError.message}`);
      }

      // Commit the transaction
      await supabase.rpc('commit_transaction');
      
      return NextResponse.json({ success: true });
    } catch (error: any) {
      // Rollback the transaction on any error
      await supabase.rpc('rollback_transaction');
      
      console.error('Transaction error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
