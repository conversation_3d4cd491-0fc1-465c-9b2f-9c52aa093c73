'use client';

import React, { useState, useEffect } from 'react';
import { Plus } from 'lucide-react';
import { MasterTable } from '@/components/common/Table/MasterTable';
import { MasterForm } from '@/components/common/Form/MasterForm';
import { useMasterData } from '@/hooks/useMasterData';
import { MASTER_CONFIGS } from '@/config/constants';
import type { ItemTypeMaster } from '@/types/masters';

export default function ItemTypesPage() {
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<ItemTypeMaster | null>(null);
  const { data, loading, error, add, update, remove } = useMasterData<ItemTypeMaster>({
    tableName: 'item_type_mast',
  });

  // Debug logs
  useEffect(() => {
    console.log('ItemTypesPage data:', data);
    console.log('ItemTypesPage loading:', loading);
    console.log('ItemTypesPage error:', error);
  }, [data, loading, error]);

  const handleSubmit = async (values: Partial<ItemTypeMaster>) => {
    try {
      if (editingItem) {
        await update(editingItem.item_type_id, values);
      } else {
        await add(values);
      }
      setShowForm(false);
      setEditingItem(null);
    } catch (error) {
      console.error('Failed to save item type:', error);
      alert('Failed to save item type. Please try again.');
    }
  };

  const handleEdit = (item: ItemTypeMaster) => {
    setEditingItem(item);
    setShowForm(true);
  };

  const handleDelete = async (item: ItemTypeMaster) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      try {
        await remove(item.item_type_id);
      } catch (error) {
        console.error('Failed to delete item type:', error);
        alert('Failed to delete item type. Please try again.');
      }
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          {MASTER_CONFIGS.itemType.title}
        </h1>
        <button
          onClick={() => setShowForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <MasterTable
          data={data}
          config={MASTER_CONFIGS.itemType}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      </div>

      {showForm && (
        <MasterForm
          config={MASTER_CONFIGS.itemType}
          onSubmit={handleSubmit}
          onCancel={() => {
            setShowForm(false);
            setEditingItem(null);
          }}
          initialData={editingItem}
        />
      )}
    </div>
  );
}
