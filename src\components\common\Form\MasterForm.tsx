'use client';

import React from 'react';
import { FormField } from '@/types/common';

interface MasterFormProps<T> {
  config: {
    fields: FormField[];
    title: string;
  };
  initialData: T | null;
  onSubmit: (values: Partial<T>) => void;
  onCancel: () => void;
}

export function MasterForm<T>({
  config,
  initialData,
  onSubmit,
  onCancel,
}: MasterFormProps<T>) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const values: { [key: string]: any } = {};
    config.fields.forEach((field) => {
      const value = formData.get(field.id);
      if (field.type === 'number' && value) {
        values[field.id] = Number(parseFloat(value as string).toFixed(4));
      } else if (value) {
        values[field.id] = value;
      }
    });
    onSubmit(values as Partial<T>);
  };

  const getInitialValue = (field: FormField) => {
    if (!initialData) return '';
    const value = (initialData as any)[field.id];
    if (field.type === 'number') {
      return value || 0;
    }
    return value || '';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
          {initialData ? 'Edit' : 'Add'} {config.title}
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          {config.fields.map((field) => (
            <div key={field.id}>
              <label
                htmlFor={field.id}
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {field.label}
                {field.required && <span className="text-red-500 dark:text-red-400">*</span>}
              </label>
              {field.type === 'select' ? (
                <select
                  id={field.id}
                  name={field.id}
                  defaultValue={getInitialValue(field)}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required={field.required}
                >
                  <option value="">Select {field.label}</option>
                  {field.options?.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : (
                <>
                  <input
                    type={field.type}
                    id={field.id}
                    name={field.id}
                    defaultValue={getInitialValue(field)}
                    className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                    required={field.required}
                    min={field.type === 'number' ? 0 : undefined}
                    step={field.type === 'number' ? '0.0001' : undefined}
                    placeholder={field.label}
                    pattern={field.pattern}
                    maxLength={field.maxLength}
                  />
                  {field.helpText && (
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">{field.helpText}</p>
                  )}
                </>
              )}
            </div>
          ))}
          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600"
            >
              {initialData ? 'Update' : 'Create'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
