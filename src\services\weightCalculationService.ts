/**
 * @service WeightCalculationService
 * @description Advanced weight calculations for jewelry manufacturing
 * 
 * BUSINESS LOGIC:
 * - Handles gross vs net weight transitions
 * - Manages carat to gram conversions (1 carat = 0.2 grams)
 * - Calculates multi-material additions in single process
 * - Tracks dust recovery with percentage calculations
 */

export interface WeightCalculationInput {
  // Previous process output
  previousNetWeight: number; // grams
  previousGrossWeight: number; // grams
  
  // Current process additions
  diamondsAdded: number; // carats
  polkisAdded: PolkiAddition[];
  
  // Current process output
  currentGrossWeight: number; // grams
  currentNetWeight: number; // grams
  dustCollected: number; // grams
  dustRecoveryPercentage: number; // %
  
  // Process context
  processType: string;
  orderId: string;
  workerId: string;
}

export interface PolkiAddition {
  polkiId: string;
  totalCarats: number; // Total polki weight in carats
  diamondCarats: number; // Diamond portion in carats
  silverCarats: number; // Silver foil portion in carats
}

export interface WeightCalculationResult {
  // Weight analysis
  metalLossGrams: number;
  metalLossPercentage: number;
  
  // Material additions
  totalDiamondsCarats: number;
  totalDiamondsGrams: number;
  totalPolkisCarats: number;
  totalPolkisGrams: number;
  
  // Dust analysis
  dustCollectedGrams: number;
  expectedDustRecoveryGrams: number;
  actualDustRecoveryGrams: number;
  dustRecoveryEfficiency: number; // %
  
  // Final weights
  finalGrossWeight: number;
  finalNetWeight: number;
  
  // Validation
  isValid: boolean;
  validationErrors: string[];
  recommendations: string[];
}

// Constants
const CARAT_TO_GRAM_RATIO = 0.2; // 1 carat = 0.2 grams
const GRAM_TO_CARAT_RATIO = 5; // 1 gram = 5 carats

/**
 * Convert carats to grams
 */
export function caratsToGrams(carats: number): number {
  return carats * CARAT_TO_GRAM_RATIO;
}

/**
 * Convert grams to carats
 */
export function gramsToCarats(grams: number): number {
  return grams * GRAM_TO_CARAT_RATIO;
}

/**
 * Calculate comprehensive weight analysis for jewelry manufacturing
 */
export function calculateWeightAnalysis(input: WeightCalculationInput): WeightCalculationResult {
  const errors: string[] = [];
  const recommendations: string[] = [];
  
  // 1. Calculate material additions
  const totalDiamondsCarats = input.diamondsAdded;
  const totalDiamondsGrams = caratsToGrams(totalDiamondsCarats);
  
  const totalPolkisCarats = input.polkisAdded.reduce((sum, polki) => sum + polki.totalCarats, 0);
  const totalPolkisGrams = caratsToGrams(totalPolkisCarats);
  
  // 2. Calculate expected weights
  const expectedGrossWeight = input.previousNetWeight + totalDiamondsGrams + totalPolkisGrams;
  const expectedNetWeight = input.previousNetWeight; // Net weight should remain same (metal only)
  
  // 3. Calculate metal loss
  const metalLossGrams = input.previousNetWeight - input.currentNetWeight;
  const metalLossPercentage = (metalLossGrams / input.previousNetWeight) * 100;
  
  // 4. Calculate dust recovery
  const expectedDustRecoveryGrams = (metalLossGrams * input.dustRecoveryPercentage) / 100;
  const actualDustRecoveryGrams = input.dustCollected;
  const dustRecoveryEfficiency = expectedDustRecoveryGrams > 0 
    ? (actualDustRecoveryGrams / expectedDustRecoveryGrams) * 100 
    : 0;
  
  // 5. Validation checks
  let isValid = true;
  
  // Check gross weight calculation
  const grossWeightDifference = Math.abs(input.currentGrossWeight - expectedGrossWeight);
  if (grossWeightDifference > 0.1) { // Allow 0.1g tolerance
    errors.push(`Gross weight mismatch: Expected ${expectedGrossWeight.toFixed(3)}g, got ${input.currentGrossWeight.toFixed(3)}g`);
    isValid = false;
  }
  
  // Check net weight (should not increase unless metal was added)
  if (input.currentNetWeight > input.previousNetWeight + 0.05) { // Allow 0.05g tolerance
    errors.push(`Net weight increased unexpectedly: ${input.previousNetWeight.toFixed(3)}g → ${input.currentNetWeight.toFixed(3)}g`);
    isValid = false;
  }
  
  // Check metal loss percentage
  const processLossThresholds = getProcessLossThresholds(input.processType);
  if (metalLossPercentage > processLossThresholds.excessive) {
    errors.push(`Excessive metal loss: ${metalLossPercentage.toFixed(2)}% (threshold: ${processLossThresholds.excessive}%)`);
    recommendations.push('Review process technique and material handling');
  } else if (metalLossPercentage > processLossThresholds.high) {
    recommendations.push('Metal loss is higher than expected - monitor closely');
  }
  
  // Check dust recovery efficiency
  if (dustRecoveryEfficiency < 50) {
    recommendations.push('Low dust recovery efficiency - improve collection methods');
  } else if (dustRecoveryEfficiency > 100) {
    errors.push('Dust recovery exceeds expected amount - check measurements');
  }
  
  // 6. Generate recommendations
  if (metalLossPercentage < processLossThresholds.excellent) {
    recommendations.push('Excellent work! Loss is below expected threshold');
  }
  
  if (dustRecoveryEfficiency > 80) {
    recommendations.push('Good dust collection efficiency');
  }
  
  return {
    metalLossGrams,
    metalLossPercentage,
    totalDiamondsCarats,
    totalDiamondsGrams,
    totalPolkisCarats,
    totalPolkisGrams,
    dustCollectedGrams: input.dustCollected,
    expectedDustRecoveryGrams,
    actualDustRecoveryGrams,
    dustRecoveryEfficiency,
    finalGrossWeight: input.currentGrossWeight,
    finalNetWeight: input.currentNetWeight,
    isValid,
    validationErrors: errors,
    recommendations
  };
}

/**
 * Get process-specific loss thresholds
 */
function getProcessLossThresholds(processType: string) {
  const thresholds: Record<string, { excellent: number; good: number; acceptable: number; high: number; excessive: number }> = {
    'filing': { excellent: 1.0, good: 2.0, acceptable: 3.0, high: 4.0, excessive: 5.0 },
    'setting': { excellent: 0.5, good: 1.0, acceptable: 1.5, high: 2.0, excessive: 3.0 },
    'polishing': { excellent: 1.5, good: 2.5, acceptable: 3.5, high: 4.5, excessive: 6.0 },
    'sprue_cutting': { excellent: 2.0, good: 3.0, acceptable: 4.0, high: 5.0, excessive: 7.0 },
    'default': { excellent: 1.0, good: 2.0, acceptable: 3.0, high: 4.0, excessive: 5.0 }
  };

  return thresholds[processType.toLowerCase()] || thresholds.default;
}

/**
 * Example calculation for your scenario
 */
export function exampleFilingToSettingCalculation() {
  // Filing Process Output
  const filingOutput = {
    previousNetWeight: 9.7, // grams (net = gross after filing)
    previousGrossWeight: 9.7, // grams
    
    // Setting Process Additions
    diamondsAdded: 1.0, // carats
    polkisAdded: [{
      polkiId: 'polki-001',
      totalCarats: 0.45,
      diamondCarats: 0.15,
      silverCarats: 0.30
    }],
    
    // Setting Process Output
    currentGrossWeight: 9.9, // grams (9.7 + 0.2g from stones)
    currentNetWeight: 9.7, // grams (metal weight unchanged)
    dustCollected: 0.0, // grams (no metal loss in setting)
    dustRecoveryPercentage: 0,
    
    processType: 'setting',
    orderId: 'ORD-12345',
    workerId: 'worker-001'
  };
  
  return calculateWeightAnalysis(filingOutput);
}

/**
 * Create process transition record
 */
export interface ProcessTransition {
  fromProcessId: string;
  toProcessId: string;
  orderId: string;
  workerId: string;
  
  // Material flow
  materialCarriedForward: {
    netWeightGrams: number;
    grossWeightGrams: number;
    description: string;
  };
  
  // New materials added
  materialsAdded: {
    diamonds?: { carats: number; description: string; };
    polkis?: PolkiAddition[];
    findings?: { id: string; weightGrams: number; description: string; }[];
  };
  
  // Process output
  outputWeights: {
    grossWeightGrams: number;
    netWeightGrams: number;
  };
  
  // Loss tracking
  lossAnalysis: WeightCalculationResult;
}

/**
 * Create a process transition record for order tracking
 */
export function createProcessTransition(
  fromProcess: string,
  toProcess: string,
  weightCalculation: WeightCalculationResult,
  orderId: string,
  workerId: string
): ProcessTransition {
  return {
    fromProcessId: fromProcess,
    toProcessId: toProcess,
    orderId,
    workerId,
    materialCarriedForward: {
      netWeightGrams: weightCalculation.finalNetWeight,
      grossWeightGrams: weightCalculation.finalGrossWeight,
      description: `Material from ${fromProcess} to ${toProcess}`
    },
    materialsAdded: {
      diamonds: weightCalculation.totalDiamondsCarats > 0 ? {
        carats: weightCalculation.totalDiamondsCarats,
        description: `${weightCalculation.totalDiamondsCarats} carats diamonds added`
      } : undefined,
      polkis: weightCalculation.totalPolkisCarats > 0 ? [] : undefined // TODO: Add polki details
    },
    outputWeights: {
      grossWeightGrams: weightCalculation.finalGrossWeight,
      netWeightGrams: weightCalculation.finalNetWeight
    },
    lossAnalysis: weightCalculation
  };
}
