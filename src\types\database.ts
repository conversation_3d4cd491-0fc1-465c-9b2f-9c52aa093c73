// Import the generated Database type from the local supabase types file
import { Database } from './supabase' 

// Re-export the Database type for use in client creation
export type { Database } 

export type Tables = Database['public']['Tables']

// Order Types
export type Order = Tables['orders']['Row']
export type OrderInsert = Tables['orders']['Insert']
export type OrderUpdate = Tables['orders']['Update']

// Process Types
export type ProcessMast = Tables['process_mast']['Row']
export type ProcessTracking = Tables['process_tracking']['Row']
export type ProcessTemplate = Tables['process_templates']['Row']

// Worker Types
export type WorkerMast = Tables['worker_mast']['Row']
export type WorkerSkills = Tables['worker_skills']['Row']

// Master Table Types
export type CustomerMast = Tables['customer_mast']['Row']
// Note: metal_colour_mast and purity_mast don't exist in current schema
// export type MetalColourMast = Tables['metal_colour_mast']['Row']
// export type PurityMast = Tables['purity_mast']['Row']
export type ItemTypeMast = Tables['item_type_mast']['Row']
export type OrderCategoryMast = Tables['order_category_mast']['Row']
export type ThirdPartyCustMast = Tables['third_party_cust_mast']['Row']

// Legacy Types (for backward compatibility)
export type GoldColourMast = Tables['gold_colour_mast']['Row']
export type KaratMast = Tables['karat_mast']['Row']

// Enums
export type ProcessStatus = 'PENDING' | 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'DELAYED' | 'ON_HOLD'
export type OrderStatus = 'active' | 'completed' | 'cancelled' | 'on_hold'
export type HolidayType = 'FULL_DAY' | 'HALF_DAY' | 'UNSCHEDULED'

// Workshop Types
export type WorkshopConfig = Tables['workshop_config']['Row']
export type HolidayCalendar = Tables['holiday_calendar']['Row']

// Utility type for handling nullable fields
export type NonNullable<T> = {
    [P in keyof T]: Exclude<T[P], null>
}

// Utility type for handling optional fields
export type Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>
