/**
 * ProcessQueue Component
 * Displays a list of unscheduled processes that can be dragged to workers.
 * 
 * @module components/scheduling
 */
'use client';

import React from 'react';

// TODO: This component should receive a list of unscheduled processes as props
// and render them as draggable items. For now, it's a placeholder.

export const ProcessQueue: React.FC = () => {
  return (
    <div className="space-y-2">
      {/* Placeholder content */}
      <p className="text-sm text-gray-500">Unscheduled processes will appear here.</p>
    </div>
  );
};

export default ProcessQueue;
