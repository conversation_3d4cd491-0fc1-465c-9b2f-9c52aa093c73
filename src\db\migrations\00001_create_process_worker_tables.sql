-- Drop tables if they exist (in correct order due to dependencies)
DROP TABLE IF EXISTS worker_process_skill CASCADE;
DROP TABLE IF EXISTS process_mast CASCADE;
DROP TABLE IF EXISTS worker_mast CASCADE;

-- Create process master table
CREATE TABLE IF NOT EXISTS process_mast (
    process_id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
    description varchar(100) NOT NULL,
    is_active boolean DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create worker master table
CREATE TABLE IF NOT EXISTS worker_mast (
    worker_id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
    name varchar(100) NOT NULL,
    is_active boolean DEFAULT true,
    is_vendor boolean DEFAULT false,
    shift_start time,
    shift_end time,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create worker process skill table
CREATE TABLE IF NOT EXISTS worker_process_skill (
    worker_id uuid NOT NULL REFERENCES worker_mast(worker_id),
    process_id uuid NOT NULL REFERENCES process_mast(process_id),
    skill_level integer CHECK (skill_level BETWEEN 1 AND 5),
    efficiency_factor decimal(3,2) DEFAULT 1.00,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (worker_id, process_id)
);

-- Add table comments
COMMENT ON TABLE process_mast IS 'Master table for jewelry manufacturing processes';
COMMENT ON TABLE worker_mast IS 'Master table for workers and vendors';
COMMENT ON TABLE worker_process_skill IS 'Mapping of worker/vendor skills to processes';
