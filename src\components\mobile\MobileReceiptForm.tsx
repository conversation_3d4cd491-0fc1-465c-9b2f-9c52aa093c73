/**
 * @module components/mobile/MobileReceiptForm
 * @description Mobile-optimized receipt form for workshop floor use
 * 
 * Mobile UX Features:
 * - Large touch targets (minimum 44px)
 * - Simplified navigation with minimal scrolling
 * - Voice input for notes and descriptions
 * - Offline capability with sync when available
 * - Quick access to common actions
 * - Haptic feedback for interactions
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  SmartphoneIcon,
  MicIcon,
  MicOffIcon,
  WifiIcon,
  WifiOffIcon,
  CheckCircleIcon,
  AlertTriangleIcon,
  ScaleIcon,
  SaveIcon,
  SendIcon,
  VolumeXIcon
} from 'lucide-react';

interface MobileReceiptFormProps {
  workerId?: string;
  processId?: string;
  customerId?: string;
  onSuccess?: () => void;
}

interface OfflineAction {
  id: string;
  type: 'receipt' | 'issue' | 'transfer';
  data: any;
  timestamp: string;
}

export function MobileReceiptForm({ 
  workerId, 
  processId, 
  customerId, 
  onSuccess 
}: MobileReceiptFormProps) {
  // Connection state
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [queuedActions, setQueuedActions] = useState<OfflineAction[]>([]);
  
  // Voice input state
  const [isListening, setIsListening] = useState(false);
  const [voiceSupported, setVoiceSupported] = useState(false);
  const [voiceNotes, setVoiceNotes] = useState('');
  
  // Form state
  const [receivedWeight, setReceivedWeight] = useState(0);
  const [dustCollected, setDustCollected] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize voice recognition and offline detection
  useEffect(() => {
    // Check for voice support
    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      setVoiceSupported(true);
    }

    // Online/offline detection
    const handleOnline = () => {
      setIsOnline(true);
      syncQueuedActions();
    };
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Load queued actions from localStorage
    const saved = localStorage.getItem('jwl_queued_actions');
    if (saved) {
      setQueuedActions(JSON.parse(saved));
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Save queued actions to localStorage
  useEffect(() => {
    localStorage.setItem('jwl_queued_actions', JSON.stringify(queuedActions));
  }, [queuedActions]);

  const startVoiceInput = () => {
    if (!voiceSupported || typeof window === 'undefined') return;

    const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
    const recognition = new SpeechRecognition();
    
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';

    recognition.onstart = () => {
      setIsListening(true);
      // Haptic feedback if available
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    };

    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript;
      setVoiceNotes(prev => prev + (prev ? ' ' : '') + transcript);
    };

    recognition.onerror = (event: any) => {
      console.error('Voice recognition error:', event.error);
      setIsListening(false);
    };

    recognition.onend = () => {
      setIsListening(false);
    };

    recognition.start();
  };

  const stopVoiceInput = () => {
    setIsListening(false);
  };

  const syncQueuedActions = async () => {
    if (!isOnline || queuedActions.length === 0) return;

    try {
      // Process queued actions
      for (const action of queuedActions) {
        // In real implementation, send to API
        console.log('Syncing action:', action);
      }
      
      // Clear queue after successful sync
      setQueuedActions([]);
      
      // Haptic feedback for successful sync
      if (navigator.vibrate) {
        navigator.vibrate([100, 50, 100]);
      }
    } catch (error) {
      console.error('Error syncing queued actions:', error);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      const receiptData = {
        workerId,
        processId,
        customerId,
        receivedWeight,
        dustCollected,
        notes: voiceNotes,
        timestamp: new Date().toISOString()
      };

      if (isOnline) {
        // Submit immediately
        console.log('Submitting receipt:', receiptData);
        
        // Haptic feedback for success
        if (navigator.vibrate) {
          navigator.vibrate([200, 100, 200]);
        }
        
        if (onSuccess) onSuccess();
      } else {
        // Queue for later sync
        const queuedAction: OfflineAction = {
          id: Date.now().toString(),
          type: 'receipt',
          data: receiptData,
          timestamp: new Date().toISOString()
        };
        
        setQueuedActions(prev => [...prev, queuedAction]);
        
        // Haptic feedback for queued
        if (navigator.vibrate) {
          navigator.vibrate([100, 50, 100, 50, 100]);
        }
      }

    } catch (error) {
      console.error('Error submitting receipt:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = () => {
    const draftData = {
      receivedWeight,
      dustCollected,
      notes: voiceNotes,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('jwl_receipt_draft', JSON.stringify(draftData));
    
    // Haptic feedback
    if (navigator.vibrate) {
      navigator.vibrate(100);
    }
  };

  return (
    <div className="mobile-container min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile Header */}
      <div className="sticky top-0 z-10 bg-white dark:bg-gray-800 border-b shadow-sm">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <SmartphoneIcon className="w-6 h-6 text-blue-600" />
            <div>
              <h1 className="text-lg font-bold">Material Receipt</h1>
              <p className="text-sm text-gray-600">Mobile Workshop</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {isOnline ? (
              <Badge className="bg-green-100 text-green-800">
                <WifiIcon className="w-3 h-3 mr-1" />
                Online
              </Badge>
            ) : (
              <Badge className="bg-red-100 text-red-800">
                <WifiOffIcon className="w-3 h-3 mr-1" />
                Offline
              </Badge>
            )}
            {queuedActions.length > 0 && (
              <Badge variant="secondary">
                {queuedActions.length} queued
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-4 space-y-6">
        {/* Offline Queue Status */}
        {!isOnline && queuedActions.length > 0 && (
          <Alert className="bg-yellow-50 border-yellow-200">
            <AlertTriangleIcon className="w-4 h-4" />
            <AlertDescription>
              {queuedActions.length} actions queued for sync when online
            </AlertDescription>
          </Alert>
        )}

        {/* Touch-Optimized Weight Input */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2">
              <ScaleIcon className="w-5 h-5" />
              Weight Entry
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Received Weight */}
            <div className="space-y-3">
              <Label htmlFor="received-weight" className="text-lg font-medium">
                Received Weight (g)
              </Label>
              <Input
                id="received-weight"
                type="number"
                step="0.001"
                value={receivedWeight}
                onChange={(e) => setReceivedWeight(parseFloat(e.target.value) || 0)}
                className="text-2xl h-16 text-center font-bold"
                placeholder="0.000"
              />
            </div>

            {/* Dust Collected */}
            <div className="space-y-3">
              <Label htmlFor="dust-collected" className="text-lg font-medium">
                Dust Collected (g)
              </Label>
              <Input
                id="dust-collected"
                type="number"
                step="0.001"
                value={dustCollected}
                onChange={(e) => setDustCollected(parseFloat(e.target.value) || 0)}
                className="text-2xl h-16 text-center font-bold"
                placeholder="0.000"
              />
            </div>
          </CardContent>
        </Card>

        {/* Voice Notes */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2">
              <MicIcon className="w-5 h-5" />
              Voice Notes
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {voiceSupported ? (
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Button
                    size="lg"
                    variant={isListening ? "destructive" : "default"}
                    onClick={isListening ? stopVoiceInput : startVoiceInput}
                    className="flex-1 h-14"
                  >
                    {isListening ? (
                      <>
                        <MicOffIcon className="w-5 h-5 mr-2" />
                        Stop Recording
                      </>
                    ) : (
                      <>
                        <MicIcon className="w-5 h-5 mr-2" />
                        Start Recording
                      </>
                    )}
                  </Button>
                </div>
                
                {voiceNotes && (
                  <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
                    <p className="text-sm">{voiceNotes}</p>
                  </div>
                )}
              </div>
            ) : (
              <Alert>
                <VolumeXIcon className="w-4 h-4" />
                <AlertDescription>
                  Voice input not supported on this device
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Large Action Buttons */}
        <div className="space-y-3 pb-6">
          <Button
            size="lg"
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="w-full h-16 text-lg font-semibold bg-green-600 hover:bg-green-700"
          >
            {isSubmitting ? (
              'Processing...'
            ) : isOnline ? (
              <>
                <SendIcon className="w-5 h-5 mr-2" />
                Complete Receipt
              </>
            ) : (
              <>
                <SaveIcon className="w-5 h-5 mr-2" />
                Queue for Sync
              </>
            )}
          </Button>

          <Button
            size="lg"
            variant="outline"
            onClick={handleSaveDraft}
            className="w-full h-14 text-lg"
          >
            <SaveIcon className="w-5 h-5 mr-2" />
            Save Draft
          </Button>
        </div>
      </div>
    </div>
  );
}
