/**
 * Order Service
 * 
 * Handles order creation, retrieval, update and deletion operations
 * Implements proper order ID generation using item type suffix
 */

import { supabase } from '@/lib/db';
import { Order, OrderStatus } from '@/types/orders';
import { ItemTypeMaster } from '@/types/masters';
import { generateOrderId, getLastOrderSequence, isOrderIdUnique } from './orderIdService';

/**
 * Creates a new order with auto-generated order ID based on item type
 * 
 * @param {Omit<Order, 'order_id' | 'created_at' | 'updated_at'>} orderData Order data without ID and timestamps
 * @returns {Promise<Order>} Created order with ID
 * @throws {Error} If order creation fails
 */
export async function createOrder(orderData: Omit<Order, 'order_id' | 'created_at' | 'updated_at'>): Promise<Order> {
  try {
    // 1. Get item type details to extract suffix
    const { data: itemType, error: itemTypeError } = await supabase
      .from('item_type_mast')
      .select('*')
      .eq('item_type_id', orderData.item_type_id)
      .single();

    if (itemTypeError) {
      console.error('Error fetching item type:', itemTypeError);
      throw new Error(`Item type not found for ID: ${orderData.item_type_id}`);
    }

    // 2. Get current year-month for sequence
    const date = new Date();
    const yearMonth = `${date.getFullYear().toString().slice(-2)}${(date.getMonth() + 1)
      .toString()
      .padStart(2, '0')}`;

    // 3. Get last sequence number for current month
    const lastSequence = await getLastOrderSequence(yearMonth);
    
    // 4. Generate new order ID
    let orderId = generateOrderId(lastSequence, itemType as ItemTypeMaster);
    
    // 5. Ensure order ID is unique (in case of concurrency issues)
    let isUnique = await isOrderIdUnique(orderId);
    let retryCount = 0;
    
    // If not unique, try the next sequence number
    while (!isUnique && retryCount < 5) {
      orderId = generateOrderId(lastSequence + retryCount + 1, itemType as ItemTypeMaster);
      isUnique = await isOrderIdUnique(orderId);
      retryCount++;
    }
    
    if (!isUnique) {
      throw new Error('Failed to generate a unique order ID after multiple attempts');
    }

    // 6. Insert order with generated ID
    const { data, error } = await supabase
      .from('orders')
      .insert([
        {
          ...orderData,
          order_id: orderId,
          order_reference_no: orderId, // Set order reference number same as order ID
          order_status: orderData.order_status || OrderStatus.Pending,
          is_repeat_order: false, // Default for new orders
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
}

/**
 * Updates an existing order
 * 
 * @param {string} orderId Order ID
 * @param {Partial<Order>} orderData Order data to update
 * @returns {Promise<Order>} Updated order
 * @throws {Error} If order update fails
 */
export async function updateOrder(orderId: string, orderData: Partial<Order>): Promise<Order> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .update({
        ...orderData,
        updated_at: new Date().toISOString()
      })
      .eq('order_id', orderId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating order:', error);
    throw error;
  }
}

/**
 * Deletes an order
 * 
 * @param {string} orderId Order ID
 * @throws {Error} If order deletion fails
 */
export async function deleteOrder(orderId: string): Promise<void> {
  try {
    const { error } = await supabase.from('orders').delete().eq('order_id', orderId);
    if (error) throw error;
  } catch (error) {
    console.error('Error deleting order:', error);
    throw error;
  }
}

/**
 * Gets an order by ID with related entities
 * 
 * @param {string} orderId Order ID
 * @returns {Promise<Order>} Order with related data
 * @throws {Error} If order retrieval fails
 */
export async function getOrder(orderId: string): Promise<Order> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        item_type:item_type_mast!inner(item_type_id, description, suffix),
        karat:karat_mast!inner(karat_id, description),
        gold_color:gold_colour_mast!inner(gold_colour_id, description),
        customer:customer_mast!inner(customer_id, description),
        order_category:order_category_mast!inner(order_category_id, description),
        reference_order:orders(order_id, order_reference_no, style_code)
      `)
      .eq('order_id', orderId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching order:', error);
    throw error;
  }
}

/**
 * Gets all orders with related entities
 * 
 * @returns {Promise<Order[]>} List of orders with related data
 * @throws {Error} If orders retrieval fails
 */
export async function getOrders(): Promise<Order[]> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        item_type:item_type_mast!inner(item_type_id, description, suffix),
        karat:karat_mast!inner(karat_id, description),
        gold_color:gold_colour_mast!inner(gold_colour_id, description),
        customer:customer_mast!inner(customer_id, description),
        order_category:order_category_mast!inner(order_category_id, description),
        reference_order:orders(order_id, order_reference_no, style_code)
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }
}
