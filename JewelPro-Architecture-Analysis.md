# JewelPro Application - Comprehensive Architecture Analysis

## Executive Summary

JewelPro is a **production-ready jewelry manufacturing workflow management system** built with Next.js 14, TypeScript, and Supabase. The application focuses on **order-centric material loss tracking** across the complete jewelry production lifecycle, from raw material receipt to finished product delivery.

## Technology Stack

### Frontend Framework
- **Next.js 14** with App Router for modern React development
- **TypeScript** for type safety and developer experience
- **Tailwind CSS** with Shadcn/ui components for consistent styling
- **Radix UI** primitives for accessible component foundation

### Backend & Database
- **Supabase** (PostgreSQL) with Row Level Security for data management
- **35+ database tables** with comprehensive schema design
- **Real-time subscriptions** for live data updates
- **Automated migrations** with version tracking

### State Management & Data Fetching
- **Zustand** for client-side state management
- **TanStack Query** for server state management and caching
- **React Hook Form** with Zod validation for form handling
- **Custom hooks** for reusable business logic

### Authentication & Security
- **Supabase Auth** with role-based access control
- **Three user roles**: admin, data_entry, customer
- **Row Level Security** policies for data isolation
- **JWT-based authentication** with automatic token refresh

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── inventory/         # Inventory management
│   ├── masters/           # Master data management
│   ├── materials/         # Material operations
│   ├── orders/            # Order management
│   └── reports/           # Reporting and analytics
├── components/            # React components
│   ├── auth/              # Authentication components
│   ├── common/            # Shared components
│   ├── dashboard/         # Dashboard widgets
│   ├── forms/             # Form components
│   ├── inventory/         # Inventory components
│   ├── materials/         # Material handling components
│   ├── orders/            # Order management components
│   ├── reports/           # Reporting components
│   └── ui/                # Base UI components (Shadcn)
├── services/              # Business logic layer
├── hooks/                 # Custom React hooks
├── store/                 # Zustand state stores
├── types/                 # TypeScript type definitions
├── lib/                   # Utility libraries
├── config/                # Configuration files
└── constants/             # Application constants
```

## Core Business Modules

### 1. Order Management System
**Location**: `src/services/orderService.ts`, `src/store/orderStore.ts`

**Key Features**:
- Auto-generated order IDs with item type suffixes
- Order lifecycle management (pending → active → completed)
- Reference order support for repeat orders
- Integration with customer and item type masters

**Business Logic**:
- Order ID format: `YYMM001XX` (YY=year, MM=month, 001=sequence, XX=item suffix)
- Automatic sequence number generation with concurrency handling
- Order status tracking with state transitions

### 2. Material Transaction System
**Location**: `src/services/materialTransactionService.ts`

**Key Features**:
- Material issue and receipt tracking
- Customer-segregated inventory management
- Loss percentage calculation and monitoring
- Dust collection and recovery tracking

**Business Logic**:
- Tracks material flow: issue → process → receipt
- Calculates actual vs expected loss percentages
- Maintains customer-specific material pools
- Supports three material types: metals, stones, findings

### 3. Inventory Management
**Location**: `src/services/stoneInventoryService.ts`, `src/services/findingsInventoryService.ts`

**Key Features**:
- Multi-location inventory tracking (Customers, Safe, Central, External Vendors, Floor)
- Stone inventory with type, shape, size, quality dimensions
- Polki findings with composite stone tracking
- Status-based inventory management (available, allocated, issued, consumed)

**Business Logic**:
- Customer-specific inventory segregation
- Order allocation and tracking
- Location-based inventory movement
- Quality and size-based stone categorization

### 4. Process Management
**Location**: `src/services/settingProcessService.ts`, `src/store/processStore.ts`

**Key Features**:
- Process template management
- Worker assignment and tracking
- Process-specific material handling
- Setting process with stone disposition tracking

**Business Logic**:
- Three setting scenarios: all_set, all_returned, mixed
- Stone disposition tracking (set, returned, broken, lost)
- Process efficiency monitoring
- Worker skill-based assignments

### 5. Dust Management System
**Location**: `src/services/dustManagementService.ts`

**Key Features**:
- Dust parcel creation and tracking
- Batch refining operations
- Recovery rate monitoring
- Cost tracking for refining operations

**Business Logic**:
- Dust collection from various processes
- Batch creation for efficient refining
- Recovery rate analysis (expected vs actual)
- Purity estimation and tracking

### 6. Weight Calculation Engine
**Location**: `src/services/weightCalculationService.ts`

**Key Features**:
- Gross vs net weight transitions
- Carat to gram conversions (1 carat = 0.2 grams)
- Multi-material weight calculations
- Loss analysis and recommendations

**Business Logic**:
- Tracks weight changes through processes
- Calculates material additions (diamonds, polkis)
- Monitors metal loss and dust recovery
- Provides efficiency recommendations

## Database Schema Overview

### Master Tables (15 tables)
- Customer management (`customer_mast`)
- Item types with order suffixes (`item_type_mast`)
- Karat and gold color specifications (`karat_mast`, `gold_colour_mast`)
- Stone specifications (`stone_type_mast`, `stone_shape_mast`, `stone_size_mast`, `stone_quality_mast`)
- Process and worker management (`process_mast`, `worker_mast`)
- UOM and location masters (`uom_mast`, `location_mast`)

### Transaction Tables (20+ tables)
- Orders and order tracking (`orders`, `order_tracking`)
- Material transactions (`material_transactions`)
- Inventory tables (`stone_inventory`, `findings_mast`, `metal_pool`)
- Process tracking (`process_tracking`, `process_templates`)
- Dust management (`dust_parcels_enhanced`, `dust_refine_batches`)
- Receipt and issue tracking (`customer_material_receipt`, various receipt item tables)

## Authentication & Authorization

### User Roles
1. **Admin**: Full system access, user management, system configuration
2. **Data Entry**: Order creation, material transactions, process updates
3. **Customer**: Limited access to own orders and materials

### Security Implementation
- **Row Level Security** policies ensure customer data isolation
- **JWT tokens** with automatic refresh for session management
- **Role-based middleware** for API route protection
- **Supabase Auth** integration with custom user metadata

## State Management Architecture

### Client State (Zustand)
- **Order Store**: Order CRUD operations and state
- **Worker Store**: Worker management and assignments
- **Process Store**: Process tracking and updates
- **Auth Store**: Authentication state and user management

### Server State (TanStack Query)
- **Master data caching** for dropdown populations
- **Real-time updates** for inventory and process changes
- **Optimistic updates** for better user experience
- **Background refetching** for data consistency

## Specialized Services

### Style Code Management
**Location**: `src/services/styleCodeService.ts`
- Automatic style code generation
- Style matching for repeat orders
- Complexity level assessment
- Processing time estimation

### Order ID Generation
**Location**: `src/services/orderIdService.ts`
- Sequential order ID generation
- Concurrency handling for unique IDs
- Item type suffix integration
- Year-month based sequencing

### Location Services
**Location**: `src/services/locationService.ts`
- Multi-location inventory queries
- Location-based material filtering
- Inventory movement tracking
- Cross-location transfers

## Configuration & Utilities

### Environment Configuration
**Location**: `src/config/env.ts`
- Supabase connection parameters
- Environment-specific settings
- API endpoint configurations

### MCP Integration
**Location**: `src/config/mcp.ts`, `mcp-config.json`
- Model Context Protocol configuration
- Direct PostgreSQL connection for external tools
- Database introspection capabilities

### Utility Libraries
**Location**: `src/lib/utils/`
- Date formatting utilities
- Weight and currency formatting
- Class name merging (cn function)
- API error handling

## Testing & Development

### Mock Authentication
**Location**: `src/middleware/withAuth.ts`
- Development-mode authentication bypass
- Mock admin user for testing
- Role-based access simulation

### Migration Management
**Location**: `scripts/apply-migration.ts`
- Automated database migrations
- Version tracking and rollback support
- SQL execution with error handling

## Key Business Processes

### 1. Order-to-Delivery Workflow
1. **Order Creation** → Auto-generate order ID with item suffix
2. **Material Receipt** → Customer materials received and inventoried
3. **Process Assignment** → Workers assigned to specific processes
4. **Material Issue** → Materials issued to workers for processing
5. **Process Execution** → Work completed with material tracking
6. **Material Receipt** → Processed materials received back
7. **Loss Analysis** → Calculate and analyze material losses
8. **Quality Control** → Final inspection and approval
9. **Delivery** → Finished product delivered to customer

### 2. Material Loss Tracking
1. **Issue Weight Recording** → Precise weight measurement at issue
2. **Process Execution** → Work performed with dust collection
3. **Receipt Weight Recording** → Weight measurement at receipt
4. **Loss Calculation** → Automatic loss percentage calculation
5. **Dust Recovery** → Dust collection and parcel creation
6. **Batch Refining** → Dust parcels batched for refining
7. **Recovery Analysis** → Actual vs expected recovery rates
8. **Cost Allocation** → Refining costs allocated to customers

### 3. Inventory Management
1. **Material Receipt** → Customer materials received and categorized
2. **Location Assignment** → Materials assigned to specific locations
3. **Quality Assessment** → Materials graded and quality-checked
4. **Order Allocation** → Materials allocated to specific orders
5. **Issue Processing** → Materials issued for production
6. **Status Tracking** → Real-time status updates throughout process
7. **Return Processing** → Unused materials returned to inventory
8. **Loss Recording** → Damaged or lost materials properly recorded

This architecture supports a complete jewelry manufacturing operation with comprehensive tracking, loss management, and process optimization capabilities.

## Orphaned/Standalone Modules Analysis

### Migration and Deployment Scripts
**Location**: `scripts/apply-migration.ts`, `scripts/deploy-migration.ts`
- **Purpose**: Database migration management and deployment automation
- **Status**: Standalone utility modules
- **Dependencies**: Direct Supabase connection, file system operations
- **Usage**: Development and deployment workflows

### MCP (Model Context Protocol) Configuration
**Location**: `src/config/mcp.ts`, `src/config/mcp.json`, `mcp-config.json`
- **Purpose**: External database introspection and AI tool integration
- **Status**: Specialized configuration for external tools
- **Dependencies**: PostgreSQL connection string, environment variables
- **Usage**: AI-assisted development and database analysis

### Store Utilities
**Location**: `src/store/index.ts`, `src/store/types.ts`
- **Purpose**: Centralized store exports and type definitions
- **Status**: Utility modules for state management
- **Dependencies**: Individual store modules
- **Usage**: Simplified imports and type consistency

### Constants and Routes
**Location**: `src/constants/routes.ts`, `src/constants/api.ts`
- **Purpose**: Application routing and API endpoint definitions
- **Status**: Configuration modules
- **Dependencies**: None (pure constants)
- **Usage**: Navigation and API consistency

### Database Utilities
**Location**: `src/db/utils.ts`, `src/db/errors/`, `src/db/migrations/`
- **Purpose**: Database operation utilities and error handling
- **Status**: Support modules for data layer
- **Dependencies**: Supabase client, error classes
- **Usage**: Database operations and error management

### Type Definition Files
**Location**: `src/types/common.ts`, `src/types/*.ts` (various specific types)
- **Purpose**: Shared type definitions and domain-specific types
- **Status**: Type definition modules
- **Dependencies**: Database schema types
- **Usage**: Type safety across application

## Module Dependency Analysis

### Highly Connected Modules (Core)
1. **orderService.ts** - Central to order management, connects to 8+ modules
2. **materialTransactionService.ts** - Core material tracking, connects to 6+ modules
3. **supabase client (lib/db.ts)** - Database access layer, used by all services
4. **type definitions** - Used across all modules for type safety

### Moderately Connected Modules (Supporting)
1. **stoneInventoryService.ts** - Inventory management, connects to 4+ modules
2. **dustManagementService.ts** - Specialized dust tracking, connects to 3+ modules
3. **authStore.ts** - Authentication state, connects to 5+ modules
4. **utility functions** - Formatting and helper functions, used by multiple components

### Loosely Connected Modules (Specialized)
1. **weightCalculationService.ts** - Specialized calculations, used by setting process
2. **settingProcessService.ts** - Process-specific logic, limited connections
3. **mcp configuration** - External tool integration, standalone
4. **migration scripts** - Development utilities, standalone

### Isolated Modules (Utilities)
1. **Mobile components** - Responsive design utilities
2. **Debug components** - Development debugging tools
3. **Scheduling hooks** - Production scheduling utilities
4. **Error classes** - Error handling definitions

## Business Process Flow Diagrams

### 1. Complete Order Lifecycle
```
Customer Material Receipt → Order Creation → Style Code Assignment →
Process Planning → Material Issue → Work Execution → Material Receipt →
Loss Analysis → Quality Control → Final Delivery
```

### 2. Material Loss Tracking Flow
```
Material Issue (Weight A) → Process Execution → Dust Collection →
Material Receipt (Weight B) → Loss Calculation (A-B) →
Dust Parcel Creation → Batch Refining → Recovery Analysis
```

### 3. Inventory Management Flow
```
Material Receipt → Quality Assessment → Location Assignment →
Order Allocation → Issue Processing → Status Tracking →
Return Processing → Loss Recording
```

## Performance and Scalability Considerations

### Database Optimization
- **Indexed columns**: order_id, customer_id, worker_id, process_id
- **Partitioning strategy**: Orders by year-month for historical data
- **Row Level Security**: Customer data isolation with minimal performance impact
- **Connection pooling**: Supabase handles connection management

### Frontend Performance
- **Code splitting**: Next.js automatic route-based splitting
- **State management**: Zustand for minimal re-renders
- **Data fetching**: TanStack Query for caching and background updates
- **Component optimization**: React.memo for expensive components

### Scalability Patterns
- **Microservice ready**: Services layer can be extracted to separate APIs
- **Database scaling**: PostgreSQL read replicas for reporting
- **Caching strategy**: Redis integration possible for high-frequency data
- **Background processing**: Queue system for heavy operations (dust refining, reports)

## Security Architecture

### Authentication Flow
1. **User login** → Supabase Auth verification
2. **JWT token** → Stored in secure HTTP-only cookies
3. **Role assignment** → Fetched from user_roles table
4. **Route protection** → Middleware validates token and role
5. **API protection** → withAuth wrapper for API routes

### Data Security
- **Row Level Security** policies ensure customer data isolation
- **Encrypted connections** to Supabase with SSL/TLS
- **Environment variables** for sensitive configuration
- **Input validation** with Zod schemas for all forms
- **SQL injection protection** through Supabase's prepared statements

### Access Control Matrix
| Role | Orders | Materials | Inventory | Reports | Admin |
|------|--------|-----------|-----------|---------|-------|
| Admin | Full | Full | Full | Full | Full |
| Data Entry | CRUD | CRUD | Read/Update | Read | None |
| Customer | Own Only | Own Only | Own Only | Own Only | None |

## Integration Points

### External System Integration
1. **Supabase Auth** - User authentication and management
2. **PostgreSQL** - Primary data storage with real-time subscriptions
3. **MCP Server** - Database introspection for AI tools
4. **File Storage** - Supabase Storage for images and documents

### API Integration Patterns
- **RESTful APIs** for CRUD operations
- **Real-time subscriptions** for live updates
- **Webhook support** for external notifications
- **GraphQL ready** through Supabase's PostgREST

### Future Integration Possibilities
- **ERP systems** through standardized APIs
- **Accounting software** for financial integration
- **IoT devices** for automated weight measurements
- **Mobile apps** through existing API layer
- **Third-party logistics** for delivery tracking

## Deployment Architecture

### Current Deployment
- **Frontend**: Vercel/Netlify deployment with Next.js
- **Database**: Supabase managed PostgreSQL
- **Authentication**: Supabase Auth service
- **File Storage**: Supabase Storage

### Production Considerations
- **Environment separation**: Development, staging, production
- **Database backups**: Automated daily backups with point-in-time recovery
- **Monitoring**: Application performance monitoring and error tracking
- **Logging**: Structured logging for debugging and audit trails
- **CI/CD pipeline**: Automated testing and deployment

This comprehensive architecture analysis demonstrates that JewelPro is a well-structured, production-ready application with clear separation of concerns, robust business logic, and scalable architecture patterns.
