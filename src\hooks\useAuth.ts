import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/db';
import { User } from '@supabase/supabase-js';

/**
 * User role types
 */
export type UserRole = 'admin' | 'data_entry' | 'customer';

/**
 * Authentication state interface
 */
interface AuthState {
  user: User | null;
  userRole: UserRole | null;
  loading: boolean;
}

/**
 * Custom hook for managing authentication state and operations
 * Provides user authentication status, role-based access control, and auth operations
 * 
 * @returns {Object} Authentication state and methods
 * @property {User|null} user - Current authenticated user
 * @property {UserRole|null} userRole - User's role (admin, data_entry, customer)
 * @property {boolean} loading - Authentication loading state
 * @property {boolean} isAdmin - Whether current user has admin role
 * @property {boolean} isDataEntry - Whether current user has data entry role
 * @property {boolean} isCustomer - Whether current user has customer role
 * @property {boolean} isAuthenticated - Whether user is authenticated
 * @property {Function} signIn - Method to sign in user
 * @property {Function} signUp - Method to register new user
 * @property {Function} signOut - Method to sign out user
 */
export const useAuth = () => {
  const router = useRouter();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    userRole: null,
    loading: true,
  });

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setAuthState({
        user: session?.user ?? null,
        userRole: (session?.user?.user_metadata.role as UserRole) ?? null,
        loading: false,
      });
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN') {
        // Fetch user role from user_roles table
        const { data: roleData } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', session?.user?.id)
          .single();

        setAuthState({
          user: session?.user ?? null,
          userRole: roleData?.role as UserRole ?? null,
          loading: false,
        });
      } else if (event === 'SIGNED_OUT') {
        setAuthState({
          user: null,
          userRole: null,
          loading: false,
        });
        router.push('/auth/login');
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [router]);

  /**
   * Sign in user with email and password
   * 
   * @param {string} email - User's email
   * @param {string} password - User's password
   */
  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) throw error;
  };

  /**
   * Register new user with email, password, and role
   * 
   * @param {string} email - User's email
   * @param {string} password - User's password
   * @param {UserRole} role - User's role (default: customer)
   */
  const signUp = async (email: string, password: string, role: UserRole = 'customer') => {
    const { error: signUpError, data } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          role,
        },
      },
    });
    if (signUpError) throw signUpError;

    // Create user role entry
    if (data.user) {
      const { error: roleError } = await supabase
        .from('user_roles')
        .insert([{ user_id: data.user.id, role }]);
      if (roleError) throw roleError;
    }
  };

  /**
   * Sign out current user
   */
  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  return {
    ...authState,
    isAdmin: authState.userRole === 'admin',
    isDataEntry: authState.userRole === 'data_entry',
    isCustomer: authState.userRole === 'customer',
    isAuthenticated: !!authState.user,
    signIn,
    signUp,
    signOut,
  };
};
