/**
 * @file ThirdPartyCustomerForm.tsx
 * Form component for managing third party customer data
 */

import { useForm } from '@/hooks/useForm';
import { ThirdPartyCustomer, thirdPartyCustomerFields } from '@/types/thirdPartyCustomer';
import { supabase } from '@/lib/db';
import { useState } from 'react';
import { toast } from 'react-hot-toast';

interface ThirdPartyCustomerFormProps {
  customerId?: string;
  initialData?: Partial<ThirdPartyCustomer>;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form component for creating and editing third party customer records
 * 
 * @component
 * @param {Object} props - Component props
 * @param {string} [props.customerId] - ID of customer being edited
 * @param {Partial<ThirdPartyCustomer>} [props.initialData] - Initial form data
 * @param {Function} [props.onSuccess] - Callback after successful submission
 * @param {Function} [props.onCancel] - Callback when form is cancelled
 */
export function ThirdPartyCustomerForm({
  customerId,
  initialData = {},
  onSuccess,
  onCancel,
}: ThirdPartyCustomerFormProps) {
  const [loading, setLoading] = useState(false);

  const { values, handleChange, handleSubmit } = useForm<ThirdPartyCustomer>({
    initialValues: {
      description: '',
      ...initialData,
    },
    onSubmit: async (formData) => {
      try {
        setLoading(true);
        
        // Validate description is not empty
        if (!formData.description.trim()) {
          toast.error('Customer Name is required');
          return;
        }

        if (customerId) {
          // Update existing customer
          const { error } = await supabase
            .from('third_party_cust_mast')
            .update({
              description: formData.description,
              updated_at: new Date().toISOString(),
            })
            .eq('party_cust_id', customerId);

          if (error) throw error;
          toast.success('Customer updated successfully');
        } else {
          // Create new customer
          const { error } = await supabase
            .from('third_party_cust_mast')
            .insert([{
              description: formData.description,
            }]);

          if (error) throw error;
          toast.success('Customer created successfully');
        }

        onSuccess?.();
      } catch (error: any) {
        console.error('Error saving customer:', error);
        if (error.code === '23505') {
          toast.error('This Customer Name already exists');
        } else {
          toast.error('Error saving customer');
        }
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {thirdPartyCustomerFields.map((field) => (
        <div key={field.name} className="space-y-2">
          <label
            htmlFor={field.name}
            className="block text-sm font-medium text-gray-700 dark:text-gray-200"
          >
            {field.label}
            {field.required && <span className="text-red-500">*</span>}
          </label>

          <input
            type={field.type}
            id={field.name}
            name={field.name}
            value={values[field.name] || ''}
            onChange={(e) => handleChange(field.name, e.target.value)}
            maxLength={field.maxLength}
            required={field.required}
            placeholder={field.placeholder}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:border-gray-600"
          />
        </div>
      ))}

      <div className="flex justify-end space-x-4">
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600"
          >
            Cancel
          </button>
        )}
        <button
          type="submit"
          disabled={loading}
          className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {loading ? 'Saving...' : customerId ? 'Update' : 'Create'}
        </button>
      </div>
    </form>
  );
}
