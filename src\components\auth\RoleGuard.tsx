'use client';

import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

type AllowedRoles = 'admin' | 'data_entry' | 'customer';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: AllowedRoles[];
  fallbackPath?: string;
}

/**
 * RoleGuard Component - Protects routes based on user roles
 * 
 * @component
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components to render if role check passes
 * @param {AllowedRoles[]} props.allowedRoles - List of roles that can access the protected content
 * @param {string} [props.fallbackPath='/'] - Redirect path if role check fails
 * 
 * @example
 * ```tsx
 * <RoleGuard allowedRoles={['admin']}>
 *   <AdminDashboard />
 * </RoleGuard>
 * ```
 */
export function RoleGuard({
  children,
  allowedRoles,
  fallbackPath = '/',
}: RoleGuardProps) {
  const { userRole, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && userRole && !allowedRoles.includes(userRole as AllowedRoles)) {
      router.push(fallbackPath);
    }
  }, [userRole, loading, allowedRoles, fallbackPath, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (!userRole || !allowedRoles.includes(userRole as AllowedRoles)) {
    return null;
  }

  return <>{children}</>;
}
