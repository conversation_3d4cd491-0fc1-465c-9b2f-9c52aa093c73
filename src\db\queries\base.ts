import { supabase } from '@/lib/db';
import { getCurrentTimestamp, handleError } from '../utils';
import type { Tables, TableRow, TableInsert, TableUpdate } from '../types';

export class BaseQueries<T extends keyof Tables> {
  protected tableName: T;
  protected pkColumn: string;

  constructor(tableName: T, pkColumn: string = 'id') {
    this.tableName = tableName;
    this.pkColumn = pkColumn;
  }

  public async create(data: TableInsert<T>): Promise<TableRow<T>> {
    const { data: result, error } = await supabase
      .from(this.tableName)
      .insert({ ...data, created_at: getCurrentTimestamp(), updated_at: getCurrentTimestamp() })
      .select()
      .single();

    if (error) handleError(error);
    return result;
  }

  public async update(id: string | number, data: TableUpdate<T>): Promise<TableRow<T>> {
    const { data: result, error } = await supabase
      .from(this.tableName)
      .update({ ...data, updated_at: getCurrentTimestamp() })
      .eq(this.pkColumn, id)
      .select()
      .single();

    if (error) handleError(error);
    return result;
  }

  public async delete(id: string | number): Promise<void> {
    const { error } = await supabase
      .from(this.tableName)
      .delete()
      .eq(this.pkColumn, id);

    if (error) handleError(error);
  }

  public async getById(id: string | number): Promise<TableRow<T> | null> {
    const { data, error } = await supabase
      .from(this.tableName)
      .select()
      .eq(this.pkColumn, id)
      .single();

    if (error && error.code !== 'PGRST116') handleError(error);
    return data;
  }

  public async list(options?: {
    page?: number;
    limit?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
  }): Promise<TableRow<T>[]> {
    const {
      page = 1,
      limit = 10,
      orderBy = 'created_at',
      orderDirection = 'desc'
    } = options || {};

    const { data, error } = await supabase
      .from(this.tableName)
      .select()
      .order(orderBy, { ascending: orderDirection === 'asc' })
      .range((page - 1) * limit, page * limit - 1);

    if (error) handleError(error);
    return data || [];
  }
}
