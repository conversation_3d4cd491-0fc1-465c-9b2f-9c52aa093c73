/**
 * @module components/material-issue/FindingIssueForm
 * @description Finding Issue Form Component - Issue polki/finding assemblies with embedded stones
 */

'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { issueFinding, getAvailableFindings } from '@/services/findingsInventoryService';

const findingIssueSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  order_id: z.string().min(1, 'Order is required'),
  worker_id: z.string().min(1, 'Worker is required'),
  process_id: z.string().min(1, 'Process is required'),
  notes: z.string().optional()
});

interface FindingIssueFormData {
  customer_id: string;
  order_id: string;
  worker_id: string;
  process_id: string;
  notes?: string;
}

interface FindingInventoryItem {
  finding_id: string;
  description: string;
  finding_type: string;
  gross_weight_grams: number;
  location: string;
  customer_id: string;
  status: string | null;
  order_id: string | null;
  notes: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export function FindingIssueForm() {
  const [customers, setCustomers] = useState([]);
  const [orders, setOrders] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [processes, setProcesses] = useState([]);
  const [availableFindings, setAvailableFindings] = useState<FindingInventoryItem[]>([]);
  const [selectedFindings, setSelectedFindings] = useState<string[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FindingIssueFormData>({
    resolver: zodResolver(findingIssueSchema),
    defaultValues: {
      customer_id: '',
      order_id: '',
      worker_id: '',
      process_id: '',
      notes: ''
    }
  });

  // Load master data on component mount
  useEffect(() => {
    loadMasterData();
  }, []);

  // Load available findings when customer changes
  useEffect(() => {
    if (selectedCustomerId) {
      loadAvailableFindings();
    }
  }, [selectedCustomerId]);

  const loadMasterData = async () => {
    try {
      setIsLoading(true);
      
      // Load all master data in parallel
      const [customersRes, workersRes, processesRes] = await Promise.all([
        fetch('/api/masters/customers').then(res => res.json()),
        fetch('/api/masters/workers').then(res => res.json()),
        fetch('/api/masters/processes').then(res => res.json())
      ]);

      setCustomers(customersRes.data || []);
      setWorkers(workersRes.data || []);
      setProcesses(processesRes.data || []);

    } catch (error) {
      console.error('Error loading master data:', error);
      toast.error('Failed to load master data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCustomerOrders = async (customerId: string) => {
    try {
      const response = await fetch(`/api/orders?customer_id=${customerId}&status=active`);
      const data = await response.json();
      setOrders(data.data || []);
    } catch (error) {
      console.error('Error loading customer orders:', error);
      toast.error('Failed to load customer orders');
    }
  };

  const loadAvailableFindings = async () => {
    if (!selectedCustomerId) return;

    try {
      const findings = await getAvailableFindings({
        customer_id: selectedCustomerId
      });

      setAvailableFindings(findings);
    } catch (error) {
      console.error('Error loading available findings:', error);
      toast.error('Failed to load available findings');
    }
  };

  const handleCustomerChange = (customerId: string) => {
    setSelectedCustomerId(customerId);
    form.setValue('customer_id', customerId);
    form.setValue('order_id', '');
    setOrders([]);
    setSelectedFindings([]);
    loadCustomerOrders(customerId);
  };

  const handleFindingSelection = (findingId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedFindings(prev => [...prev, findingId]);
    } else {
      setSelectedFindings(prev => prev.filter(id => id !== findingId));
    }
  };

  const handleSelectAll = () => {
    if (selectedFindings.length === availableFindings.length) {
      setSelectedFindings([]);
    } else {
      setSelectedFindings(availableFindings.map(f => f.finding_id));
    }
  };

  const onSubmit = async (data: FindingIssueFormData) => {
    if (selectedFindings.length === 0) {
      toast.error('Please select at least one finding to issue');
      return;
    }

    try {
      setIsSubmitting(true);

      // Issue each selected finding
      for (const findingId of selectedFindings) {
        const finding = availableFindings.find(f => f.finding_id === findingId);
        if (!finding) continue;

        await issueFinding({
          finding_id: findingId,
          customer_id: data.customer_id,
          order_id: data.order_id,
          worker_id: data.worker_id,
          process_id: data.process_id,
          issued_by: 'current_user', // TODO: Get from auth context
          notes: data.notes
        });
      }

      toast.success(`${selectedFindings.length} finding(s) issued successfully`);
      
      // Reset form and selections
      form.reset();
      setSelectedFindings([]);
      setSelectedCustomerId('');
      setAvailableFindings([]);
      
    } catch (error) {
      console.error('Error issuing findings:', error);
      toast.error('Failed to issue findings');
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedFindingsData = availableFindings.filter(f => selectedFindings.includes(f.finding_id));
  const totalGrossWeight = selectedFindingsData.reduce((sum, finding) => sum + finding.gross_weight_grams, 0);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Issue Findings to Worker</CardTitle>
          <CardDescription>
            Select customer, order, and findings (polki/assemblies) to issue for processing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Customer Selection */}
              <div className="space-y-2">
                <Label htmlFor="customer_id">Customer *</Label>
                <select
                  onChange={(e) => handleCustomerChange(e.target.value)}
                  className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select customer</option>
                  {customers.map((customer: any) => (
                    <option key={customer.customer_id} value={customer.customer_id}>
                      {customer.customer_name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Order Selection */}
              <div className="space-y-2">
                <Label htmlFor="order_id">Order *</Label>
                <select
                  disabled={!selectedCustomerId}
                  onChange={(e) => form.setValue('order_id', e.target.value)}
                  className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                >
                  <option value="">Select order</option>
                  {orders.map((order: any) => (
                    <option key={order.order_id} value={order.order_id}>
                      {order.order_no} - {order.style_code}
                    </option>
                  ))}
                </select>
              </div>

              {/* Worker Selection */}
              <div className="space-y-2">
                <Label htmlFor="worker_id">Worker *</Label>
                <select
                  onChange={(e) => form.setValue('worker_id', e.target.value)}
                  className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select worker</option>
                  {workers.map((worker: any) => (
                    <option key={worker.worker_id} value={worker.worker_id}>
                      {worker.worker_name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Process Selection */}
              <div className="space-y-2">
                <Label htmlFor="process_id">Process *</Label>
                <select
                  onChange={(e) => form.setValue('process_id', e.target.value)}
                  className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select process</option>
                  {processes.map((process: any) => (
                    <option key={process.process_id} value={process.process_id}>
                      {process.process_name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                {...form.register('notes')}
                placeholder="Enter any additional notes..."
                rows={3}
              />
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Available Findings */}
      {selectedCustomerId && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Available Findings</CardTitle>
                <CardDescription>
                  Select findings to issue for the selected customer
                </CardDescription>
              </div>
              {availableFindings.length > 0 && (
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  onClick={handleSelectAll}
                >
                  {selectedFindings.length === availableFindings.length ? 'Deselect All' : 'Select All'}
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {availableFindings.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">
                No findings available for the selected customer
              </p>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">Select</TableHead>
                    <TableHead>Finding ID</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Gross Weight (g)</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {availableFindings.map((finding) => {
                    const isSelected = selectedFindings.includes(finding.finding_id);
                    
                    return (
                      <TableRow key={finding.finding_id} className={isSelected ? "bg-muted/50" : ""}>
                        <TableCell>
                          <Checkbox 
                            checked={isSelected}
                            onCheckedChange={(checked) => handleFindingSelection(finding.finding_id, checked as boolean)}
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          {finding.finding_id}
                        </TableCell>
                        <TableCell>
                          {finding.description}
                        </TableCell>
                        <TableCell>
                          {finding.finding_type}
                        </TableCell>
                        <TableCell>
                          {finding.gross_weight_grams.toFixed(3)}
                        </TableCell>
                        <TableCell>
                          {finding.location}
                        </TableCell>
                        <TableCell>
                          <span className="text-muted-foreground text-sm">
                            {finding.notes || 'No notes'}
                          </span>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      )}

      {/* Selected Findings Summary */}
      {selectedFindings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Issue Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Total Findings</div>
                <div className="text-2xl font-bold">{selectedFindings.length}</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Gross Weight</div>
                <div className="text-2xl font-bold">{totalGrossWeight.toFixed(3)}g</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Selected Items</div>
                <div className="text-2xl font-bold">{selectedFindings.length}</div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            {/* Selected Findings List */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Selected Findings:</Label>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {selectedFindingsData.map((finding) => (
                  <div key={finding.finding_id} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <div>
                      <span className="font-medium">{finding.finding_id}</span>
                      <span className="text-muted-foreground ml-2">- {finding.description}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {finding.gross_weight_grams.toFixed(3)}g
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex justify-end space-x-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setSelectedFindings([]);
                  form.reset();
                }}
              >
                Clear Selection
              </Button>
              <Button 
                onClick={form.handleSubmit(onSubmit)}
                disabled={isSubmitting || selectedFindings.length === 0}
              >
                {isSubmitting ? 'Issuing...' : `Issue ${selectedFindings.length} Finding(s)`}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
