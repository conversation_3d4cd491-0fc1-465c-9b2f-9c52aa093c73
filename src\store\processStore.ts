/**
 * @module store/processStore
 * @description Zustand store for managing manufacturing processes.
 */
import { create } from 'zustand';
import { type Database } from '@/types/db';
import { ProcessTracking } from '@/types/process';

type Process = Database['public']['Tables']['process_mast']['Row'];

interface ProcessState {
  processes: Process[];
  processTrackings: ProcessTracking[]; // Add this
  loading: boolean;
  error: string | null;
  setProcesses: (processes: Process[]) => void;
  addProcess: (process: Process) => void;
  updateProcess: (processId: string, updates: Partial<Process>) => void;
  setProcessTrackings: (processTrackings: ProcessTracking[]) => void; // Add this
  updateProcessTracking: (processTrackingId: string, updates: Partial<ProcessTracking>) => void; // Add this
}

export const useProcessStore = create<ProcessState>((set) => ({
  processes: [],
  processTrackings: [], // Add this
  loading: false,
  error: null,
  setProcesses: (processes) => set({ processes, loading: false, error: null }),
  addProcess: (process) => set((state) => ({ processes: [...state.processes, process] })),
  updateProcess: (processId, updates) =>
    set((state) => ({
      processes: state.processes.map((p) =>
        p.process_id === processId ? { ...p, ...updates } : p
      ),
    })),
  setProcessTrackings: (processTrackings) => set({ processTrackings }), // Add this
  updateProcessTracking: (processTrackingId, updates) => // Add this
    set((state) => ({
      processTrackings: state.processTrackings.map((pt) =>
        pt.id === processTrackingId ? { ...pt, ...updates } : pt
      ),
    })),
}));
