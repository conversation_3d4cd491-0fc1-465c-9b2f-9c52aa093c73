import { AuthForm } from '@/components/auth/AuthForm';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Login - JWL Process Management',
  description: 'Sign in to your account',
};

export default function LoginPage({
  searchParams,
}: {
  searchParams: { redirectedFrom?: string };
}) {
  return (
    <AuthForm 
      mode="login" 
      redirectPath={searchParams.redirectedFrom || '/'} 
    />
  );
}
