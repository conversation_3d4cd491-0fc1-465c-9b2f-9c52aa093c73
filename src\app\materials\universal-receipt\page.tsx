/**
 * @page materials/universal-receipt
 * @description Universal Material Receipt Page - Unified interface for all material receipts
 */

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UniversalReceiptForm } from '@/components/materials/UniversalReceiptForm';
import { 
  ArrowLeftIcon,
  ClipboardCheckIcon,
  SparklesIcon,
  TrendingUpIcon,
  UsersIcon
} from 'lucide-react';
import Link from 'next/link';

export default function UniversalReceiptPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/materials">
            <Button variant="ghost" size="sm">
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              Back to Materials
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Universal Material Receipt</h1>
            <p className="text-gray-600 mt-1">
              Streamlined receipt process for all material types in one interface
            </p>
          </div>
        </div>
        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          <SparklesIcon className="w-3 h-3 mr-1" />
          Enhanced UX
        </Badge>
      </div>

      {/* Feature Highlights */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <ClipboardCheckIcon className="w-8 h-8 text-blue-600" />
              <div>
                <h3 className="font-semibold">Unified Interface</h3>
                <p className="text-sm text-gray-600">
                  Handle stones, findings, and metals in one form
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUpIcon className="w-8 h-8 text-green-600" />
              <div>
                <h3 className="font-semibold">Smart Analytics</h3>
                <p className="text-sm text-gray-600">
                  Real-time loss tracking with visual feedback
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <UsersIcon className="w-8 h-8 text-purple-600" />
              <div>
                <h3 className="font-semibold">Batch Processing</h3>
                <p className="text-sm text-gray-600">
                  Process multiple items simultaneously
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Form */}
      <UniversalReceiptForm 
        onSuccess={() => {
          // Handle success - could redirect or show success message
          console.log('Receipt completed successfully');
        }}
      />

      {/* Help Section */}
      <Card className="bg-gray-50 dark:bg-gray-900">
        <CardHeader>
          <CardTitle className="text-lg">How to Use Universal Receipt</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold">Quick Start:</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Select customer, worker, and process to load issued materials</li>
                <li>Review the list of materials that need to be received</li>
                <li>Use batch actions for standard receipts or edit individual items</li>
                <li>Enter received weights and dust collection amounts</li>
                <li>Review loss calculations and complete the receipt</li>
              </ol>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold">Advanced Features:</h4>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li><strong>Interactive Weight Tracker:</strong> Click the zoom icon for detailed loss analysis</li>
                <li><strong>Batch Operations:</strong> Select multiple items for quick processing</li>
                <li><strong>Smart Suggestions:</strong> System suggests dust collection amounts</li>
                <li><strong>Real-time Validation:</strong> Immediate feedback on loss percentages</li>
                <li><strong>Historical Context:</strong> Compare with worker and process averages</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tips & Best Practices */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <SparklesIcon className="w-5 h-5" />
            Tips for Best Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-green-700">✅ Do:</h4>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Weigh materials immediately after process completion</li>
                <li>• Collect dust systematically for better recovery</li>
                <li>• Use the interactive weight tracker for unusual losses</li>
                <li>• Add notes for any exceptional circumstances</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-red-700">❌ Avoid:</h4>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Mixing materials from different customers</li>
                <li>• Ignoring high loss warnings without investigation</li>
                <li>• Forgetting to collect dust during filing/polishing</li>
                <li>• Batch processing items with unusual characteristics</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
