/**
 * MetalTypeMaster Component
 * Manages the metal type master data for inventory management
 * 
 * @module components/masters
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Pencil, Trash2 } from 'lucide-react';
import { MetalTypeMast } from '@/types/inventory';
import { metalTypeMastFields } from '@/components/masters/forms/FormFields';
import { MasterForm } from '@/components/common/Form/MasterForm';
import { FormField } from '@/types/common';
import { useToast } from '@/hooks/useToast';

/**
 * MetalTypeMaster Component
 * Allows management of metal types used in jewelry manufacturing
 */
export const MetalTypeMaster: React.FC = () => {
  const [metalTypes, setMetalTypes] = useState<MetalTypeMast[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<MetalTypeMast | null>(null);
  const { showToast } = useToast();

  // Convert the record to array of FormField for MasterForm
  const formFields = Object.entries(metalTypeMastFields).map(([id, field]) => ({
    ...field,
    id,
  }));

  const formConfig = {
    fields: formFields,
    title: 'Metal Type',
  };

  useEffect(() => {
    fetchMetalTypes();
  }, []);

  /**
   * Fetches metal types from the database
   */
  const fetchMetalTypes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/masters/metal-types');
      
      if (!response.ok) {
        throw new Error('Failed to fetch metal types');
      }
      
      const data = await response.json();
      setMetalTypes(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching metal types:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      showToast({
        title: 'Error',
        description: errorMessage || 'Error loading metal types',
        type: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handles form submission for creating or updating a metal type
   * @param values - The form values
   */
  const handleSubmit = async (values: Partial<MetalTypeMast>) => {
    try {
      const url = '/api/masters/metal-types';
      const method = selectedItem ? 'PUT' : 'POST';
      const body = selectedItem 
        ? JSON.stringify({ ...values, metal_type_id: selectedItem.metal_type_id }) 
        : JSON.stringify(values);
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body,
      });
      
      if (!response.ok) {
        throw new Error('Failed to save metal type');
      }
      
      await fetchMetalTypes();
      setShowForm(false);
      setSelectedItem(null);
      showToast({
        title: 'Success',
        description: `Metal type ${selectedItem ? 'updated' : 'created'} successfully`,
        type: 'success'
      });
    } catch (err) {
      console.error('Error saving metal type:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      showToast({
        title: 'Error',
        description: errorMessage || 'Error saving metal type',
        type: 'destructive'
      });
    }
  };

  /**
   * Handles deleting a metal type
   * @param item - The metal type to delete
   */
  const handleDelete = async (item: MetalTypeMast) => {
    if (!confirm(`Are you sure you want to delete ${item.name}?`)) {
      return;
    }
    
    try {
      const response = await fetch(`/api/masters/metal-types?id=${item.metal_type_id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete metal type');
      }
      
      await fetchMetalTypes();
      showToast({
        title: 'Success',
        description: 'Metal type deleted successfully',
        type: 'success'
      });
    } catch (err) {
      console.error('Error deleting metal type:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      showToast({
        title: 'Error',
        description: errorMessage || 'Error deleting metal type',
        type: 'destructive'
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Metal Types</h1>
        <button
          onClick={() => {
            setSelectedItem(null);
            setShowForm(true);
          }}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Plus className="w-5 h-5 mr-2" />
           Add Metal Type
         </button>
       </div>
 
       {error && (
         <div className="bg-red-100 border-l-4 border-red-500 text-red-700 dark:bg-red-900 dark:border-red-700 dark:text-red-200 p-4 mb-6" role="alert">
           <p>{error}</p>
         </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {metalTypes.length === 0 ? (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    No metal types found
                  </td>
                </tr>
              ) : (
                metalTypes.map((item) => (
                  <tr key={item.metal_type_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {item.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {item.description || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          item.is_active
                            ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                            : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                        }`}
                      >
                        {item.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => {
                          setSelectedItem(item);
                          setShowForm(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-4"
                      >
                        <Pencil className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(item)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      {showForm && (
        <MasterForm
          config={formConfig}
          initialData={selectedItem}
          onSubmit={handleSubmit}
          onCancel={() => {
            setShowForm(false);
            setSelectedItem(null);
          }}
        />
      )}
    </div>
  );
};
