/**
 * @module types/process
 * @description Shared TypeScript types and interfaces for manufacturing processes.
 */
import { type Database } from './db';

// Base Process type derived from the database schema
export type Process = Database['public']['Tables']['process_mast']['Row'];

// Enum-like type for process statuses
export type ProcessStatus = 'PENDING' | 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'DELAYED' | 'ON_HOLD';

// Interface for tracking a specific process instance within an order
export interface ProcessTracking {
  id: string;
  process_id: string;
  worker_id: string;
  status: ProcessStatus;
  sequence_no: number;
  order_id: string;
  created_at: string;
  updated_at: string;
  process: {
    name: string;
    standard_time: number;
  };
}

// Interface for process scheduling
export interface ProcessSchedule {
  id: string;
  process_id: string;
  worker_id: string;
  scheduled_start: string;
  scheduled_end: string;
  status: ProcessStatus;
}

// Interface for worker assignments
export interface WorkerAssignment {
  id: string;
  worker_id: string;
  process_id: string;
  order_id: string;
  assigned_date: string;
  status: ProcessStatus;
  estimated_hours: number;
  actual_hours?: number;
}
