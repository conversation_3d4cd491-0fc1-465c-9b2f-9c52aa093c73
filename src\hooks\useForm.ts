'use client';

import { useState } from 'react';

interface UseFormProps<T> {
  initialValues: Partial<T>;
  onSubmit: (values: T) => void;
}

/**
 * Custom hook for managing form state and handling form submissions
 * 
 * @template T - Type of form values
 * @param {Object} props
 * @param {Partial<T>} props.initialValues - Initial form values
 * @param {(values: T) => void} props.onSubmit - Form submission handler
 * 
 * @returns {Object} Form handlers and state
 * @property {Partial<T>} values - Current form values
 * @property {(field: keyof T, value: any) => void} handleChange - Field change handler
 * @property {(e: React.FormEvent) => void} handleSubmit - Form submission handler
 * 
 * @example
 * ```tsx
 * const { values, handleChange, handleSubmit } = useForm({
 *   initialValues: { name: '', email: '' },
 *   onSubmit: (values) => console.log(values)
 * });
 * ```
 */
export function useForm<T>({ initialValues, onSubmit }: UseFormProps<T>) {
  const [values, setValues] = useState<Partial<T>>(initialValues);

  const handleChange = (field: keyof T, value: any) => {
    setValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(values as T);
  };

  return {
    values,
    handleChange,
    handleSubmit,
  };
}
