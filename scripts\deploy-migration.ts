/**
 * @script deploy-migration.ts
 * @description Deploy migration to Supabase and generate types
 */

import * as fs from 'fs';
import * as path from 'path';

async function deployMigration() {
  try {
    console.log('🚀 Preparing Supabase migration deployment...');
    
    // Get the latest migration file
    const migrationsDir = path.join(process.cwd(), 'supabase', 'migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();

    const latestMigration = migrationFiles[migrationFiles.length - 1];
    console.log(`📝 Latest migration: ${latestMigration}`);
    
    // Read migration file
    const migrationPath = path.join(migrationsDir, latestMigration);
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('\n' + '='.repeat(80));
    console.log('📋 COPY THE FOLLOWING SQL TO SUPABASE SQL EDITOR:');
    console.log('='.repeat(80));
    console.log('\n-- Setting Process Synchronization Migration');
    console.log('-- Please copy and paste this entire SQL block into Supabase SQL Editor\n');
    console.log(migrationSQL);
    console.log('\n' + '='.repeat(80));
    console.log('📋 END OF SQL - COPY ABOVE TO SUPABASE SQL EDITOR');
    console.log('='.repeat(80));

    console.log('\n📖 INSTRUCTIONS:');
    console.log('1. Go to your Supabase Dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Create a new query');
    console.log('4. Copy and paste the SQL above');
    console.log('5. Click "Run" to execute the migration');
    console.log('6. After successful execution, run: npm run gen:types');

    // Also save to a separate file for easy copying
    const outputPath = path.join(process.cwd(), 'migration-to-deploy.sql');
    fs.writeFileSync(outputPath, migrationSQL);
    console.log(`\n💾 SQL also saved to: ${outputPath}`);

  } catch (error) {
    console.error('❌ Error preparing migration:', error);
    process.exit(1);
  }
}

deployMigration();
