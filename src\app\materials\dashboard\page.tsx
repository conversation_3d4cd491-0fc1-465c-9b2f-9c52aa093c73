/**
 * @page materials/dashboard
 * @description Material Flow Dashboard Page - Management overview of all material activities
 */

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MaterialFlowDashboard } from '@/components/dashboard/MaterialFlowDashboard';
import { 
  ArrowLeftIcon,
  BarChart3Icon,
  TrendingUpIcon,
  ActivityIcon,
  EyeIcon
} from 'lucide-react';
import Link from 'next/link';

export default function MaterialDashboardPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/materials">
            <Button variant="ghost" size="sm">
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              Back to Materials
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Material Flow Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Real-time overview and management of all material activities
            </p>
          </div>
        </div>
        <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          <ActivityIcon className="w-3 h-3 mr-1" />
          Live Data
        </Badge>
      </div>

      {/* Dashboard Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <EyeIcon className="w-8 h-8 text-blue-600" />
              <div>
                <h3 className="font-semibold">Real-time Monitoring</h3>
                <p className="text-sm text-gray-600">
                  Live updates every 30 seconds
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950 dark:to-orange-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <BarChart3Icon className="w-8 h-8 text-red-600" />
              <div>
                <h3 className="font-semibold">Exception Alerts</h3>
                <p className="text-sm text-gray-600">
                  Immediate attention to issues
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUpIcon className="w-8 h-8 text-green-600" />
              <div>
                <h3 className="font-semibold">Quick Actions</h3>
                <p className="text-sm text-gray-600">
                  One-click management tasks
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard */}
      <MaterialFlowDashboard />

      {/* Quick Actions Panel */}
      <Card className="bg-gray-50 dark:bg-gray-900">
        <CardHeader>
          <CardTitle className="text-lg">Quick Management Actions</CardTitle>
          <CardDescription>
            Common tasks for material flow management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link href="/materials/universal-receipt">
              <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <ActivityIcon className="w-4 h-4 text-green-600" />
                </div>
                <div className="text-center">
                  <div className="font-medium">Universal Receipt</div>
                  <div className="text-xs text-gray-500">Process all materials</div>
                </div>
              </Button>
            </Link>

            <Link href="/materials/issue">
              <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <ActivityIcon className="w-4 h-4 text-blue-600" />
                </div>
                <div className="text-center">
                  <div className="font-medium">Issue Materials</div>
                  <div className="text-xs text-gray-500">New assignments</div>
                </div>
              </Button>
            </Link>

            <Link href="/materials/dust">
              <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <ActivityIcon className="w-4 h-4 text-yellow-600" />
                </div>
                <div className="text-center">
                  <div className="font-medium">Dust Management</div>
                  <div className="text-xs text-gray-500">Collection & refining</div>
                </div>
              </Button>
            </Link>

            <Link href="/materials/transfers">
              <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <ActivityIcon className="w-4 h-4 text-purple-600" />
                </div>
                <div className="text-center">
                  <div className="font-medium">Location Transfers</div>
                  <div className="text-xs text-gray-500">Move materials</div>
                </div>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Dashboard Usage Guide */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <EyeIcon className="w-5 h-5" />
            Dashboard Usage Guide
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold">Key Metrics:</h4>
              <ul className="text-sm space-y-2">
                <li><strong>Active:</strong> Materials currently with workers</li>
                <li><strong>Overdue:</strong> Materials past expected return time</li>
                <li><strong>Alerts:</strong> High loss transactions requiring attention</li>
                <li><strong>Avg Loss:</strong> Current average loss percentage</li>
                <li><strong>Dust Ready:</strong> Dust parcels ready for refining</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold">Quick Actions:</h4>
              <ul className="text-sm space-y-2">
                <li><strong>Quick Receipt:</strong> Fast receipt for standard returns</li>
                <li><strong>Resolve Alert:</strong> Mark loss alerts as investigated</li>
                <li><strong>Auto Refresh:</strong> Dashboard updates every 30 seconds</li>
                <li><strong>Priority Colors:</strong> Red (high), Yellow (medium), Green (low)</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
