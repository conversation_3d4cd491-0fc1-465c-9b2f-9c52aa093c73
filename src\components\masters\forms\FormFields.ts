import { FormField } from '@/types/common';

/**
 * Form field definitions for Metal Type Master
 * Contains field validation and configuration
 */
export const metalTypeMastFields: Record<string, FormField> = {
  name: {
    id: 'name',
    label: 'Metal Type Name',
    type: 'text',
    required: true,
    maxLength: 50,
    placeholder: 'Enter metal type name'
  },
  description: {
    id: 'description',
    label: 'Description',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter description'
  },
  is_active: {
    id: 'is_active',
    label: 'Active',
    type: 'checkbox',
    required: false,
    defaultValue: true
  }
};

/**
 * Form field definitions for Stone Type Master
 * Contains field validation and configuration
 */
export const stoneTypeMastFields: Record<string, FormField> = {
  name: {
    id: 'name',
    label: 'Stone Type Name',
    type: 'text',
    required: true,
    maxLength: 50,
    placeholder: 'Enter stone type name'
  },
  description: {
    id: 'description',
    label: 'Description',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter description'
  },
  is_active: {
    id: 'is_active',
    label: 'Active',
    type: 'checkbox',
    required: false,
    defaultValue: true
  }
};

/**
 * Form field definitions for Stone Shape Master
 * Contains field validation and configuration
 */
export const stoneShapeMastFields: Record<string, FormField> = {
  name: {
    id: 'name',
    label: 'Shape Name',
    type: 'text',
    required: true,
    maxLength: 50,
    placeholder: 'Enter shape name'
  },
  description: {
    id: 'description',
    label: 'Description',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter description'
  },
  is_active: {
    id: 'is_active',
    label: 'Active',
    type: 'checkbox',
    required: false,
    defaultValue: true
  }
};

/**
 * Form field definitions for Diamond Cut Master
 * Contains field validation and configuration
 */
export const diamondCutMastFields: Record<string, FormField> = {
  name: {
    id: 'name',
    label: 'Cut Name',
    type: 'text',
    required: true,
    maxLength: 50,
    placeholder: 'Enter cut name'
  },
  description: {
    id: 'description',
    label: 'Description',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter description'
  },
  is_active: {
    id: 'is_active',
    label: 'Active',
    type: 'checkbox',
    required: false,
    defaultValue: true
  }
};

/**
 * Form field definitions for Polki Size Master
 * Contains field validation and configuration
 */
export const polkiSizeMastFields: Record<string, FormField> = {
  size_name: {
    id: 'size_name',
    label: 'Size Name',
    type: 'text',
    required: true,
    maxLength: 50,
    placeholder: 'Enter size name'
  },
  min_size: {
    id: 'min_size',
    label: 'Minimum Size (mm)',
    type: 'number',
    required: false,
    min: 0,
    step: 0.01,
    placeholder: 'Enter minimum size'
  },
  max_size: {
    id: 'max_size',
    label: 'Maximum Size (mm)',
    type: 'number',
    required: false,
    min: 0,
    step: 0.01,
    placeholder: 'Enter maximum size'
  },
  description: {
    id: 'description',
    label: 'Description',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter description'
  },
  is_active: {
    id: 'is_active',
    label: 'Active',
    type: 'checkbox',
    required: false,
    defaultValue: true
  }
};

/**
 * Form field definitions for Unit of Measure Master
 * Contains field validation and configuration
 */
export const uomMastFields: Record<string, FormField> = {
  code: {
    id: 'code',
    label: 'UOM Code',
    type: 'text',
    required: true,
    maxLength: 10,
    pattern: '^[A-Z]+$',
    placeholder: 'Enter UOM code (e.g., GM)'
  },
  name: {
    id: 'name',
    label: 'UOM Name',
    type: 'text',
    required: true,
    maxLength: 50,
    placeholder: 'Enter UOM name (e.g., Grams)'
  },
  description: {
    id: 'description',
    label: 'Description',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter description'
  },
  is_active: {
    id: 'is_active',
    label: 'Active',
    type: 'checkbox',
    required: false,
    defaultValue: true
  }
};

/**
 * Form field definitions for Customer Material Receipt
 * Contains field validation and configuration
 */
export const customerMaterialReceiptFields: Record<string, FormField> = {
  customer_id: {
    id: 'customer_id',
    label: 'Customer',
    type: 'select',
    required: true,
    options: [], // Populated from customer_mast
    placeholder: 'Select customer'
  },
  order_id: {
    id: 'order_id',
    label: 'Order',
    type: 'select',
    required: false,
    options: [], // Populated from orders
    placeholder: 'Select order (optional)'
  },
  receipt_date: {
    id: 'receipt_date',
    label: 'Receipt Date',
    type: 'date',
    required: true,
    placeholder: 'Select receipt date'
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 500,
    placeholder: 'Enter notes'
  }
};

/**
 * Form field definitions for Metal Pool
 * Contains field validation and configuration
 */
export const metalPoolFields: Record<string, FormField> = {
  metal_type_id: {
    id: 'metal_type_id',
    label: 'Metal Type',
    type: 'select',
    required: true,
    options: [], // Populated from metal_type_mast
    placeholder: 'Select metal type'
  },
  karat_id: {
    id: 'karat_id',
    label: 'Karat/Purity',
    type: 'select',
    required: true,
    options: [], // Populated from karat_mast
    placeholder: 'Select karat/purity',
    dependsOn: 'metal_type_id'
  },
  initial_weight: {
    id: 'initial_weight',
    label: 'Initial Weight',
    type: 'number',
    required: true,
    min: 0.001,
    step: 0.001,
    placeholder: 'Enter initial weight'
  },
  current_weight: {
    id: 'current_weight',
    label: 'Current Weight',
    type: 'number',
    required: true,
    min: 0,
    step: 0.001,
    placeholder: 'Enter current weight'
  },
  customer_id: {
    id: 'customer_id',
    label: 'Customer',
    type: 'select',
    required: true,
    options: [], // Populated from customer_mast
    placeholder: 'Select customer'
  },
  uom_id: {
    id: 'uom_id',
    label: 'Unit of Measure',
    type: 'select',
    required: true,
    options: [], // Populated from uom_mast
    placeholder: 'Select unit of measure'
  },
  is_active: {
    id: 'is_active',
    label: 'Active',
    type: 'checkbox',
    required: false,
    defaultValue: true
  }
};

/**
 * Form field definitions for Metal Transformation
 * Contains field validation and configuration
 */
export const metalTransformationFields: Record<string, FormField> = {
  source_pool_id: {
    id: 'source_pool_id',
    label: 'Source Metal Pool',
    type: 'select',
    required: true,
    options: [], // Populated from metal_pool
    placeholder: 'Select source pool'
  },
  target_pool_id: {
    id: 'target_pool_id',
    label: 'Target Metal Pool',
    type: 'select',
    required: true,
    options: [], // Populated from metal_pool
    placeholder: 'Select target pool'
  },
  source_weight: {
    id: 'source_weight',
    label: 'Source Weight',
    type: 'number',
    required: true,
    min: 0.001,
    step: 0.001,
    placeholder: 'Enter source weight'
  },
  target_weight: {
    id: 'target_weight',
    label: 'Target Weight',
    type: 'number',
    required: true,
    min: 0.001,
    step: 0.001,
    placeholder: 'Enter target weight'
  },
  transformation_date: {
    id: 'transformation_date',
    label: 'Transformation Date',
    type: 'date',
    required: true,
    placeholder: 'Select transformation date'
  },
  process_id: {
    id: 'process_id',
    label: 'Process',
    type: 'select',
    required: false,
    options: [], // Populated from process_mast
    placeholder: 'Select process (optional)'
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 500,
    placeholder: 'Enter notes'
  }
};

/**
 * Form field definitions for Metal Transaction
 * Contains field validation and configuration
 */
export const metalTransactionFields: Record<string, FormField> = {
  transaction_type_id: {
    id: 'transaction_type_id',
    label: 'Transaction Type',
    type: 'select',
    required: true,
    options: [], // Populated from metal_transaction_type_mast
    placeholder: 'Select transaction type'
  },
  pool_id: {
    id: 'pool_id',
    label: 'Metal Pool',
    type: 'select',
    required: true,
    options: [], // Populated from metal_pool
    placeholder: 'Select metal pool'
  },
  related_transaction_id: {
    id: 'related_transaction_id',
    label: 'Related Transaction',
    type: 'select',
    required: false,
    options: [], // Populated from metal_transactions
    placeholder: 'Select related transaction (optional)'
  },
  order_id: {
    id: 'order_id',
    label: 'Order',
    type: 'select',
    required: false,
    options: [], // Populated from orders
    placeholder: 'Select order (optional)'
  },
  process_id: {
    id: 'process_id',
    label: 'Process',
    type: 'select',
    required: false,
    options: [], // Populated from process_mast
    placeholder: 'Select process (optional)'
  },
  external_entity_id: {
    id: 'external_entity_id',
    label: 'External Processor',
    type: 'select',
    required: false,
    options: [], // Populated from external_processor_mast
    placeholder: 'Select external processor (optional)'
  },
  weight: {
    id: 'weight',
    label: 'Weight',
    type: 'number',
    required: true,
    min: 0.001,
    step: 0.001,
    placeholder: 'Enter weight'
  },
  fine_weight: {
    id: 'fine_weight',
    label: 'Fine Weight',
    type: 'number',
    required: false,
    min: 0,
    step: 0.001,
    placeholder: 'Enter fine weight'
  },
  uom_id: {
    id: 'uom_id',
    label: 'Unit of Measure',
    type: 'select',
    required: true,
    options: [], // Populated from uom_mast
    placeholder: 'Select unit of measure'
  },
  transaction_date: {
    id: 'transaction_date',
    label: 'Transaction Date',
    type: 'date',
    required: true,
    placeholder: 'Select transaction date'
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 500,
    placeholder: 'Enter notes'
  }
};

/**
 * Form field definitions for Order Diamonds
 * Contains field validation and configuration
 */
export const orderDiamondFields: Record<string, FormField> = {
  order_id: {
    id: 'order_id',
    label: 'Order',
    type: 'select',
    required: true,
    options: [], // Populated from orders
    placeholder: 'Select order'
  },
  receipt_id: {
    id: 'receipt_id',
    label: 'Receipt',
    type: 'select',
    required: true,
    options: [], // Populated from customer_material_receipt
    placeholder: 'Select receipt',
    dependsOn: 'order_id'
  },
  cut_id: {
    id: 'cut_id',
    label: 'Cut Type',
    type: 'select',
    required: true,
    options: [], // Populated from diamond_cut_mast
    placeholder: 'Select cut type'
  },
  shape_id: {
    id: 'shape_id',
    label: 'Shape',
    type: 'select',
    required: true,
    options: [], // Populated from stone_shape_mast
    placeholder: 'Select shape'
  },
  quantity: {
    id: 'quantity',
    label: 'Quantity',
    type: 'number',
    required: true,
    min: 1,
    step: 1,
    placeholder: 'Enter quantity'
  },
  weight: {
    id: 'weight',
    label: 'Weight',
    type: 'number',
    required: false,
    min: 0,
    step: 0.001,
    placeholder: 'Enter weight'
  },
  size_mm: {
    id: 'size_mm',
    label: 'Size (mm)',
    type: 'number',
    required: false,
    min: 0,
    step: 0.01,
    placeholder: 'Enter size in mm'
  },
  uom_id: {
    id: 'uom_id',
    label: 'Unit of Measure',
    type: 'select',
    required: true,
    options: [], // Populated from uom_mast
    placeholder: 'Select unit of measure'
  },
  status: {
    id: 'status',
    label: 'Status',
    type: 'select',
    required: true,
    options: [
      { value: 'RECEIVED', label: 'Received' },
      { value: 'IN_PROCESS', label: 'In Process' },
      { value: 'USED', label: 'Used' },
      { value: 'RETURNED', label: 'Returned' }
    ],
    placeholder: 'Select status'
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter notes'
  }
};

/**
 * Form field definitions for Order Color Stones
 * Contains field validation and configuration
 */
export const orderColorStoneFields: Record<string, FormField> = {
  order_id: {
    id: 'order_id',
    label: 'Order',
    type: 'select',
    required: true,
    options: [], // Populated from orders
    placeholder: 'Select order'
  },
  receipt_id: {
    id: 'receipt_id',
    label: 'Receipt',
    type: 'select',
    required: true,
    options: [], // Populated from customer_material_receipt
    placeholder: 'Select receipt',
    dependsOn: 'order_id'
  },
  stone_type_id: {
    id: 'stone_type_id',
    label: 'Stone Type',
    type: 'select',
    required: true,
    options: [], // Populated from stone_type_mast
    placeholder: 'Select stone type'
  },
  shape_id: {
    id: 'shape_id',
    label: 'Shape',
    type: 'select',
    required: true,
    options: [], // Populated from stone_shape_mast
    placeholder: 'Select shape'
  },
  quantity: {
    id: 'quantity',
    label: 'Quantity',
    type: 'number',
    required: true,
    min: 1,
    step: 1,
    placeholder: 'Enter quantity'
  },
  weight: {
    id: 'weight',
    label: 'Weight',
    type: 'number',
    required: false,
    min: 0,
    step: 0.001,
    placeholder: 'Enter weight'
  },
  size_mm: {
    id: 'size_mm',
    label: 'Size (mm)',
    type: 'number',
    required: false,
    min: 0,
    step: 0.01,
    placeholder: 'Enter size in mm'
  },
  uom_id: {
    id: 'uom_id',
    label: 'Unit of Measure',
    type: 'select',
    required: true,
    options: [], // Populated from uom_mast
    placeholder: 'Select unit of measure'
  },
  status: {
    id: 'status',
    label: 'Status',
    type: 'select',
    required: true,
    options: [
      { value: 'RECEIVED', label: 'Received' },
      { value: 'IN_PROCESS', label: 'In Process' },
      { value: 'USED', label: 'Used' },
      { value: 'RETURNED', label: 'Returned' }
    ],
    placeholder: 'Select status'
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter notes'
  }
};

/**
 * Form field definitions for Process Metal Consumption
 * Contains field validation and configuration
 */
export const processMetalConsumptionFields: Record<string, FormField> = {
  process_id: {
    id: 'process_id',
    label: 'Process',
    type: 'select',
    required: true,
    options: [], // Populated from process_mast
    placeholder: 'Select process'
  },
  order_id: {
    id: 'order_id',
    label: 'Order',
    type: 'select',
    required: true,
    options: [], // Populated from orders
    placeholder: 'Select order'
  },
  pool_id: {
    id: 'pool_id',
    label: 'Metal Pool',
    type: 'select',
    required: true,
    options: [], // Populated from metal_pool
    placeholder: 'Select metal pool'
  },
  expected_weight: {
    id: 'expected_weight',
    label: 'Expected Weight',
    type: 'number',
    required: true,
    min: 0.001,
    step: 0.001,
    placeholder: 'Enter expected weight'
  },
  actual_weight: {
    id: 'actual_weight',
    label: 'Actual Weight',
    type: 'number',
    required: true,
    min: 0,
    step: 0.001,
    placeholder: 'Enter actual weight'
  },
  uom_id: {
    id: 'uom_id',
    label: 'Unit of Measure',
    type: 'select',
    required: true,
    options: [], // Populated from uom_mast
    placeholder: 'Select unit of measure'
  },
  consumed_at: {
    id: 'consumed_at',
    label: 'Consumption Date',
    type: 'date',
    required: true,
    placeholder: 'Select consumption date'
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter notes'
  }
};
