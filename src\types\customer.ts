/**
 * Interface for customer data
 * Uses 2-letter uppercase code
 */
export interface Customer {
  customer_id?: string;
  description: string; // 2-letter uppercase code
  created_at?: Date;
  updated_at?: Date;
}

/**
 * Form configuration for customer fields
 */
export const customerFields = [
  {
    name: 'description',
    label: 'Customer Code',
    type: 'text',
    required: true,
    maxLength: 2,
    pattern: '^[A-Z]{2}$',
    placeholder: 'e.g., AB',
  },
] as const;
