/**
 * Model Context Protocol (MCP) configuration
 * 
 * This file contains configuration for connecting to Supabase using the
 * Model Context Protocol. It defines the connection parameters needed for
 * the PostgreSQL database hosted on Supabase.
 */

export const mcpConfig = {
  postgresql: {
    command: 'npx',
    args: [
      '@modelcontextprotocol/server-postgres',
      'postgresql://postgres.vcimuvdnftocekbqrbfy:<EMAIL>:5432/postgres'
    ],
    env: {}
  }
};

/**
 * Returns the connection string for the Supabase PostgreSQL database
 * 
 * @returns {string} The full PostgreSQL connection string
 */
export function getSupabaseConnectionString(): string {
  return mcpConfig.postgresql.args[1] as string;
}

/**
 * Returns the MCP server command with arguments
 * 
 * @returns {string[]} Array containing the command and arguments to run the MCP server
 */
export function getMcpServerCommand(): string[] {
  return [mcpConfig.postgresql.command, ...mcpConfig.postgresql.args];
}

export default mcpConfig;
