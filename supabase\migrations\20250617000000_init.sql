-- 20250616_init.sql – single clean schema for JwlProcManage
-- This file recreates the entire public schema from scratch.
-- Run with Supabase CLI or psql. All objects are created in correct dependency order.

BEGIN;

-- --------------------------------------------------
-- 0. Extensions (UUID helpers)
-- --------------------------------------------------
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";      -- uuid_generate_v4()
CREATE EXTENSION IF NOT EXISTS "pgcrypto";       -- gen_random_uuid()

-- --------------------------------------------------
-- 1. Lookup / master tables with no dependencies
-- --------------------------------------------------

CREATE TABLE public.uom_mast (
  uom_id       uuid PRIMARY KEY         DEFAULT gen_random_uuid(),
  code         varchar      NOT NULL,
  name         varchar      NOT NULL,
  description  text,
  is_active    bool         DEFAULT true,
  created_at   timestamptz  DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz  DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.gold_colour_mast (
  gold_colour_id uuid PRIMARY KEY       DEFAULT uuid_generate_v4(),
  description    varchar      NOT NULL,
  processing_complexity_factor numeric NOT NULL,
  created_at     timestamptz  DEFAULT CURRENT_TIMESTAMP,
  updated_at     timestamptz  DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.karat_mast (
  karat_id     uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),
  description  varchar      NOT NULL,
  purity       numeric      NOT NULL,
  standard_wastage numeric  NOT NULL,
  density      numeric      NOT NULL DEFAULT 0,
  created_at   timestamptz  DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz  DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.item_type_mast (
  item_type_id uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),
  description  varchar      NOT NULL,
  average_processing_time numeric NOT NULL,
  suffix       varchar      NOT NULL UNIQUE CHECK (length(suffix)=2),
  created_at   timestamptz  DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz  DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.metal_type_mast (
  metal_type_id uuid PRIMARY KEY        DEFAULT gen_random_uuid(),
  name          varchar      NOT NULL,
  description   text,
  is_active     bool         DEFAULT true,
  created_at    timestamptz  DEFAULT CURRENT_TIMESTAMP,
  updated_at    timestamptz  DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.order_category_mast (
  order_category_id uuid PRIMARY KEY    DEFAULT uuid_generate_v4(),
  description  varchar      NOT NULL,
  base_processing_time numeric NOT NULL,
  created_at   timestamptz  DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz  DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.diamond_cut_mast (
  cut_id       uuid PRIMARY KEY         DEFAULT gen_random_uuid(),
  name         varchar      NOT NULL,
  description  text,
  is_active    bool         DEFAULT true,
  created_at   timestamp    DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamp    DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.stone_shape_mast (
  shape_id     uuid PRIMARY KEY         DEFAULT gen_random_uuid(),
  name         varchar      NOT NULL,
  description  text,
  is_active    bool         DEFAULT true,
  created_at   timestamp    DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamp    DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.stone_size_mast (
  size_id          uuid PRIMARY KEY     DEFAULT gen_random_uuid(),
  size_description varchar      NOT NULL UNIQUE,
  min_carat_weight numeric,
  max_carat_weight numeric,
  diameter_mm      numeric,
  created_at       timestamptz  DEFAULT CURRENT_TIMESTAMP,
  updated_at       timestamptz  DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.stone_type_mast (
  stone_type_id uuid PRIMARY KEY        DEFAULT gen_random_uuid(),
  name          varchar      NOT NULL,
  description   text,
  is_active     bool         DEFAULT true,
  is_diamond    bool         DEFAULT false,
  created_at    timestamp    DEFAULT CURRENT_TIMESTAMP,
  updated_at    timestamp    DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.process_mast (
  process_id   uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),
  name         varchar      NOT NULL,
  description  text,
  standard_time numeric     NOT NULL,
  quality_checkpoints jsonb,
  is_active    bool         DEFAULT true,
  requires_skill bool       DEFAULT true,
  can_be_outsourced bool    DEFAULT false,
  complexity_hours_json jsonb,
  default_wastage_pct numeric,
  default_recovery_pct numeric,
  dust_generated bool       DEFAULT false,
  repeatable    bool        DEFAULT false,
  created_at    timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at    timestamptz DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.external_processor_mast (
  processor_id uuid PRIMARY KEY         DEFAULT gen_random_uuid(),
  name         varchar      NOT NULL,
  contact_person varchar,
  phone        varchar,
  email        varchar,
  address      text,
  is_active    bool         DEFAULT true,
  created_at   timestamp    DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamp    DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.third_party_cust_mast (
  party_cust_id uuid PRIMARY KEY        DEFAULT uuid_generate_v4(),
  description   varchar      NOT NULL,
  created_at    timestamptz  DEFAULT CURRENT_TIMESTAMP,
  updated_at    timestamptz  DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.customer_mast (
  customer_id   uuid PRIMARY KEY         DEFAULT gen_random_uuid(),
  customer_code varchar      NOT NULL UNIQUE CHECK (customer_code ~ '^[A-Z]{2}$'),
  customer_name varchar,
  created_at    timestamptz  DEFAULT CURRENT_TIMESTAMP,
  updated_at    timestamptz  DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.worker_mast (
  worker_id     uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),
  name          varchar      NOT NULL,
  shift_start   time,
  shift_end     time,
  worker_type   varchar      CHECK (worker_type IN ('internal','vendor')),
  available_from timestamptz,
  available_to  timestamptz,
  skills        jsonb,
  efficiency_factor numeric   DEFAULT 1.0,
  is_active     bool          DEFAULT true,
  is_vendor     bool          DEFAULT false,
  created_at    timestamptz   DEFAULT CURRENT_TIMESTAMP,
  updated_at    timestamptz   DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.user_roles (
  user_id     uuid PRIMARY KEY,
  role        varchar NOT NULL CHECK (role IN ('admin','data_entry','customer')),
  created_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at  timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- --------------------------------------------------
-- 2. inventory_locations (self-referencing)
-- --------------------------------------------------
CREATE TABLE public.inventory_locations (
  location_id        uuid PRIMARY KEY     DEFAULT gen_random_uuid(),
  location_name      varchar    NOT NULL UNIQUE,
  location_type      varchar    NOT NULL,
  parent_location_id uuid REFERENCES public.inventory_locations(location_id),
  is_active          bool       DEFAULT true,
  created_at         timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at         timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- seed core locations
INSERT INTO public.inventory_locations (location_name,location_type,is_active) VALUES
 ('Customers','CUSTOMER',true),
 ('Safe','VAULT',true),
 ('Central','COUNTER',true),
 ('Floor','WIP',true),
 ('ExternalVendor','VENDOR',true),
 ('DustStore','DUST',true)
ON CONFLICT (location_name) DO NOTHING;

-- --------------------------------------------------
-- 3. Styles and related
-- --------------------------------------------------
CREATE TABLE public.styles_mast (
  style_id     uuid PRIMARY KEY        DEFAULT uuid_generate_v4(),
  item_type_id uuid REFERENCES public.item_type_mast(item_type_id),
  party_id     uuid REFERENCES public.third_party_cust_mast(party_cust_id),
  net_wt       numeric      NOT NULL,
  net_wt_karat_id uuid REFERENCES public.karat_mast(karat_id),
  estimated_processing_time numeric NOT NULL,
  processing_notes text,
  created_at   timestamptz  DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz  DEFAULT CURRENT_TIMESTAMP
);

-- --------------------------------------------------
-- 4. Orders & dependent tables
-- --------------------------------------------------
CREATE TABLE public.orders (
  order_id            uuid PRIMARY KEY   DEFAULT gen_random_uuid(),
  order_reference_no  varchar(30) UNIQUE NOT NULL,
  party_order_no      varchar,
  style_code          uuid REFERENCES public.styles_mast(style_id),
  item_type_id        uuid NOT NULL REFERENCES public.item_type_mast(item_type_id),
  karat_id            uuid NOT NULL REFERENCES public.karat_mast(karat_id),
  gold_colour_id      uuid NOT NULL REFERENCES public.gold_colour_mast(gold_colour_id),
  third_party_cust_id uuid REFERENCES public.third_party_cust_mast(party_cust_id),
  order_category_id   uuid NOT NULL REFERENCES public.order_category_mast(order_category_id),
  issue_date          date NOT NULL,
  party_delivery_date date,
  expected_delivery_date date NOT NULL,
  polki_quality       varchar,
  diamond_quality     varchar,
  diamond_wt_expected numeric,
  gold_wt_expected    numeric,
  reference_image_url text,
  tags                text[],
  metal_received      bool DEFAULT false,
  diamonds_received   bool DEFAULT false,
  polki_received      bool DEFAULT false,
  remarks             text,
  qr_code             text UNIQUE,
  customer_id         uuid NOT NULL REFERENCES public.customer_mast(customer_id),
  order_status        varchar DEFAULT 'active' CHECK (order_status IN ('active','completed','cancelled','on_hold')),
  created_at          timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at          timestamptz DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.customer_material_receipt (
  receipt_id   uuid PRIMARY KEY      DEFAULT gen_random_uuid(),
  customer_id  uuid REFERENCES public.customer_mast(customer_id),
  order_id     uuid REFERENCES public.orders(order_id),
  notes        text,
  receipt_date timestamp DEFAULT CURRENT_TIMESTAMP,
  created_at   timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamp DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.customer_metal_receipt_items (
  item_id      uuid PRIMARY KEY      DEFAULT gen_random_uuid(),
  receipt_id   uuid REFERENCES public.customer_material_receipt(receipt_id),
  metal_type_id uuid REFERENCES public.metal_type_mast(metal_type_id),
  karat_id     uuid REFERENCES public.karat_mast(karat_id),
  purity_received numeric,
  weight_grams numeric NOT NULL,
  form_received varchar,
  notes        text,
  created_at   timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.customer_stone_receipt_items (
  item_id      uuid PRIMARY KEY      DEFAULT gen_random_uuid(),
  receipt_id   uuid REFERENCES public.customer_material_receipt(receipt_id),
  stone_type_id uuid REFERENCES public.stone_type_mast(stone_type_id),
  stone_shape_id uuid REFERENCES public.stone_shape_mast(shape_id),
  stone_size_id uuid REFERENCES public.stone_size_mast(size_id),
  quantity     int  NOT NULL,
  total_weight_carats numeric,
  quality_description_customer varchar,
  customer_stone_reference varchar,
  notes text,
  created_at   timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.customer_finding_receipt_items (
  item_id     uuid PRIMARY KEY       DEFAULT gen_random_uuid(),
  receipt_id  uuid REFERENCES public.customer_material_receipt(receipt_id),
  finding_description_customer varchar NOT NULL,
  quantity    numeric NOT NULL,
  uom_id      uuid REFERENCES public.uom_mast(uom_id),
  base_metal_type_id uuid REFERENCES public.metal_type_mast(metal_type_id),
  base_karat_id uuid REFERENCES public.karat_mast(karat_id),
  total_metal_weight_grams_per_unit numeric,
  embedded_stone_type_id uuid REFERENCES public.stone_type_mast(stone_type_id),
  embedded_stone_shape_id uuid REFERENCES public.stone_shape_mast(shape_id),
  embedded_stone_size_id uuid REFERENCES public.stone_size_mast(size_id),
  quantity_of_embedded_stones_per_unit int,
  notes        text,
  created_at   timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- --------------------------------------------------
-- 5. Metal inventory & pool
-- --------------------------------------------------
CREATE TABLE public.metal_pool (
  pool_id      uuid PRIMARY KEY       DEFAULT gen_random_uuid(),
  metal_type_id uuid REFERENCES public.metal_type_mast(metal_type_id),
  karat_id     uuid REFERENCES public.karat_mast(karat_id),
  initial_weight numeric NOT NULL,
  current_weight numeric NOT NULL,
  customer_id  uuid REFERENCES public.customer_mast(customer_id),
  uom_id       uuid REFERENCES public.uom_mast(uom_id),
  is_active    bool DEFAULT true,
  created_at   timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamp DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.metal_transaction_type_mast (
  type_id      uuid PRIMARY KEY       DEFAULT gen_random_uuid(),
  code         varchar NOT NULL UNIQUE,
  name         varchar NOT NULL,
  description  text,
  affects_pool bool NOT NULL,
  is_active    bool DEFAULT true,
  created_at   timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamp DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.metal_transactions (
  transaction_id uuid PRIMARY KEY     DEFAULT gen_random_uuid(),
  transaction_type_id uuid REFERENCES public.metal_transaction_type_mast(type_id),
  pool_id       uuid REFERENCES public.metal_pool(pool_id),
  related_transaction_id uuid REFERENCES public.metal_transactions(transaction_id),
  process_id    uuid REFERENCES public.process_mast(process_id),
  external_entity_id uuid,
  weight        numeric NOT NULL,
  fine_weight   numeric,
  uom_id        uuid REFERENCES public.uom_mast(uom_id),
  notes         text,
  created_by    uuid REFERENCES public.worker_mast(worker_id),
  worker_id     uuid REFERENCES public.worker_mast(worker_id),
  order_id      uuid REFERENCES public.orders(order_id),
  transaction_date timestamp DEFAULT CURRENT_TIMESTAMP,
  created_at    timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at    timestamp DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.metal_transformation (
  transformation_id uuid PRIMARY KEY  DEFAULT gen_random_uuid(),
  source_pool_id uuid REFERENCES public.metal_pool(pool_id),
  target_pool_id uuid REFERENCES public.metal_pool(pool_id),
  source_weight numeric NOT NULL,
  target_weight numeric NOT NULL,
  process_id    uuid REFERENCES public.process_mast(process_id),
  notes         text,
  created_by    uuid REFERENCES public.worker_mast(worker_id),
  transformation_date timestamp DEFAULT CURRENT_TIMESTAMP,
  created_at    timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at    timestamp DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.metal_inventory_transactions (
  transaction_id uuid PRIMARY KEY     DEFAULT gen_random_uuid(),
  transaction_type varchar NOT NULL,
  from_location_id uuid REFERENCES public.inventory_locations(location_id),
  to_location_id uuid REFERENCES public.inventory_locations(location_id),
  karat_id     uuid REFERENCES public.karat_mast(karat_id) NOT NULL,
  gold_colour_id uuid REFERENCES public.gold_colour_mast(gold_colour_id),
  weight_in_grams numeric NOT NULL,
  reference_number varchar,
  notes         text,
  created_by    uuid REFERENCES public.worker_mast(worker_id),
  order_id      uuid REFERENCES public.orders(order_id),
  transaction_date timestamptz DEFAULT CURRENT_TIMESTAMP,
  created_at    timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at    timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- --------------------------------------------------
-- 6. Order process flow & dust
-- --------------------------------------------------
CREATE TABLE public.order_process_flow (
  order_process_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id    uuid REFERENCES public.orders(order_id),
  process_id  uuid REFERENCES public.process_mast(process_id),
  parent_process_id uuid,
  worker_id   uuid REFERENCES public.worker_mast(worker_id),
  external_processor_id uuid REFERENCES public.external_processor_mast(processor_id),
  expected_start timestamptz,
  actual_start   timestamptz,
  expected_end   timestamptz,
  actual_end     timestamptz,
  iteration_no   int DEFAULT 1,
  status         varchar DEFAULT 'PENDING',
  created_at     timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at     timestamptz DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.dust_refine_batches (
  batch_id     uuid PRIMARY KEY       DEFAULT gen_random_uuid(),
  refine_date  date,
  total_in_grams numeric,
  total_out_grams numeric,
  recovery_pct numeric,
  notes        text,
  created_at   timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.dust_parcels (
  parcel_id    uuid PRIMARY KEY       DEFAULT gen_random_uuid(),
  collection_date date,
  origin_type  varchar,
  origin_id    uuid,
  weight_grams numeric,
  estimated_purity numeric,
  recovery_rate_pct numeric,
  refined      bool DEFAULT false,
  batch_id     uuid REFERENCES public.dust_refine_batches(batch_id),
  notes        text,
  created_at   timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- --------------------------------------------------
-- 7. Findings inventory & other leftover tables
-- --------------------------------------------------
CREATE TABLE public.findings_inventory (
  finding_id   uuid PRIMARY KEY       DEFAULT gen_random_uuid(),
  name         varchar NOT NULL,
  description  text,
  uom_id       uuid REFERENCES public.uom_mast(uom_id),
  quantity_on_hand int DEFAULT 0 NOT NULL,
  is_active    bool DEFAULT true,
  created_at   timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamp DEFAULT CURRENT_TIMESTAMP
);

-- --------------------------------------------------
-- 8. Activity log (needs user_roles)
-- --------------------------------------------------
CREATE TABLE public.activity_log (
  log_id       uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),
  user_id      uuid REFERENCES public.user_roles(user_id),
  activity_type varchar NOT NULL,
  entity_type  varchar NOT NULL,
  entity_id    uuid NOT NULL,
  old_value    jsonb,
  new_value    jsonb,
  ip_address   varchar,
  activity_time timestamptz DEFAULT CURRENT_TIMESTAMP,
  created_at   timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at   timestamptz DEFAULT CURRENT_TIMESTAMP
);

COMMIT;
