export interface ProductionSchedule {
  schedule_id: string;
  order_id: string;
  process_id: string;
  worker_id: string;
  planned_start_date: string;
  planned_end_date: string;
  actual_start_date?: string;
  actual_end_date?: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'delayed';
  priority: number;
}

export interface CapacitySlot {
  slot_id: string;
  process_id: string;
  date: string;
  total_capacity_hours: number;
  allocated_hours: number;
  available_hours: number;
  worker_allocations: {
    worker_id: string;
    allocated_hours: number;
    available_hours: number;
  }[];
}

export interface ProductionForecast {
  forecast_id: string;
  process_id: string;
  date: string;
  expected_orders: number;
  required_capacity_hours: number;
  available_capacity_hours: number;
  capacity_utilization: number;
}

export interface SchedulingConstraint {
  constraint_id: string;
  process_id: string;
  min_workers_required: number;
  max_workers_allowed: number;
  skill_level_required: number;
  setup_time_minutes: number;
  standard_time_minutes: number;
}
