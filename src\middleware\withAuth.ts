import { NextRequest, NextResponse } from 'next/server';
import { UserRole } from '@/hooks/useAuth';

type NextApiHandler<T = any, C extends { params?: any } = { params?: any }> = (
  req: NextRequest,
  context: C
) => Promise<NextResponse<T>> | NextResponse<T>;

/**
 * Authentication middleware that checks if user has required role
 * Currently configured for testing with mock admin role
 * 
 * @param {UserRole[]} allowedRoles - Array of roles that can access the route
 * @returns {Function} Middleware function that wraps the route handler
 * 
 * @example
 * ```typescript
 * export const POST = withAuth(['admin'])(async (req, context) => {
 *   // Handle authenticated request
 * });
 * ```
 */
export function withAuth(allowedRoles: UserRole[]) {
  return function <T, C extends { params?: any }>(
    handler: NextApiHandler<T, C>
  ): NextApiHandler<T, C> {
    return async function (req: NextRequest, context: C) {
      // For testing: Skip auth check and assume admin role
      req.headers.set('x-user-id', 'mock-admin-id');
      req.headers.set('x-user-role', 'admin');
      
      return handler(req, context);
    };
  };
}

/**
 * Audit logging middleware that adds user and timestamp information to requests
 * 
 * @returns {Function} Middleware function that wraps the route handler
 * @throws {401} If user is not authenticated
 * 
 * @example
 * ```typescript
 * export const POST = withAuditLog()(async (req, context) => {
 *   // Handle request with audit logging
 * });
 * ```
 */
export function withAuditLog() {
  return function <T, C extends { params?: any }>(
    handler: NextApiHandler<T, C>
  ): NextApiHandler<T, C> {
    return async function (req: NextRequest, context: C) {
      const userId = req.headers.get('x-user-id');

      if (!userId) {
        // Cast error object to T to satisfy the generic return type
        return NextResponse.json(
          { error: 'Unauthorized' } as T, 
          { status: 401 }
        );
      }

      // Add audit context to request
      req.headers.set('x-audit-user', userId);
      req.headers.set('x-audit-time', new Date().toISOString());

      return handler(req, context);
    };
  };
}
