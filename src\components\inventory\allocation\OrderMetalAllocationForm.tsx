/**
 * OrderMetalAllocationForm Component
 * Form for allocating metals from pools to specific orders
 * 
 * @module components/inventory/allocation
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { OrderMetalAllocation, MetalPool } from '@/types/inventory';
import { useToast } from '@/hooks/useToast';

// Interface for the form's select options
interface SelectOption {
  value: string;
  label: string;
}

/**
 * Props for OrderMetalAllocationForm component
 */
interface OrderMetalAllocationFormProps {
  initialData?: Partial<OrderMetalAllocation>;
  orderId?: string;
  onSubmit?: (data: Partial<OrderMetalAllocation>) => void;
  onCancel?: () => void;
}

/**
 * OrderMetalAllocationForm Component
 * Form for allocating metals from pools to specific orders
 */
export const OrderMetalAllocationForm: React.FC<OrderMetalAllocationFormProps> = ({
  initialData,
  orderId,
  onSubmit,
  onCancel
}) => {
  const [formData, setFormData] = useState<Partial<OrderMetalAllocation>>(
    initialData || (orderId ? { order_id: orderId } : {})
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orders, setOrders] = useState<SelectOption[]>([]);
  const [metalPools, setMetalPools] = useState<MetalPool[]>([]);
  const [filteredPools, setFilteredPools] = useState<MetalPool[]>([]);
  const [uoms, setUoms] = useState<SelectOption[]>([]);
  const [customers, setCustomers] = useState<SelectOption[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [loading, setLoading] = useState({
    orders: true,
    pools: true,
    uoms: true,
    customers: true
  });
  const { showToast } = useToast();
  const router = useRouter();

  // Fetch initial data
  useEffect(() => {
    fetchCustomers();
    fetchUoms();
    
    if (orderId) {
      fetchOrderDetails(orderId);
    } else {
      fetchOrders();
    }
  }, [orderId]);

  // Filter pools when customer is selected or changed
  useEffect(() => {
    if (selectedCustomerId) {
      setFilteredPools(metalPools.filter(pool => pool.customer_id === selectedCustomerId));
    } else {
      setFilteredPools(metalPools);
    }
  }, [selectedCustomerId, metalPools]);

  // Set customer ID when order is selected
  useEffect(() => {
    if (formData.order_id) {
      fetchOrderDetails(formData.order_id);
    }
  }, [formData.order_id]);

  /**
   * Fetches all customers
   */
  const fetchCustomers = async () => {
    try {
      setLoading(prev => ({ ...prev, customers: true }));
      const response = await fetch('/api/masters/customers');
      
      if (!response.ok) {
        throw new Error('Failed to fetch customers');
      }
      
      const data = await response.json();
      const options = data.map((customer: any) => ({
        value: customer.customer_id,
        label: customer.customer_name
      }));
      
      setCustomers(options);
    } catch (error) {
      console.error('Error fetching customers:', error);
      showToast({
        title: 'Error',
        description: 'Error loading customers',
        type: 'destructive'
      });
    } finally {
      setLoading(prev => ({ ...prev, customers: false }));
    }
  };

  /**
   * Fetches all orders
   */
  const fetchOrders = async () => {
    try {
      setLoading(prev => ({ ...prev, orders: true }));
      const response = await fetch('/api/orders');
      
      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }
      
      const data = await response.json();
      const options = data.map((order: any) => ({
        value: order.order_id,
        label: `${order.order_id.substring(0, 8)}... - ${order.style_code || 'No Style'}`
      }));
      
      setOrders(options);
    } catch (error) {
      console.error('Error fetching orders:', error);
      showToast({
        title: 'Error',
        description: 'Error loading orders',
        type: 'destructive'
      });
    } finally {
      setLoading(prev => ({ ...prev, orders: false }));
    }
  };

  /**
   * Fetches details for a specific order
   * @param id - The order ID
   */
  const fetchOrderDetails = async (id: string) => {
    try {
      const response = await fetch(`/api/orders/${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch order details');
      }
      
      const order = await response.json();
      setSelectedCustomerId(order.customer_id);
      
      // Fetch metal pools for this customer
      fetchMetalPools(order.customer_id);
    } catch (error) {
      console.error('Error fetching order details:', error);
      showToast({
        title: 'Error',
        description: 'Error loading order details',
        type: 'destructive'
      });
    }
  };

  /**
   * Fetches metal pools for a specific customer
   * @param customerId - The customer ID
   */
  const fetchMetalPools = async (customerId: string) => {
    try {
      setLoading(prev => ({ ...prev, pools: true }));
      const response = await fetch(`/api/inventory/metal-pools?customerId=${customerId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch metal pools');
      }
      
      const data = await response.json();
      setMetalPools(data);
      setFilteredPools(data);
    } catch (error) {
      console.error('Error fetching metal pools:', error);
      showToast({
        title: 'Error',
        description: 'Error loading metal pools',
        type: 'destructive'
      });
    } finally {
      setLoading(prev => ({ ...prev, pools: false }));
    }
  };

  /**
   * Fetches all units of measure
   */
  const fetchUoms = async () => {
    try {
      setLoading(prev => ({ ...prev, uoms: true }));
      const response = await fetch('/api/inventory/masters/uoms');
      
      if (!response.ok) {
        throw new Error('Failed to fetch units of measure');
      }
      
      const data = await response.json();
      const options = data.map((uom: any) => ({
        value: uom.uom_id,
        label: `${uom.name} (${uom.code})`
      }));
      
      setUoms(options);
    } catch (error) {
      console.error('Error fetching units of measure:', error);
      showToast({
        title: 'Error',
        description: 'Error loading units of measure',
        type: 'destructive'
      });
    } finally {
      setLoading(prev => ({ ...prev, uoms: false }));
    }
  };

  /**
   * Handles form input changes
   * @param e - The input change event
   */
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    // Handle special case for pool_id
    if (name === 'pool_id' && value) {
      const selectedPool = metalPools.find(pool => pool.pool_id === value);
      if (selectedPool) {
        setFormData(prev => ({
          ...prev,
          [name]: value,
          metal_type_id: selectedPool.metal_type_id,
          purity: selectedPool.karat?.purity, // Access purity via nested karat object
        }));
        return;
      }
    }
    
    if (name === 'order_id') {
      const selectedOrderOption = orders.find(o => o.value === value);
      const customerId = selectedOrderOption ? JSON.parse(selectedOrderOption.label.split(' - CustID: ')[1] || 'null') : null;
      setSelectedCustomerId(customerId);
      // Reset pool selection when order changes
      setFormData(prev => ({ ...prev, order_id: value, pool_id: '', metal_type_id: undefined, purity: undefined, allocated_weight: undefined }));
      return; // Exit early after setting order and customer ID
    }

    setFormData(prev => ({
      ...prev,
      [name]: name === 'allocated_weight' ? parseFloat(value) || 0 : value, // Ensure weight is a number
    }));
  };

  /**
   * Handles form submission
   * @param e - The form submit event
   */
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Set the allocation date if not provided
      // Set default values if not present
      const dataToSend = {
        ...formData,
        allocation_date: formData.allocation_date || new Date().toISOString(),
        status: formData.status || 'ALLOCATED', // Set default status
      };

      // If onSubmit callback is provided, use it
      if (onSubmit) {
        onSubmit(formData);
        return;
      }
      
      // Otherwise, submit to the API directly
      const method = initialData?.allocation_id ? 'PUT' : 'POST';
      const url = '/api/inventory/order-allocations';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(dataToSend) // Send data with default status
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save metal allocation');
      }
      
      showToast({
        title: 'Success',
        description: `Metal allocation ${initialData?.allocation_id ? 'updated' : 'created'} successfully`,
        type: 'success'
      });
      
      // Redirect to order allocations list or order details
      if (formData.order_id) {
        router.push(`/orders/${formData.order_id}/materials`);
      } else {
        router.push('/inventory/order-allocations');
      }
    } catch (error: any) {
      console.error('Error saving metal allocation:', error);
      showToast({
        title: 'Error',
        description: error.message || 'Error submitting allocation data',
        type: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handles cancellation of the form
   */
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else if (formData.order_id) {
      router.push(`/orders/${formData.order_id}/materials`);
    } else {
      router.push('/inventory/order-allocations');
    }
  };

  // Check if any data is still loading
  const isLoading = Object.values(loading).some(status => status);

  return (
    <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-6">
        {initialData?.allocation_id ? 'Edit' : 'New'} Metal Allocation
      </h2>
      
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Order Field */}
            <div>
              <label htmlFor="order_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Order <span className="text-red-500">*</span>
              </label>
              <select
                id="order_id"
                name="order_id"
                value={formData.order_id || ''}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
                disabled={isSubmitting || !!orderId}
              >
                <option value="">Select Order</option>
                {orders.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Metal Pool Field */}
            <div>
              <label htmlFor="pool_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Metal Pool <span className="text-red-500">*</span>
              </label>
              <select
                id="pool_id"
                name="pool_id"
                value={formData.pool_id || ''}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
                disabled={isSubmitting || !selectedCustomerId}
              >
                <option value="">Select Metal Pool</option>
                {filteredPools.map((pool) => (
                  <option key={pool.pool_id} value={pool.pool_id}>
                    {`${pool.metal_type?.name || 'Unknown Type'} - ${pool.karat?.purity || 'N/A'}% - Balance: ${pool.current_weight} ${pool.uom?.code || ''}`}
                  </option>
                ))}
              </select>
              {!selectedCustomerId && (
                <p className="mt-1 text-sm text-amber-600 dark:text-amber-400">
                  Please select an order first to see available metal pools
                </p>
              )}
            </div>
            
            {/* Weight Field */}
            <div>
              <label htmlFor="allocated_weight" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Weight <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                id="allocated_weight"
                name="allocated_weight"
                value={formData.allocated_weight || ''}
                onChange={handleChange}
                min="0.001"
                step="0.001"
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                placeholder="Enter weight"
                required
                disabled={isSubmitting}
              />
            </div>
            
            {/* Unit of Measure Field */}
            <div>
              <label htmlFor="uom_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Unit of Measure <span className="text-red-500">*</span>
              </label>
              <select
                id="uom_id"
                name="uom_id"
                value={formData.uom_id || ''}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
                disabled={isSubmitting}
              >
                <option value="">Select Unit</option>
                {uoms.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Allocation Date Field */}
            <div>
              <label htmlFor="allocation_date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Allocation Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="allocation_date"
                name="allocation_date"
                value={formData.allocation_date 
                  ? new Date(formData.allocation_date).toISOString().split('T')[0] 
                  : new Date().toISOString().split('T')[0]
                }
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
                disabled={isSubmitting}
              />
            </div>
            
            {/* Notes Field */}
            <div className="md:col-span-2">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes || ''}
                onChange={handleChange}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                placeholder="Enter notes about this allocation"
                disabled={isSubmitting}
              />
            </div>
          </div>
          
          {/* Form Actions */}
          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : initialData?.allocation_id ? 'Update' : 'Allocate Metal'}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};
