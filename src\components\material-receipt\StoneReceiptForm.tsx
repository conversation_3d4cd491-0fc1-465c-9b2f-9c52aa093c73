/**
 * @module components/material-receipt/StoneReceiptForm
 * @description Stone Receipt Form Component - Receive stones back from workers with loss tracking
 */

'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertTriangleIcon, CheckCircleIcon } from 'lucide-react';
import { receiveStones, getIssuedStones } from '@/services/stoneInventoryService';

const stoneReceiptSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  order_id: z.string().min(1, 'Order is required'),
  worker_id: z.string().min(1, 'Worker is required'),
  process_id: z.string().min(1, 'Process is required'),
  notes: z.string().optional()
});

interface StoneReceiptFormData {
  customer_id: string;
  order_id: string;
  worker_id: string;
  process_id: string;
  notes?: string;
}

interface IssuedStoneItem {
  stone_transaction_id: string;
  stone_inventory_id: string;
  quantity_issued: number;
  weight_carats_issued: number;
  issue_date: string;
  stone_details?: any;
  stone_shape?: any;
  stone_size?: any;
  stone_quality?: any;
}

interface StoneReceiptItem extends IssuedStoneItem {
  quantity_received: number;
  weight_carats_received: number;
  quantity_loss: number;
  weight_loss_carats: number;
  loss_reason?: string;
}

export function StoneReceiptForm() {
  const [customers, setCustomers] = useState([]);
  const [orders, setOrders] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [processes, setProcesses] = useState([]);
  const [issuedStones, setIssuedStones] = useState<IssuedStoneItem[]>([]);
  const [receiptItems, setReceiptItems] = useState<StoneReceiptItem[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState('');
  const [selectedOrderId, setSelectedOrderId] = useState('');
  const [selectedWorkerId, setSelectedWorkerId] = useState('');
  const [selectedProcessId, setSelectedProcessId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<StoneReceiptFormData>({
    resolver: zodResolver(stoneReceiptSchema),
    defaultValues: {
      customer_id: '',
      order_id: '',
      worker_id: '',
      process_id: '',
      notes: ''
    }
  });

  // Load master data on component mount
  useEffect(() => {
    loadMasterData();
  }, []);

  // Load issued stones when all required fields are selected
  useEffect(() => {
    if (selectedCustomerId && selectedOrderId && selectedWorkerId && selectedProcessId) {
      loadIssuedStones();
    }
  }, [selectedCustomerId, selectedOrderId, selectedWorkerId, selectedProcessId]);

  const loadMasterData = async () => {
    try {
      setIsLoading(true);
      
      // Load all master data in parallel
      const [customersRes, workersRes, processesRes] = await Promise.all([
        fetch('/api/masters/customers').then(res => res.json()),
        fetch('/api/masters/workers').then(res => res.json()),
        fetch('/api/masters/processes').then(res => res.json())
      ]);

      setCustomers(customersRes.data || []);
      setWorkers(workersRes.data || []);
      setProcesses(processesRes.data || []);

    } catch (error) {
      console.error('Error loading master data:', error);
      toast.error('Failed to load master data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCustomerOrders = async (customerId: string) => {
    try {
      const response = await fetch(`/api/orders?customer_id=${customerId}&status=active`);
      const data = await response.json();
      setOrders(data.data || []);
    } catch (error) {
      console.error('Error loading customer orders:', error);
      toast.error('Failed to load customer orders');
    }
  };

  const loadIssuedStones = async () => {
    try {
      const stones = await getIssuedStones({
        customer_id: selectedCustomerId,
        order_id: selectedOrderId,
        worker_id: selectedWorkerId,
        process_id: selectedProcessId
      });

      setIssuedStones(stones);
      
      // Initialize receipt items with issued quantities
      const initialReceiptItems: StoneReceiptItem[] = stones.map(stone => ({
        ...stone,
        quantity_received: stone.quantity_issued,
        weight_carats_received: stone.weight_carats_issued,
        quantity_loss: 0,
        weight_loss_carats: 0,
        loss_reason: ''
      }));
      
      setReceiptItems(initialReceiptItems);

    } catch (error) {
      console.error('Error loading issued stones:', error);
      toast.error('Failed to load issued stones');
    }
  };

  const handleCustomerChange = (customerId: string) => {
    setSelectedCustomerId(customerId);
    form.setValue('customer_id', customerId);
    resetDependentFields();
    loadCustomerOrders(customerId);
  };

  const handleOrderChange = (orderId: string) => {
    setSelectedOrderId(orderId);
    form.setValue('order_id', orderId);
    setIssuedStones([]);
    setReceiptItems([]);
  };

  const handleWorkerChange = (workerId: string) => {
    setSelectedWorkerId(workerId);
    form.setValue('worker_id', workerId);
    setIssuedStones([]);
    setReceiptItems([]);
  };

  const handleProcessChange = (processId: string) => {
    setSelectedProcessId(processId);
    form.setValue('process_id', processId);
    setIssuedStones([]);
    setReceiptItems([]);
  };

  const resetDependentFields = () => {
    form.setValue('order_id', '');
    setSelectedOrderId('');
    setOrders([]);
    setIssuedStones([]);
    setReceiptItems([]);
  };

  const updateReceiptItem = (index: number, field: keyof StoneReceiptItem, value: any) => {
    setReceiptItems(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };
      
      // Calculate losses when quantities change
      if (field === 'quantity_received') {
        updated[index].quantity_loss = Math.max(0, updated[index].quantity_issued - value);
      }
      if (field === 'weight_carats_received') {
        updated[index].weight_loss_carats = Math.max(0, updated[index].weight_carats_issued - value);
      }
      
      return updated;
    });
  };

  const onSubmit = async (data: StoneReceiptFormData) => {
    if (receiptItems.length === 0) {
      toast.error('No stones to receive');
      return;
    }

    // Validate receipt quantities
    const invalidItems = receiptItems.filter(item => 
      item.quantity_received < 0 || 
      item.weight_carats_received < 0 ||
      item.quantity_received > item.quantity_issued ||
      item.weight_carats_received > item.weight_carats_issued
    );

    if (invalidItems.length > 0) {
      toast.error('Invalid receipt quantities detected. Please check your entries.');
      return;
    }

    try {
      setIsSubmitting(true);

      // Process each receipt item
      for (const item of receiptItems) {
        await receiveStones({
          transaction_id: item.stone_transaction_id,
          stones_received: [{
            stone_inventory_id: item.stone_inventory_id,
            returned_quantity: item.quantity_received,
            returned_weight_carats: item.weight_carats_received,
            condition: item.quantity_loss > 0 || item.weight_loss_carats > 0 ? 'damaged' : 'good',
            notes: item.loss_reason
          }],
          received_by: 'current_user', // TODO: Get from auth context
          notes: data.notes
        });
      }

      toast.success('Stones received successfully');
      
      // Reset form
      form.reset();
      setSelectedCustomerId('');
      setSelectedOrderId('');
      setSelectedWorkerId('');
      setSelectedProcessId('');
      setIssuedStones([]);
      setReceiptItems([]);
      
    } catch (error) {
      console.error('Error receiving stones:', error);
      toast.error('Failed to receive stones');
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalIssuedQuantity = receiptItems.reduce((sum, item) => sum + item.quantity_issued, 0);
  const totalReceivedQuantity = receiptItems.reduce((sum, item) => sum + item.quantity_received, 0);
  const totalLossQuantity = receiptItems.reduce((sum, item) => sum + item.quantity_loss, 0);
  const totalIssuedWeight = receiptItems.reduce((sum, item) => sum + item.weight_carats_issued, 0);
  const totalReceivedWeight = receiptItems.reduce((sum, item) => sum + item.weight_carats_received, 0);
  const totalLossWeight = receiptItems.reduce((sum, item) => sum + item.weight_loss_carats, 0);

  const hasLosses = totalLossQuantity > 0 || totalLossWeight > 0;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Receive Stones from Worker</CardTitle>
          <CardDescription>
            Receive stones back from worker after processing with loss tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Customer Selection */}
              <div className="space-y-2">
                <Label htmlFor="customer_id">Customer *</Label>
                <Select
                  {...form.register('customer_id')}
                  onChange={(e) => handleCustomerChange(e.target.value)}
                  options={[
                    { value: '', label: 'Select customer' },
                    ...customers.map((customer: any) => ({
                      value: customer.customer_id,
                      label: customer.customer_name
                    }))
                  ]}
                />
              </div>

              {/* Order Selection */}
              <div className="space-y-2">
                <Label htmlFor="order_id">Order *</Label>
                <Select
                  {...form.register('order_id')}
                  disabled={!selectedCustomerId}
                  onChange={(e) => handleOrderChange(e.target.value)}
                  options={[
                    { value: '', label: 'Select order' },
                    ...orders.map((order: any) => ({
                      value: order.order_id,
                      label: `${order.order_no} - ${order.style_code}`
                    }))
                  ]}
                />
              </div>

              {/* Worker Selection */}
              <div className="space-y-2">
                <Label htmlFor="worker_id">Worker *</Label>
                <Select
                  {...form.register('worker_id')}
                  onChange={(e) => handleWorkerChange(e.target.value)}
                  options={[
                    { value: '', label: 'Select worker' },
                    ...workers.map((worker: any) => ({
                      value: worker.worker_id,
                      label: worker.worker_name
                    }))
                  ]}
                />
              </div>

              {/* Process Selection */}
              <div className="space-y-2">
                <Label htmlFor="process_id">Process *</Label>
                <Select
                  {...form.register('process_id')}
                  onChange={(e) => handleProcessChange(e.target.value)}
                  options={[
                    { value: '', label: 'Select process' },
                    ...processes.map((process: any) => ({
                      value: process.process_id,
                      label: process.process_name
                    }))
                  ]}
                />
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                {...form.register('notes')}
                placeholder="Enter any additional notes..."
                rows={3}
              />
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Issued Stones for Receipt */}
      {receiptItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Stone Receipt Details</CardTitle>
            <CardDescription>
              Update the received quantities and specify any losses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Stone Details</TableHead>
                    <TableHead>Issued Qty</TableHead>
                    <TableHead>Issued Weight (ct)</TableHead>
                    <TableHead>Received Qty</TableHead>
                    <TableHead>Received Weight (ct)</TableHead>
                    <TableHead>Loss Qty</TableHead>
                    <TableHead>Loss Weight (ct)</TableHead>
                    <TableHead>Loss Reason</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {receiptItems.map((item, index) => (
                    <TableRow key={item.stone_transaction_id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center space-x-1">
                            <Badge variant="secondary" className="text-xs">
                              {item.stone_details?.type_name || 'Unknown'}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {item.stone_shape?.shape_name || 'Unknown'}
                            </Badge>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Badge variant="outline" className="text-xs">
                              {item.stone_size?.size_name || 'Unknown'}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {item.stone_quality?.quality_name || 'Unknown'}
                            </Badge>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {item.quantity_issued}
                      </TableCell>
                      <TableCell className="font-medium">
                        {item.weight_carats_issued.toFixed(3)}
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          min="0"
                          max={item.quantity_issued}
                          value={item.quantity_received}
                          onChange={(e) => updateReceiptItem(index, 'quantity_received', parseInt(e.target.value) || 0)}
                          className="w-20"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          step="0.001"
                          min="0"
                          max={item.weight_carats_issued}
                          value={item.weight_carats_received}
                          onChange={(e) => updateReceiptItem(index, 'weight_carats_received', parseFloat(e.target.value) || 0)}
                          className="w-24"
                        />
                      </TableCell>
                      <TableCell>
                        <Badge variant={item.quantity_loss > 0 ? "destructive" : "secondary"}>
                          {item.quantity_loss}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={item.weight_loss_carats > 0 ? "destructive" : "secondary"}>
                          {item.weight_loss_carats.toFixed(3)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {(item.quantity_loss > 0 || item.weight_loss_carats > 0) && (
                          <Input
                            placeholder="Loss reason"
                            value={item.loss_reason || ''}
                            onChange={(e) => updateReceiptItem(index, 'loss_reason', e.target.value)}
                            className="w-32"
                          />
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Receipt Summary */}
      {receiptItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Receipt Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Issued Quantity</div>
                <div className="text-2xl font-bold">{totalIssuedQuantity} pcs</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Received Quantity</div>
                <div className="text-2xl font-bold text-green-600">{totalReceivedQuantity} pcs</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Loss Quantity</div>
                <div className={`text-2xl font-bold ${hasLosses ? 'text-red-600' : 'text-green-600'}`}>
                  {totalLossQuantity} pcs
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Issued Weight</div>
                <div className="text-2xl font-bold">{totalIssuedWeight.toFixed(3)} ct</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Received Weight</div>
                <div className="text-2xl font-bold text-green-600">{totalReceivedWeight.toFixed(3)} ct</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Loss Weight</div>
                <div className={`text-2xl font-bold ${hasLosses ? 'text-red-600' : 'text-green-600'}`}>
                  {totalLossWeight.toFixed(3)} ct
                </div>
              </div>
            </div>

            {hasLosses && (
              <Alert variant="destructive" className="mt-4">
                <AlertTriangleIcon className="h-4 w-4" />
                <AlertDescription>
                  Loss detected: {totalLossQuantity} pieces, {totalLossWeight.toFixed(3)} carats. 
                  Please ensure loss reasons are provided.
                </AlertDescription>
              </Alert>
            )}

            {!hasLosses && receiptItems.length > 0 && (
              <Alert className="mt-4">
                <CheckCircleIcon className="h-4 w-4" />
                <AlertDescription>
                  No losses detected. All stones accounted for.
                </AlertDescription>
              </Alert>
            )}
            
            <Separator className="my-4" />
            
            <div className="flex justify-end space-x-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  form.reset();
                  setSelectedCustomerId('');
                  setSelectedOrderId('');
                  setSelectedWorkerId('');
                  setSelectedProcessId('');
                  setReceiptItems([]);
                }}
              >
                Clear Form
              </Button>
              <Button 
                onClick={form.handleSubmit(onSubmit)}
                disabled={isSubmitting || receiptItems.length === 0}
              >
                {isSubmitting ? 'Processing...' : 'Receive Stones'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Issued Stones Message */}
      {selectedCustomerId && selectedOrderId && selectedWorkerId && selectedProcessId && 
       issuedStones.length === 0 && !isLoading && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                No stones found issued to this worker for the selected order and process.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
