/**
 * @module components/material-issue/MetalIssueForm
 * @description Metal Issue Form Component - Issue gold/silver metals with purity tracking
 */

'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// Removed incorrect Select imports - using native select elements instead
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';
import { issueMaterial } from '@/services/materialTransactionService';

const metalIssueSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  order_id: z.string().min(1, 'Order is required'),
  worker_id: z.string().min(1, 'Worker is required'),
  process_id: z.string().min(1, 'Process is required'),
  metal_type_id: z.string().min(1, 'Metal type is required'),
  karat_id: z.string().min(1, 'Karat is required'),
  weight_grams: z.number().min(0.001, 'Weight must be greater than 0'),
  expected_loss_percentage: z.number().min(0).max(100).optional(),
  notes: z.string().optional()
});

interface MetalIssueFormData {
  customer_id: string;
  order_id: string;
  worker_id: string;
  process_id: string;
  metal_type_id: string;
  karat_id: string;
  weight_grams: number;
  expected_loss_percentage?: number;
  notes?: string;
}

interface MetalInventoryItem {
  metal_inventory_id: string;
  metal_type_id: string;
  karat_id: string;
  weight_grams: number;
  purity_percentage: number;
  current_location: string;
  status: string;
  customer_id: string;
  metal_type?: {
    metal_name: string;
  };
  karat?: {
    karat_name: string;
    purity_percentage: number;
  };
  customer?: {
    customer_name: string;
  };
}

export function MetalIssueForm() {
  const [customers, setCustomers] = useState<any[]>([]);
  const [orders, setOrders] = useState<any[]>([]);
  const [workers, setWorkers] = useState<any[]>([]);
  const [processes, setProcesses] = useState<any[]>([]);
  const [metalTypes, setMetalTypes] = useState<any[]>([]);
  const [karats, setKarats] = useState<any[]>([]);
  const [availableMetals, setAvailableMetals] = useState<MetalInventoryItem[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState('');
  const [selectedMetalType, setSelectedMetalType] = useState('');
  const [selectedKarat, setSelectedKarat] = useState('');
  const [selectedProcess, setSelectedProcess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<MetalIssueFormData>({
    resolver: zodResolver(metalIssueSchema),
    defaultValues: {
      customer_id: '',
      order_id: '',
      worker_id: '',
      process_id: '',
      metal_type_id: '',
      karat_id: '',
      weight_grams: 0,
      expected_loss_percentage: 0,
      notes: ''
    }
  });

  // Load master data on component mount
  useEffect(() => {
    loadMasterData();
  }, []);

  // Load available metals when customer/metal/karat changes
  useEffect(() => {
    if (selectedCustomerId && selectedMetalType && selectedKarat) {
      loadAvailableMetals();
    }
  }, [selectedCustomerId, selectedMetalType, selectedKarat]);

  // Set expected loss percentage when process changes
  useEffect(() => {
    if (selectedProcess) {
      setExpectedLossPercentage();
    }
  }, [selectedProcess]);

  const loadMasterData = async () => {
    try {
      setIsLoading(true);
      
      // Load all master data in parallel
      const [customersRes, workersRes, processesRes, metalTypesRes, karatsRes] = await Promise.all([
        fetch('/api/masters/customers').then(res => res.json()),
        fetch('/api/masters/workers').then(res => res.json()),
        fetch('/api/masters/processes').then(res => res.json()),
        fetch('/api/masters/metal-types').then(res => res.json()),
        fetch('/api/masters/karats').then(res => res.json())
      ]);

      setCustomers(customersRes.data || []);
      setWorkers(workersRes.data || []);
      setProcesses(processesRes.data || []);
      setMetalTypes(metalTypesRes.data || []);
      setKarats(karatsRes.data || []);

    } catch (error) {
      console.error('Error loading master data:', error);
      toast.error('Failed to load master data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCustomerOrders = async (customerId: string) => {
    try {
      const response = await fetch(`/api/orders?customer_id=${customerId}&status=active`);
      const data = await response.json();
      setOrders(data.data || []);
    } catch (error) {
      console.error('Error loading customer orders:', error);
      toast.error('Failed to load customer orders');
    }
  };

  const loadAvailableMetals = async () => {
    if (!selectedCustomerId || !selectedMetalType || !selectedKarat) return;

    try {
      const response = await fetch(
        `/api/inventory/metals?customer_id=${selectedCustomerId}&metal_type_id=${selectedMetalType}&karat_id=${selectedKarat}&status=available`
      );
      const data = await response.json();
      setAvailableMetals(data.data || []);
    } catch (error) {
      console.error('Error loading available metals:', error);
      toast.error('Failed to load available metals');
    }
  };

  const setExpectedLossPercentage = () => {
    const process = processes.find((p: any) => p.process_id === selectedProcess);
    if (process && typeof process.default_wastage_percentage === 'number') {
      form.setValue('expected_loss_percentage', process.default_wastage_percentage);
    }
  };

  const handleCustomerChange = (customerId: string) => {
    setSelectedCustomerId(customerId);
    form.setValue('customer_id', customerId);
    form.setValue('order_id', '');
    setOrders([]);
    setAvailableMetals([]);
    loadCustomerOrders(customerId);
  };

  const handleMetalTypeChange = (metalTypeId: string) => {
    setSelectedMetalType(metalTypeId);
    form.setValue('metal_type_id', metalTypeId);
    setAvailableMetals([]);
  };

  const handleKaratChange = (karatId: string) => {
    setSelectedKarat(karatId);
    form.setValue('karat_id', karatId);
    setAvailableMetals([]);
  };

  const handleProcessChange = (processId: string) => {
    setSelectedProcess(processId);
    form.setValue('process_id', processId);
  };

  const onSubmit = async (data: MetalIssueFormData) => {
    // Validate sufficient metal availability
    const totalAvailableWeight = availableMetals.reduce((sum, metal) => sum + metal.weight_grams, 0);
    
    if (data.weight_grams > totalAvailableWeight) {
      toast.error(`Insufficient metal available. Available: ${totalAvailableWeight.toFixed(3)}g, Requested: ${data.weight_grams.toFixed(3)}g`);
      return;
    }

    try {
      setIsSubmitting(true);

      await issueMaterial({
        customer_id: data.customer_id,
        order_id: data.order_id,
        material_type: 'metal',
        metal_type_id: data.metal_type_id,
        karat_id: data.karat_id,
        weight_grams: data.weight_grams,
        worker_id: data.worker_id,
        process_id: data.process_id,
        expected_loss_percentage: data.expected_loss_percentage || 0,
        issued_by: 'current_user', // TODO: Get from auth context
        notes: data.notes
      });

      toast.success('Metal issued successfully');
      
      // Reset form
      form.reset();
      setSelectedCustomerId('');
      setSelectedMetalType('');
      setSelectedKarat('');
      setSelectedProcess('');
      setAvailableMetals([]);
      
    } catch (error) {
      console.error('Error issuing metal:', error);
      toast.error('Failed to issue metal');
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalAvailableWeight = availableMetals.reduce((sum, metal) => sum + metal.weight_grams, 0);
  const requestedWeight = form.watch('weight_grams') || 0;
  const remainingWeight = totalAvailableWeight - requestedWeight;
  const isWeightValid = requestedWeight > 0 && requestedWeight <= totalAvailableWeight;

  const selectedKaratData = karats.find((k: any) => k.karat_id === selectedKarat);
  const expectedLossPercentage = form.watch('expected_loss_percentage') || 0;
  const expectedReturnWeight = requestedWeight * (1 - expectedLossPercentage / 100);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Issue Metal to Worker</CardTitle>
          <CardDescription>
            Issue gold/silver metals for processing with purity and loss tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Customer Selection */}
              <div className="space-y-2">
                <Label htmlFor="customer_id">Customer *</Label>
                <select
                  id="customer_id"
                  value={selectedCustomerId}
                  onChange={(e) => handleCustomerChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select customer</option>
                  {customers.map((customer: any) => (
                    <option key={customer.customer_id} value={customer.customer_id}>
                      {customer.customer_name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Order Selection */}
              <div className="space-y-2">
                <Label htmlFor="order_id">Order *</Label>
                <select
                  id="order_id"
                  disabled={!selectedCustomerId}
                  {...form.register('order_id')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 disabled:opacity-50"
                >
                  <option value="">Select order</option>
                  {orders.map((order: any) => (
                    <option key={order.order_id} value={order.order_id}>
                      {order.order_no} - {order.style_code}
                    </option>
                  ))}
                </select>
              </div>

              {/* Worker Selection */}
              <div className="space-y-2">
                <Label htmlFor="worker_id">Worker *</Label>
                <select
                  id="worker_id"
                  {...form.register('worker_id')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select worker</option>
                  {workers.map((worker: any) => (
                    <option key={worker.worker_id} value={worker.worker_id}>
                      {worker.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Process Selection */}
              <div className="space-y-2">
                <Label htmlFor="process_id">Process *</Label>
                <select
                  id="process_id"
                  value={selectedProcess}
                  onChange={(e) => handleProcessChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select process</option>
                  {processes.map((process: any) => (
                    <option key={process.process_id} value={process.process_id}>
                      {process.name}
                      {process.default_wastage_percentage && ` (~${process.default_wastage_percentage}% loss)`}
                    </option>
                  ))}
                </select>
              </div>

              {/* Metal Type Selection */}
              <div className="space-y-2">
                <Label htmlFor="metal_type_id">Metal Type *</Label>
                <select
                  id="metal_type_id"
                  value={selectedMetalType}
                  onChange={(e) => handleMetalTypeChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select metal type</option>
                  {metalTypes.map((metalType: any) => (
                    <option key={metalType.metal_type_id} value={metalType.metal_type_id}>
                      {metalType.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Karat Selection */}
              <div className="space-y-2">
                <Label htmlFor="karat_id">Karat/Purity *</Label>
                <select
                  id="karat_id"
                  value={selectedKarat}
                  onChange={(e) => handleKaratChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select karat</option>
                  {karats.map((karat: any) => (
                    <option key={karat.karat_id} value={karat.karat_id}>
                      {karat.karat_name} ({karat.purity_percentage}%)
                    </option>
                  ))}
                </select>
              </div>

              {/* Weight Input */}
              <div className="space-y-2">
                <Label htmlFor="weight_grams">Weight (grams) *</Label>
                <Input
                  type="number"
                  step="0.001"
                  min="0.001"
                  {...form.register('weight_grams', { valueAsNumber: true })}
                  placeholder="0.000"
                  className={!isWeightValid && requestedWeight > 0 ? 'border-red-500' : ''}
                />
                {totalAvailableWeight > 0 && (
                  <div className="text-sm text-muted-foreground">
                    Available: {totalAvailableWeight.toFixed(3)}g
                    {requestedWeight > 0 && (
                      <span className={remainingWeight >= 0 ? 'text-green-600' : 'text-red-600'}>
                        {' '} | Remaining: {remainingWeight.toFixed(3)}g
                      </span>
                    )}
                  </div>
                )}
              </div>

              {/* Expected Loss Percentage */}
              <div className="space-y-2">
                <Label htmlFor="expected_loss_percentage">Expected Loss %</Label>
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  {...form.register('expected_loss_percentage', { valueAsNumber: true })}
                  placeholder="0.00"
                />
                {expectedLossPercentage > 0 && requestedWeight > 0 && (
                  <div className="text-sm text-muted-foreground">
                    Expected return: {expectedReturnWeight.toFixed(3)}g
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                {...form.register('notes')}
                placeholder="Enter any additional notes..."
                rows={3}
              />
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Available Metal Inventory */}
      {availableMetals.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Available Metal Inventory</CardTitle>
            <CardDescription>
              Current stock for selected customer, metal type, and karat
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {availableMetals.map((metal) => (
                <div key={metal.metal_inventory_id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">
                        {metal.metal_type?.metal_name || 'Unknown Metal'}
                      </Badge>
                      <Badge variant="outline">
                        {metal.karat?.karat_name || 'Unknown Karat'} ({metal.purity_percentage}%)
                      </Badge>
                      <Badge variant="outline">
                        {metal.current_location}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Weight: {metal.weight_grams.toFixed(3)}g | Status: {metal.status}
                    </div>
                  </div>
                </div>
              ))}
              
              <Separator />
              
              <div className="flex justify-between items-center font-medium">
                <span>Total Available:</span>
                <span>{totalAvailableWeight.toFixed(3)}g</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Issue Summary */}
      {requestedWeight > 0 && isWeightValid && (
        <Card>
          <CardHeader>
            <CardTitle>Issue Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Issue Weight</div>
                <div className="text-2xl font-bold">{requestedWeight.toFixed(3)}g</div>
              </div>
              
              {selectedKaratData && (
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Purity</div>
                  <div className="text-2xl font-bold">{selectedKaratData.purity_percentage}%</div>
                </div>
              )}
              
              {expectedLossPercentage > 0 && (
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Expected Return</div>
                  <div className="text-2xl font-bold">{expectedReturnWeight.toFixed(3)}g</div>
                </div>
              )}
            </div>

            {expectedLossPercentage > 0 && (
              <Alert className="mt-4">
                <InfoIcon className="h-4 w-4" />
                <AlertDescription>
                  Expected loss: {(requestedWeight * expectedLossPercentage / 100).toFixed(3)}g ({expectedLossPercentage}%)
                </AlertDescription>
              </Alert>
            )}
            
            <Separator className="my-4" />
            
            <div className="flex justify-end space-x-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  form.reset();
                  setSelectedCustomerId('');
                  setSelectedMetalType('');
                  setSelectedKarat('');
                  setSelectedProcess('');
                }}
              >
                Clear Form
              </Button>
              <Button 
                onClick={form.handleSubmit(onSubmit)}
                disabled={isSubmitting || !isWeightValid || requestedWeight === 0}
              >
                {isSubmitting ? 'Issuing...' : 'Issue Metal'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Validation Errors */}
      {requestedWeight > totalAvailableWeight && totalAvailableWeight > 0 && (
        <Alert variant="destructive">
          <AlertDescription>
            Requested weight ({requestedWeight.toFixed(3)}g) exceeds available inventory ({totalAvailableWeight.toFixed(3)}g)
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
