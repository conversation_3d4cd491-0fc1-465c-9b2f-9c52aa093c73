import { supabase } from '@/lib/supabase';
import { InventoryLocation, MetalTransaction, MetalBalance } from '@/types/inventory';

export default {
  async getLocations(): Promise<InventoryLocation[]> {
    const { data, error } = await supabase
      .from('inventory_locations')
      .select('*')
      .order('location_name', { ascending: true });

    if (error) throw new Error(error.message);
    return data as InventoryLocation[];
  },

  async createLocation(locationData: Omit<InventoryLocation, 'location_id'>): Promise<InventoryLocation> {
    const { data, error } = await supabase
      .from('inventory_locations')
      .insert(locationData)
      .select()
      .single();

    if (error) throw new Error(error.message);
    return data as InventoryLocation;
  },

  async createTransaction(transactionData: Omit<MetalTransaction, 'transaction_id'>): Promise<MetalTransaction> {
    const { data, error } = await supabase
      .from('metal_inventory_transactions')
      .insert(transactionData)
      .select()
      .single();

    if (error) throw new Error(error.message);
    return data as MetalTransaction;
  }
};
