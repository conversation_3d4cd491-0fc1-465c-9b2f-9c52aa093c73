/**
 * @service SettingProcessService
 * @description Handles Setting Process stone management with proper database synchronization
 * 
 * SCHEMA ALIGNMENT:
 * - Uses correct database column names
 * - Matches stone_transaction_details schema exactly
 * - Handles all three setting scenarios properly
 */

import { supabase } from '@/lib/db';
import { Database } from '@/types/db';

// Database types
type SettingProcessReceipt = Database['public']['Tables']['setting_process_receipts']['Row'];
type SettingProcessReceiptInsert = Database['public']['Tables']['setting_process_receipts']['Insert'];
type StoneDispositionDetail = Database['public']['Tables']['stone_disposition_details']['Row'];
type StoneTransactionDetail = Database['public']['Tables']['stone_transaction_details']['Row'];

// Service interfaces
export interface StoneDisposition {
  stoneId: string;
  stoneTypeId: string;
  stoneShapeId: string;
  stoneSizeId: string;
  description: string;
  quantityIssued: number;
  caratsIssued: number;
  quantitySet: number;
  quantityReturned: number;
  quantityBroken: number;
  quantityLost: number;
  caratsSet: number;
  caratsReturned: number;
  caratsBroken: number;
  caratsLost: number;
  notes?: string;
}

export interface SettingReceiptRequest {
  orderId: string;
  workerId: string;
  processId: string;
  transactionId: string;
  
  // Setting scenario
  scenario: 'all_set' | 'all_returned' | 'mixed';
  
  // Product weights
  grossWeightReceived: number;
  netWeightReceived: number;
  
  // Stone dispositions
  stoneDispositions: StoneDisposition[];
  
  // Metadata
  notes?: string;
  receivedBy: string;
}

export interface SettingReceiptResponse {
  receiptId: string;
  transactionId: string;
  summary: {
    totalStonesSet: number;
    totalStonesReturned: number;
    totalStonesBroken: number;
    totalStonesLost: number;
    totalCaratsSet: number;
    totalCaratsReturned: number;
    totalCaratsBroken: number;
    totalCaratsLost: number;
  };
}

/**
 * Process setting receipt with comprehensive stone tracking
 */
export async function processSettingReceipt(
  request: SettingReceiptRequest
): Promise<SettingReceiptResponse> {
  try {
    // 1. Validate request
    validateSettingReceiptRequest(request);
    
    // 2. Calculate summary totals
    const summary = calculateDispositionSummary(request.stoneDispositions);
    
    // 3. Create setting process receipt record
    const receiptData: SettingProcessReceiptInsert = {
      transaction_id: request.transactionId,
      order_id: request.orderId,
      worker_id: request.workerId,
      process_id: request.processId,
      scenario: request.scenario,
      gross_weight_received: request.grossWeightReceived,
      net_weight_received: request.netWeightReceived,
      total_stones_set: summary.totalStonesSet,
      total_stones_returned: summary.totalStonesReturned,
      total_stones_broken: summary.totalStonesBroken,
      total_stones_lost: summary.totalStonesLost,
      total_carats_set: summary.totalCaratsSet,
      total_carats_returned: summary.totalCaratsReturned,
      total_carats_broken: summary.totalCaratsBroken,
      total_carats_lost: summary.totalCaratsLost,
      notes: request.notes,
      created_by: request.receivedBy
    };

    const { data: receipt, error: receiptError } = await supabase
      .from('setting_process_receipts')
      .insert(receiptData)
      .select()
      .single();

    if (receiptError) throw receiptError;

    // 4. Update material transaction status
    const { error: transactionError } = await supabase
      .from('material_transactions')
      .update({
        status: 'completed',
        received_date: new Date().toISOString(),
        received_by: request.receivedBy,
        gross_weight_after: request.grossWeightReceived,
        net_weight_after: request.netWeightReceived,
        notes: request.notes
      })
      .eq('transaction_id', request.transactionId);

    if (transactionError) throw transactionError;

    // 5. Process each stone disposition
    for (const disposition of request.stoneDispositions) {
      await processStoneDisposition(receipt.receipt_id, disposition);
    }

    // 6. Update stone inventory based on dispositions
    await updateStoneInventoryFromDispositions(request.stoneDispositions);

    return {
      receiptId: receipt.receipt_id,
      transactionId: request.transactionId,
      summary
    };

  } catch (error) {
    console.error('Error processing setting receipt:', error);
    throw error;
  }
}

/**
 * Process individual stone disposition
 */
async function processStoneDisposition(
  receiptId: string,
  disposition: StoneDisposition
): Promise<void> {
  try {
    // 1. Find the corresponding stone transaction detail
    const { data: stoneTransactionDetail, error: findError } = await supabase
      .from('stone_transaction_details')
      .select('*')
      .eq('stone_inventory_id', disposition.stoneId)
      .single();

    if (findError) throw findError;

    // 2. Update stone transaction detail with disposition
    const { error: updateError } = await supabase
      .from('stone_transaction_details')
      .update({
        quantity_returned: disposition.quantityReturned,
        quantity_consumed: disposition.quantitySet, // "Set" stones are "consumed"
        quantity_damaged: disposition.quantityBroken,
        quantity_lost: disposition.quantityLost,
        carat_weight_returned: disposition.caratsReturned,
        carat_weight_consumed: disposition.caratsSet,
        carat_weight_damaged: disposition.caratsBroken,
        carat_weight_lost: disposition.caratsLost,
        disposition_notes: disposition.notes
      })
      .eq('detail_id', stoneTransactionDetail.detail_id);

    if (updateError) throw updateError;

    // 3. Create detailed disposition record
    const { error: dispositionError } = await supabase
      .from('stone_disposition_details')
      .insert({
        receipt_id: receiptId,
        stone_transaction_detail_id: stoneTransactionDetail.detail_id,
        stone_type_id: disposition.stoneTypeId,
        stone_shape_id: disposition.stoneShapeId,
        stone_size_id: disposition.stoneSizeId,
        description: disposition.description,
        quantity_issued: disposition.quantityIssued,
        carats_issued: disposition.caratsIssued,
        quantity_set: disposition.quantitySet,
        quantity_returned: disposition.quantityReturned,
        quantity_broken: disposition.quantityBroken,
        quantity_lost: disposition.quantityLost,
        carats_set: disposition.caratsSet,
        carats_returned: disposition.caratsReturned,
        carats_broken: disposition.caratsBroken,
        carats_lost: disposition.caratsLost,
        disposition_notes: disposition.notes
      });

    if (dispositionError) throw dispositionError;

  } catch (error) {
    console.error('Error processing stone disposition:', error);
    throw error;
  }
}

/**
 * Update stone inventory based on dispositions
 */
async function updateStoneInventoryFromDispositions(
  dispositions: StoneDisposition[]
): Promise<void> {
  try {
    for (const disposition of dispositions) {
      // Update stone inventory status based on what happened
      let newStatus = 'available';
      
      if (disposition.quantitySet > 0) {
        newStatus = 'consumed'; // Stones that were set are consumed
      } else if (disposition.quantityReturned > 0) {
        newStatus = 'available'; // Stones that were returned are available again
      } else if (disposition.quantityBroken > 0) {
        newStatus = 'damaged'; // Broken stones are damaged
      } else if (disposition.quantityLost > 0) {
        newStatus = 'lost'; // Lost stones are lost
      }

      // Update the stone inventory record
      const { error } = await supabase
        .from('stone_inventory')
        .update({
          status: newStatus,
          quantity: disposition.quantityReturned, // Only returned stones remain in inventory
          total_carat_weight: disposition.caratsReturned,
          updated_at: new Date().toISOString()
        })
        .eq('inventory_id', disposition.stoneId);

      if (error) throw error;

      // If stones were broken or lost, create separate inventory records for tracking
      if (disposition.quantityBroken > 0) {
        await createDamagedStoneRecord(disposition, 'damaged');
      }
      
      if (disposition.quantityLost > 0) {
        await createDamagedStoneRecord(disposition, 'lost');
      }
    }
  } catch (error) {
    console.error('Error updating stone inventory:', error);
    throw error;
  }
}

/**
 * Create record for damaged or lost stones
 */
async function createDamagedStoneRecord(
  disposition: StoneDisposition,
  status: 'damaged' | 'lost'
): Promise<void> {
  const quantity = status === 'damaged' ? disposition.quantityBroken : disposition.quantityLost;
  const carats = status === 'damaged' ? disposition.caratsBroken : disposition.caratsLost;
  
  if (quantity <= 0) return;

  const { error } = await supabase
    .from('stone_inventory')
    .insert({
      customer_id: '', // Will be populated from original stone record
      stone_type_id: disposition.stoneTypeId,
      stone_shape_id: disposition.stoneShapeId,
      stone_size_id: disposition.stoneSizeId,
      stone_quality_id: '', // Will be populated from original stone record
      quantity,
      total_carat_weight: carats,
      location: 'Floor', // Damaged/lost stones tracked on floor
      status,
      notes: `${status} during setting process: ${disposition.notes || ''}`
    });

  if (error) throw error;
}

/**
 * Calculate summary totals from dispositions
 */
function calculateDispositionSummary(dispositions: StoneDisposition[]) {
  return dispositions.reduce((summary, disposition) => ({
    totalStonesSet: summary.totalStonesSet + disposition.quantitySet,
    totalStonesReturned: summary.totalStonesReturned + disposition.quantityReturned,
    totalStonesBroken: summary.totalStonesBroken + disposition.quantityBroken,
    totalStonesLost: summary.totalStonesLost + disposition.quantityLost,
    totalCaratsSet: summary.totalCaratsSet + disposition.caratsSet,
    totalCaratsReturned: summary.totalCaratsReturned + disposition.caratsReturned,
    totalCaratsBroken: summary.totalCaratsBroken + disposition.caratsBroken,
    totalCaratsLost: summary.totalCaratsLost + disposition.caratsLost
  }), {
    totalStonesSet: 0,
    totalStonesReturned: 0,
    totalStonesBroken: 0,
    totalStonesLost: 0,
    totalCaratsSet: 0,
    totalCaratsReturned: 0,
    totalCaratsBroken: 0,
    totalCaratsLost: 0
  });
}

/**
 * Validate setting receipt request
 */
function validateSettingReceiptRequest(request: SettingReceiptRequest): void {
  if (!request.orderId) throw new Error('Order ID is required');
  if (!request.workerId) throw new Error('Worker ID is required');
  if (!request.processId) throw new Error('Process ID is required');
  if (!request.transactionId) throw new Error('Transaction ID is required');
  if (!request.scenario) throw new Error('Scenario is required');
  if (request.grossWeightReceived < 0) throw new Error('Gross weight cannot be negative');
  if (request.netWeightReceived < 0) throw new Error('Net weight cannot be negative');
  
  // Validate each stone disposition
  for (const disposition of request.stoneDispositions) {
    const total = disposition.quantitySet + disposition.quantityReturned + 
                  disposition.quantityBroken + disposition.quantityLost;
    
    if (total !== disposition.quantityIssued) {
      throw new Error(`Stone disposition total (${total}) doesn't match issued quantity (${disposition.quantityIssued}) for ${disposition.description}`);
    }
  }
}

/**
 * Get setting process receipts by order
 */
export async function getSettingReceiptsByOrder(orderId: string) {
  try {
    const { data, error } = await supabase
      .from('setting_process_summary')
      .select('*')
      .eq('order_id', orderId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching setting receipts:', error);
    throw error;
  }
}

/**
 * Get detailed stone disposition for a receipt
 */
export async function getStoneDispositionDetails(receiptId: string) {
  try {
    const { data, error } = await supabase
      .from('stone_disposition_details')
      .select(`
        *,
        stone_type:stone_type_mast(type_name),
        stone_shape:stone_shape_mast(shape_name),
        stone_size:stone_size_mast(size_name)
      `)
      .eq('receipt_id', receiptId)
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching stone disposition details:', error);
    throw error;
  }
}
