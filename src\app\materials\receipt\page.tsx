/**
 * Material Receipt Hub Page
 * Unified page for all material receipt operations with integrated order allocation
 * 
 * @module app/materials/receipt
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ClipboardCheckIcon, 
  GemIcon, 
  CircleIcon, 
  ScaleIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  UserIcon,
  PackageIcon
} from 'lucide-react';
import { StoneReceiptForm } from '@/components/material-receipt/StoneReceiptForm';
import { FindingReceiptForm } from '@/components/material-receipt/FindingReceiptForm';
import { MetalReceiptForm } from '@/components/material-receipt/MetalReceiptForm';

interface PendingReceipt {
  id: string;
  type: 'stone' | 'finding' | 'metal';
  description: string;
  worker: string;
  process: string;
  issued_date: string;
  expected_return: string;
  status: 'overdue' | 'due_today' | 'upcoming';
}

interface ReceiptStats {
  pending_receipts: number;
  overdue_receipts: number;
  completed_today: number;
  high_loss_alerts: number;
}

export default function MaterialReceiptPage() {
  const [activeTab, setActiveTab] = useState('stones');
  const [pendingReceipts] = useState<PendingReceipt[]>([
    {
      id: '1',
      type: 'stone',
      description: '50 pcs Diamond - Round - 2mm',
      worker: 'John Doe',
      process: 'Setting',
      issued_date: '2025-06-26',
      expected_return: '2025-06-28',
      status: 'due_today'
    },
    {
      id: '2',
      type: 'metal',
      description: '25.5g Gold - 18K',
      worker: 'Jane Smith',
      process: 'Filing',
      issued_date: '2025-06-25',
      expected_return: '2025-06-27',
      status: 'overdue'
    },
    {
      id: '3',
      type: 'finding',
      description: 'Polki Assembly - 15.2g',
      worker: 'Mike Johnson',
      process: 'Setting',
      issued_date: '2025-06-27',
      expected_return: '2025-06-29',
      status: 'upcoming'
    }
  ]);

  const [receiptStats] = useState<ReceiptStats>({
    pending_receipts: 15,
    overdue_receipts: 3,
    completed_today: 8,
    high_loss_alerts: 2
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'stone':
        return <GemIcon className="w-4 h-4" />;
      case 'finding':
        return <CircleIcon className="w-4 h-4" />;
      case 'metal':
        return <ScaleIcon className="w-4 h-4" />;
      default:
        return <PackageIcon className="w-4 h-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'overdue':
        return <Badge variant="destructive">Overdue</Badge>;
      case 'due_today':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Due Today</Badge>;
      case 'upcoming':
        return <Badge variant="secondary">Upcoming</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'stone':
        return 'bg-blue-100 text-blue-800';
      case 'finding':
        return 'bg-purple-100 text-purple-800';
      case 'metal':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Receive Materials</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Receive materials back from workers with integrated order allocation and location assignment
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Pending Receipts</p>
                <p className="text-2xl font-bold">{receiptStats.pending_receipts}</p>
              </div>
              <ClockIcon className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Overdue</p>
                <p className="text-2xl font-bold text-red-600">{receiptStats.overdue_receipts}</p>
              </div>
              <AlertTriangleIcon className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Completed Today</p>
                <p className="text-2xl font-bold text-green-600">{receiptStats.completed_today}</p>
              </div>
              <CheckCircleIcon className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Loss Alerts</p>
                <p className="text-2xl font-bold text-orange-600">{receiptStats.high_loss_alerts}</p>
              </div>
              <AlertTriangleIcon className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Main Receipt Forms */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ClipboardCheckIcon className="w-5 h-5" />
                Material Receipt Forms
              </CardTitle>
              <CardDescription>
                Receive materials with integrated order allocation and location assignment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="stones" className="flex items-center gap-2">
                    <GemIcon className="w-4 h-4" />
                    Stones
                  </TabsTrigger>
                  <TabsTrigger value="findings" className="flex items-center gap-2">
                    <CircleIcon className="w-4 h-4" />
                    Findings
                  </TabsTrigger>
                  <TabsTrigger value="metals" className="flex items-center gap-2">
                    <ScaleIcon className="w-4 h-4" />
                    Metals
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="stones" className="mt-6">
                  <StoneReceiptForm />
                </TabsContent>

                <TabsContent value="findings" className="mt-6">
                  <FindingReceiptForm />
                </TabsContent>

                <TabsContent value="metals" className="mt-6">
                  <MetalReceiptForm />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Pending Receipts */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <ClockIcon className="w-4 h-4" />
                Pending Receipts
              </CardTitle>
              <CardDescription>
                Materials waiting to be received
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {pendingReceipts.map((receipt) => (
                  <div key={receipt.id} className="p-3 rounded-lg border">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getTypeIcon(receipt.type)}
                        <Badge className={getTypeBadgeColor(receipt.type)}>
                          {receipt.type}
                        </Badge>
                      </div>
                      {getStatusBadge(receipt.status)}
                    </div>
                    <div className="text-sm font-medium mb-1">{receipt.description}</div>
                    <div className="flex items-center gap-2 text-xs text-gray-600 mb-2">
                      <UserIcon className="w-3 h-3" />
                      <span>{receipt.worker}</span>
                      <span>•</span>
                      <span>{receipt.process}</span>
                    </div>
                    <div className="text-xs text-gray-500">
                      Expected: {new Date(receipt.expected_return).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="outline" className="w-full mt-4">
                View All Pending
              </Button>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <ClipboardCheckIcon className="w-4 h-4 mr-2" />
                Bulk Receipt
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <AlertTriangleIcon className="w-4 h-4 mr-2" />
                Overdue Items
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <CheckCircleIcon className="w-4 h-4 mr-2" />
                Completed Today
              </Button>
            </CardContent>
          </Card>

          {/* Receipt Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Receipt Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                <div className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                  Order Allocation
                </div>
                <div className="text-xs text-blue-600 dark:text-blue-300">
                  You can allocate received materials to specific orders in the same screen
                </div>
              </div>
              
              <div className="p-3 rounded-lg bg-green-50 dark:bg-green-900/20">
                <div className="text-sm font-medium text-green-800 dark:text-green-200 mb-1">
                  Location Assignment
                </div>
                <div className="text-xs text-green-600 dark:text-green-300">
                  Assign materials to Safe, Central, or Floor locations during receipt
                </div>
              </div>
              
              <div className="p-3 rounded-lg bg-yellow-50 dark:bg-yellow-900/20">
                <div className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                  Loss Tracking
                </div>
                <div className="text-xs text-yellow-600 dark:text-yellow-300">
                  System automatically calculates loss percentages and flags unusual patterns
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
