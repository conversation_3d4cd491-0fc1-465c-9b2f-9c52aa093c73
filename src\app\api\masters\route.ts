/**
 * Master Data API Route Handler
 * Provides CRUD operations for all master data tables with role-based access control
 * @module api/masters
 */

import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

/**
 * List of valid master data tables
 * Used for type checking and validation
 */
const MASTER_TABLES = [
  'item_type_mast',
  'purity_mast',
  'metal_colour_mast',
  'order_category_mast',
  'process_mast',
  'styles_mast',
  // Legacy tables (for backward compatibility)
  'karat_mast',
  'gold_colour_mast'
] as const;

/** Type for master table names */
type MasterTable = typeof MASTER_TABLES[number];

/**
 * Response type for GET /api/masters
 */
type GetMasterResponse = any[] | { error: string };

/**
 * GET handler for master data
 * Retrieves master data with optional search functionality
 * 
 * @access Public - Accessible by admin, data_entry, and customer roles
 * @route GET /api/masters?table={tableName}&search={searchTerm}
 * 
 * @example
 * // Fetch all item types
 * GET /api/masters?table=item_type_mast
 * 
 * // Search for gold color masters
 * GET /api/masters?table=gold_colour_mast&search=yellow
 */
export const GET = withAuth(['admin', 'data_entry', 'customer'])(
  async (req: NextRequest): Promise<NextResponse<GetMasterResponse>> => {
    try {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const searchParams = new URL(req.url).searchParams;
      const table = searchParams.get('table') as MasterTable;
      const search = searchParams.get('search');

      if (!table || !MASTER_TABLES.includes(table)) {
        return NextResponse.json(
          { error: 'Invalid table name' },
          { status: 400 }
        );
      }

      let query = supabase.from(table).select('*');

      if (search) {
        query = query.ilike('description', `%${search}%`);
      }

      const { data, error } = await query;

      if (error) throw error;

      return NextResponse.json(data);
    } catch (error) {
      console.error('Error fetching master data:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);

/**
 * Response type for POST /api/masters
 */
type PostMasterResponse = any | { error: string };

/**
 * POST handler for master data
 * Creates a new master data entry
 * 
 * @access Restricted - Admin only
 * @route POST /api/masters?table={tableName}
 * 
 * @example
 * // Create a new item type
 * POST /api/masters?table=item_type_mast
 * Body: {
 *   "description": "Ring",
 *   "average_processing_time": 120,
 *   "suffix": "RG"
 * }
 */
export const POST = withAuth(['admin'])(
  async (req: NextRequest): Promise<NextResponse<PostMasterResponse>> => {
    try {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const searchParams = new URL(req.url).searchParams;
      const table = searchParams.get('table') as MasterTable;
      const body = await req.json();

      if (!table || !MASTER_TABLES.includes(table)) {
        return NextResponse.json(
          { error: 'Invalid table name' },
          { status: 400 }
        );
      }

      const { data, error } = await supabase
        .from(table)
        .insert(body)
        .select()
        .single();

      if (error) throw error;

      return NextResponse.json(data);
    } catch (error) {
      console.error('Error creating master data:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);

/**
 * Response type for PATCH /api/masters
 */
type PatchMasterResponse = any | { error: string };

/**
 * PATCH handler for master data
 * Updates an existing master data entry
 * 
 * @access Restricted - Admin only
 * @route PATCH /api/masters?table={tableName}
 * 
 * @example
 * // Update an item type
 * PATCH /api/masters?table=item_type_mast
 * Body: {
 *   "id": "123",
 *   "average_processing_time": 150
 * }
 */
export const PATCH = withAuth(['admin'])(
  async (req: NextRequest): Promise<NextResponse<PatchMasterResponse>> => {
    try {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const searchParams = new URL(req.url).searchParams;
      const table = searchParams.get('table') as MasterTable;
      const { id, ...updates } = await req.json();

      if (!table || !MASTER_TABLES.includes(table)) {
        return NextResponse.json(
          { error: 'Invalid table name' },
          { status: 400 }
        );
      }

      if (!id) {
        return NextResponse.json(
          { error: 'ID is required' },
          { status: 400 }
        );
      }

      const { data, error } = await supabase
        .from(table)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return NextResponse.json(data);
    } catch (error) {
      console.error('Error updating master data:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);
