import { Worker, WorkerSkill } from '@/types/domain/worker.types';
import { BaseRepository } from './BaseRepository';
import { NotFoundError, DatabaseError, ForeignKeyError } from '../errors/DatabaseError';

export class WorkerRepository extends BaseRepository<Worker, Worker> {
  // Define primary key name to override base repository
  protected primaryKey = 'worker_id';
  protected tableName = 'worker_mast';
  protected entityName = 'Worker';
  private readonly SKILLS_TABLE = 'worker_skills';

  async findAll(): Promise<Worker[]> {
    const { data, error } = await this.supabase
      .from(this.tableName)
      .select(`
        *,
        ${this.SKILLS_TABLE} (
          skill_id,
          process_id,
          skill_level,
          efficiency_factor,
          is_primary,
          process_mast (
            name,
            description,
            standard_time
          )
        )
      `);

    if (error) throw new Error(`Error fetching workers: ${error.message}`);
    return data;
  }

  async findById(id: string): Promise<Worker> {
    const { data, error } = await this.supabase
      .from(this.tableName)
      .select(`
        *,
        ${this.SKILLS_TABLE} (
          skill_id,
          process_id,
          skill_level,
          efficiency_factor,
          is_primary,
          process_mast (
            name,
            description,
            standard_time
          )
        )
      `)
      .eq('worker_id', id)
      .single();

    if (error) throw new Error(`Error fetching worker: ${error.message}`);
    if (!data) {
      throw new NotFoundError(this.entityName, id);
    }
    return data;
  }

  /**
   * Creates a new worker record
   * @param worker Worker data without the ID
   * @returns Created worker with ID
   */
  // Adjusted parameter type to match BaseRepository expectation (Omit<T, 'id'>)
  async create(worker: Omit<Worker, 'id'>): Promise<Worker> { 
    return this.executeQuery<Worker>(async () => { // Added async/await and return object
      const response = await this.supabase
        .from(this.tableName)
        .insert(worker as any) // Use type assertion if 'id' causes issues with insert type
        .select()
        .single();
      return { data: response.data, error: response.error };
    },
      'create'
    );
  }

  /**
   * Updates an existing worker record
   * @param id Worker ID to update
   * @param worker Partial worker data to update
   * @returns Updated worker record
   */
  async update(id: string, worker: Partial<Worker>): Promise<Worker> {
    // Note: BaseRepository update already checks existence via findById
    return this.executeQuery<Worker>(async () => { // Added async/await and return object
      const response = await this.supabase
        .from(this.tableName)
        .update(worker)
        .eq('worker_id', id) // Use primaryKey property if defined, or 'worker_id' directly
        .select()
        .single();
      return { data: response.data, error: response.error };
    },
      `update(${id})`
    );
  }

  /**
   * Deletes a worker record directly (unsafe - may cause foreign key constraint errors)
   * Use safeDelete for a safer alternative that handles all foreign key relationships
   * @param id Worker ID to delete
   */
  async delete(id: string): Promise<void> {
    // Note: BaseRepository delete already checks existence via findById
    await this.executeQuery<void>(async () => { // Added async/await and return object
      const response = await this.supabase
        .from(this.tableName)
        .delete()
        .eq('worker_id', id); // Use primaryKey property if defined, or 'worker_id' directly
      return { data: null, error: response.error };
    },
      `delete(${id})`
    );
  }

  /**
   * Safely deletes a worker and all related data
   * Handles foreign key constraints by first deleting related records
   * 
   * @param id - The worker ID to delete
   * @returns Promise indicating success or failure
   * @throws {ForeignKeyError} If there are active process tracking records
   * @throws {DatabaseError} If any database operation fails
   */
  async safeDelete(id: string): Promise<void> {
    try {
      // First get the worker to verify it exists
      const worker = await this.findById(id);
      if (!worker) {
        throw new NotFoundError(this.entityName, id);
      }
      
      // Check for active process tracking records
      const { data: activeProcesses, error: processError } = await this.supabase
        .from('process_tracking')
        .select('tracking_id')
        .eq('worker_id', id)
        .in('status', ['PENDING', 'SCHEDULED', 'IN_PROGRESS']);

      if (processError) {
        throw new DatabaseError(`Error checking process tracking: ${processError.message}`, 'DATABASE_ERROR');
      }

      if (activeProcesses && activeProcesses.length > 0) {
        throw new ForeignKeyError('Cannot delete worker with active processes assigned');
      }

      // 1. First, delete worker shifts which have a foreign key dependency
      console.log(`Deleting shifts for worker ${id}...`);
      const { error: shiftsError } = await this.supabase
        .from('worker_shifts')
        .delete()
        .eq('worker_id', id);

      if (shiftsError) {
        throw new DatabaseError(`Error deleting worker shifts: ${shiftsError.message}`, 'DATABASE_ERROR');
      }
      
      // 2. Delete worker skills
      console.log(`Deleting skills for worker ${id}...`);
      const { error: skillsError } = await this.supabase
        .from('worker_skills')
        .delete()
        .eq('worker_id', id);

      if (skillsError) {
        throw new DatabaseError(`Error deleting worker skills: ${skillsError.message}`, 'DATABASE_ERROR');
      }

      // 3. Update completed process tracking to null worker_id (maintain history)
      console.log(`Updating process tracking for worker ${id}...`);
      const { error: updateError } = await this.supabase
        .from('process_tracking')
        .update({ worker_id: null })
        .eq('worker_id', id);

      if (updateError) {
        throw new DatabaseError(`Error updating process tracking: ${updateError.message}`, 'DATABASE_ERROR');
      }
      
      // 4. Finally delete the worker
      console.log(`Deleting worker ${id}...`);
      const { error: workerError } = await this.supabase
        .from(this.tableName)
        .delete()
        .eq('worker_id', id);

      if (workerError) {
        throw new DatabaseError(`Error deleting worker: ${workerError.message}`, 'DATABASE_ERROR');
      }
      
      console.log(`Worker ${id} successfully deleted with all related records.`);
    } catch (error) {
      console.error(`Error in safeDelete for worker ${id}:`, error);
      throw error;
    }
  }

  async addSkill(workerId: string, skill: Omit<WorkerSkill, 'id'>): Promise<WorkerSkill> {
    const { data, error } = await this.supabase
      .from(this.SKILLS_TABLE)
      .insert({
        ...skill,
        worker_id: workerId,
      })
      .select()
      .single();

    if (error) throw new Error(`Error adding worker skill: ${error.message}`);
    return data;
  }

  async updateSkill(skillId: string, skill: Partial<WorkerSkill>): Promise<WorkerSkill> {
    const { data, error } = await this.supabase
      .from(this.SKILLS_TABLE)
      .update(skill)
      .eq('skill_id', skillId)
      .select()
      .single();

    if (error) throw new Error(`Error updating worker skill: ${error.message}`);
    return data;
  }

  async removeSkill(skillId: string): Promise<void> {
    const { error } = await this.supabase
      .from(this.SKILLS_TABLE)
      .delete()
      .eq('skill_id', skillId);

    if (error) throw new Error(`Error removing worker skill: ${error.message}`);
  }

  async getWorkerSkills(workerId: string): Promise<WorkerSkill[]> {
    const { data, error } = await this.supabase
      .from(this.SKILLS_TABLE)
      .select(`
        *,
        process_mast (
          name,
          description,
          standard_time
        )
      `)
      .eq('worker_id', workerId);

    if (error) throw new Error(`Error fetching worker skills: ${error.message}`);
    return data;
  }
}
