import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import { WorkerService } from '@/services/WorkerService';
import { ForeignKeyError } from '@/db/errors/DatabaseError';
import { Worker } from '@/types/worker';

// Define response types
type GetWorkersResponse = Worker[] | { error: string };
type PostWorkerResponse = Worker | { error: string };
type PatchWorkerResponse = Worker | { error: string };
type DeleteWorkerResponse = { success: boolean } | { error: string };

// GET /api/masters/workers - List workers
export const GET = withAuth(['admin', 'data_entry'])(
  async (req: NextRequest): Promise<NextResponse<GetWorkersResponse>> => {
    try {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

      const { data, error } = await supabase
        .from('worker_mast')
        .select('*')
        .order('name');

      if (error) throw error;

      return NextResponse.json(data);
    } catch (error) {
      console.error('Error fetching workers:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);

// POST /api/masters/workers - Create worker
export const POST = withAuth(['admin'])(
  async (req: NextRequest): Promise<NextResponse<PostWorkerResponse>> => {
    try {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const body = await req.json();

      const { data, error } = await supabase
        .from('worker_mast')
        .insert(body)
        .select()
        .single();

      if (error) throw error;

      return NextResponse.json(data);
    } catch (error) {
      console.error('Error creating worker:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);

// PATCH /api/masters/workers - Update worker
export const PATCH = withAuth(['admin'])(
  async (req: NextRequest): Promise<NextResponse<PatchWorkerResponse>> => {
    try {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const { id, ...updates } = await req.json();

      const { data, error } = await supabase
        .from('worker_mast')
        .update(updates)
        .eq('worker_id', id)
        .select()
        .single();

      if (error) throw error;

      return NextResponse.json(data);
    } catch (error) {
      console.error('Error updating worker:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);

// DELETE /api/masters/workers - Delete worker safely
export const DELETE = withAuth(['admin'])(
  async (req: NextRequest): Promise<NextResponse<DeleteWorkerResponse>> => {
    try {
      // Get worker ID from the URL
      const url = new URL(req.url);
      const workerId = url.searchParams.get('id');
      
      if (!workerId) {
        return NextResponse.json(
          { error: 'Worker ID is required' },
          { status: 400 }
        );
      }

      // Use our WorkerService to safely delete the worker
      const workerService = new WorkerService();
      await workerService.deleteWorker(workerId);
      
      return NextResponse.json({ success: true });
    } catch (error) {
      console.error('Error deleting worker:', error);
      
      let errorMessage = 'Failed to delete worker. Please ensure there are no related records.';
      let statusCode = 500;

      // Handle constraint violations with a proper error message
      if (error instanceof Error && error.name === 'AppError' && 
          (error as any).type === 'WORKER_CONSTRAINT_ERROR') {
        errorMessage = error.message || 'Cannot delete worker with active processes or assignments';
        statusCode = 400;
      }
      
      return NextResponse.json(
        { error: errorMessage },
        { status: statusCode }
      );
    }
  }
);
