import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/db';

// GET - Fetch all units of measure
export async function GET() {
  try {
    const { data, error } = await supabase
      .from('uom_mast')
      .select('*')
      .order('name');

    if (error) throw error;

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error fetching UOMs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch units of measure' },
      { status: 500 }
    );
  }
}

// POST - Create new unit of measure
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: 'name is required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('uom_mast')
      .insert([body])
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating UOM:', error);
    return NextResponse.json(
      { error: 'Failed to create unit of measure' },
      { status: 500 }
    );
  }
}

// PUT - Update unit of measure
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { uom_id, ...updateData } = body;
    
    if (!uom_id) {
      return NextResponse.json(
        { error: 'uom_id is required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('uom_mast')
      .update(updateData)
      .eq('uom_id', uom_id)
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error updating UOM:', error);
    return NextResponse.json(
      { error: 'Failed to update unit of measure' },
      { status: 500 }
    );
  }
}

// DELETE - Delete unit of measure
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const uom_id = searchParams.get('uom_id');

    if (!uom_id) {
      return NextResponse.json(
        { error: 'uom_id is required' },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from('uom_mast')
      .delete()
      .eq('uom_id', uom_id);

    if (error) throw error;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting UOM:', error);
    return NextResponse.json(
      { error: 'Failed to delete unit of measure' },
      { status: 500 }
    );
  }
}
