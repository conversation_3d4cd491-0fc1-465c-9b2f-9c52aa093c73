/**
 * @module components/material-receipt/FindingReceiptForm
 * @description Finding Receipt Form Component - Receive findings back from workers with weight tracking
 */

'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// Removed incorrect Select imports - using native select elements instead
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertTriangleIcon, CheckCircleIcon, InfoIcon } from 'lucide-react';
import { receiveFinding, getIssuedFindings } from '@/services/findingsInventoryService';

const findingReceiptSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  order_id: z.string().min(1, 'Order is required'),
  worker_id: z.string().min(1, 'Worker is required'),
  process_id: z.string().min(1, 'Process is required'),
  notes: z.string().optional()
});

interface FindingReceiptFormData {
  customer_id: string;
  order_id: string;
  worker_id: string;
  process_id: string;
  notes?: string;
}

interface IssuedFindingItem {
  finding_transaction_id: string;
  finding_id: string;
  finding_code: string;
  finding_name: string;
  gross_weight_grams_issued: number;
  net_weight_grams_issued: number;
  issue_date: string;
  stone_details?: Array<{
    stone_type_id: string;
    quantity: number;
    stone_type?: {
      type_name: string;
    };
  }>;
}

interface FindingReceiptItem extends IssuedFindingItem {
  gross_weight_grams_received: number;
  net_weight_grams_received: number;
  gross_weight_loss_grams: number;
  net_weight_loss_grams: number;
  condition_notes?: string;
  damage_description?: string;
}

export function FindingReceiptForm() {
  const [customers, setCustomers] = useState([]);
  const [orders, setOrders] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [processes, setProcesses] = useState([]);
  const [issuedFindings, setIssuedFindings] = useState<IssuedFindingItem[]>([]);
  const [receiptItems, setReceiptItems] = useState<FindingReceiptItem[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState('');
  const [selectedOrderId, setSelectedOrderId] = useState('');
  const [selectedWorkerId, setSelectedWorkerId] = useState('');
  const [selectedProcessId, setSelectedProcessId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FindingReceiptFormData>({
    resolver: zodResolver(findingReceiptSchema),
    defaultValues: {
      customer_id: '',
      order_id: '',
      worker_id: '',
      process_id: '',
      notes: ''
    }
  });

  // Load master data on component mount
  useEffect(() => {
    loadMasterData();
  }, []);

  // Load issued findings when all required fields are selected
  useEffect(() => {
    if (selectedCustomerId && selectedOrderId && selectedWorkerId && selectedProcessId) {
      loadIssuedFindings();
    }
  }, [selectedCustomerId, selectedOrderId, selectedWorkerId, selectedProcessId]);

  const loadMasterData = async () => {
    try {
      setIsLoading(true);
      
      // Load all master data in parallel
      const [customersRes, workersRes, processesRes] = await Promise.all([
        fetch('/api/masters/customers').then(res => res.json()),
        fetch('/api/masters/workers').then(res => res.json()),
        fetch('/api/masters/processes').then(res => res.json())
      ]);

      setCustomers(customersRes.data || []);
      setWorkers(workersRes.data || []);
      setProcesses(processesRes.data || []);

    } catch (error) {
      console.error('Error loading master data:', error);
      toast.error('Failed to load master data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCustomerOrders = async (customerId: string) => {
    try {
      const response = await fetch(`/api/orders?customer_id=${customerId}&status=active`);
      const data = await response.json();
      setOrders(data.data || []);
    } catch (error) {
      console.error('Error loading customer orders:', error);
      toast.error('Failed to load customer orders');
    }
  };

  const loadIssuedFindings = async () => {
    try {
      const findings = await getIssuedFindings({
        customer_id: selectedCustomerId,
        order_id: selectedOrderId,
        worker_id: selectedWorkerId,
        process_id: selectedProcessId
      });

      setIssuedFindings(findings);
      
      // Initialize receipt items with issued weights
      const initialReceiptItems: FindingReceiptItem[] = findings.map(finding => ({
        ...finding,
        gross_weight_grams_received: finding.gross_weight_grams_issued,
        net_weight_grams_received: finding.net_weight_grams_issued,
        gross_weight_loss_grams: 0,
        net_weight_loss_grams: 0,
        condition_notes: '',
        damage_description: ''
      }));
      
      setReceiptItems(initialReceiptItems);

    } catch (error) {
      console.error('Error loading issued findings:', error);
      toast.error('Failed to load issued findings');
    }
  };

  const handleCustomerChange = (customerId: string) => {
    setSelectedCustomerId(customerId);
    form.setValue('customer_id', customerId);
    resetDependentFields();
    loadCustomerOrders(customerId);
  };

  const handleOrderChange = (orderId: string) => {
    setSelectedOrderId(orderId);
    form.setValue('order_id', orderId);
    setIssuedFindings([]);
    setReceiptItems([]);
  };

  const handleWorkerChange = (workerId: string) => {
    setSelectedWorkerId(workerId);
    form.setValue('worker_id', workerId);
    setIssuedFindings([]);
    setReceiptItems([]);
  };

  const handleProcessChange = (processId: string) => {
    setSelectedProcessId(processId);
    form.setValue('process_id', processId);
    setIssuedFindings([]);
    setReceiptItems([]);
  };

  const resetDependentFields = () => {
    form.setValue('order_id', '');
    setSelectedOrderId('');
    setOrders([]);
    setIssuedFindings([]);
    setReceiptItems([]);
  };

  const updateReceiptItem = (index: number, field: keyof FindingReceiptItem, value: any) => {
    setReceiptItems(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };
      
      // Calculate losses when weights change
      if (field === 'gross_weight_grams_received') {
        updated[index].gross_weight_loss_grams = Math.max(0, updated[index].gross_weight_grams_issued - value);
      }
      if (field === 'net_weight_grams_received') {
        updated[index].net_weight_loss_grams = Math.max(0, updated[index].net_weight_grams_issued - value);
      }
      
      return updated;
    });
  };

  const onSubmit = async (data: FindingReceiptFormData) => {
    if (receiptItems.length === 0) {
      toast.error('No findings to receive');
      return;
    }

    // Validate receipt weights
    const invalidItems = receiptItems.filter(item => 
      item.gross_weight_grams_received < 0 || 
      item.net_weight_grams_received < 0 ||
      item.gross_weight_grams_received > item.gross_weight_grams_issued ||
      item.net_weight_grams_received > item.net_weight_grams_issued
    );

    if (invalidItems.length > 0) {
      toast.error('Invalid receipt weights detected. Please check your entries.');
      return;
    }

    try {
      setIsSubmitting(true);

      // Process each receipt item
      for (const item of receiptItems) {
        await receiveFinding({
          transaction_id: item.finding_transaction_id,
          finding_id: item.finding_id,
          received_gross_weight_grams: item.gross_weight_grams_received,
          received_net_weight_grams: item.net_weight_grams_received,
          condition: item.gross_weight_loss_grams > 0 ? 'damaged' : 'good',
          stone_losses: [], // TODO: Handle stone losses if needed
          received_by: 'current_user', // TODO: Get from auth context
          notes: data.notes
        });
      }

      toast.success('Findings received successfully');
      
      // Reset form
      form.reset();
      setSelectedCustomerId('');
      setSelectedOrderId('');
      setSelectedWorkerId('');
      setSelectedProcessId('');
      setIssuedFindings([]);
      setReceiptItems([]);
      
    } catch (error) {
      console.error('Error receiving findings:', error);
      toast.error('Failed to receive findings');
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalIssuedGrossWeight = receiptItems.reduce((sum, item) => sum + item.gross_weight_grams_issued, 0);
  const totalReceivedGrossWeight = receiptItems.reduce((sum, item) => sum + item.gross_weight_grams_received, 0);
  const totalGrossWeightLoss = receiptItems.reduce((sum, item) => sum + item.gross_weight_loss_grams, 0);
  const totalIssuedNetWeight = receiptItems.reduce((sum, item) => sum + item.net_weight_grams_issued, 0);
  const totalReceivedNetWeight = receiptItems.reduce((sum, item) => sum + item.net_weight_grams_received, 0);
  const totalNetWeightLoss = receiptItems.reduce((sum, item) => sum + item.net_weight_loss_grams, 0);

  const hasWeightLoss = totalGrossWeightLoss > 0 || totalNetWeightLoss > 0;
  const hasDamageReports = receiptItems.some(item => item.damage_description && item.damage_description.trim() !== '');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Receive Findings from Worker</CardTitle>
          <CardDescription>
            Receive findings back from worker after processing with weight and condition tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Customer Selection */}
              <div className="space-y-2">
                <Label htmlFor="customer_id">Customer *</Label>
                <select
                  id="customer_id"
                  value={selectedCustomerId}
                  onChange={(e) => handleCustomerChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select customer</option>
                  {customers.map((customer: any) => (
                    <option key={customer.customer_id} value={customer.customer_id}>
                      {customer.customer_name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Order Selection */}
              <div className="space-y-2">
                <Label htmlFor="order_id">Order *</Label>
                <select
                  id="order_id"
                  disabled={!selectedCustomerId}
                  value={selectedOrderId}
                  onChange={(e) => handleOrderChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 disabled:opacity-50"
                >
                  <option value="">Select order</option>
                  {orders.map((order: any) => (
                    <option key={order.order_id} value={order.order_id}>
                      {order.order_no} - {order.style_code}
                    </option>
                  ))}
                </select>
              </div>

              {/* Worker Selection */}
              <div className="space-y-2">
                <Label htmlFor="worker_id">Worker *</Label>
                <select
                  id="worker_id"
                  value={selectedWorkerId}
                  onChange={(e) => handleWorkerChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select worker</option>
                  {workers.map((worker: any) => (
                    <option key={worker.worker_id} value={worker.worker_id}>
                      {worker.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Process Selection */}
              <div className="space-y-2">
                <Label htmlFor="process_id">Process *</Label>
                <select
                  id="process_id"
                  value={selectedProcessId}
                  onChange={(e) => handleProcessChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select process</option>
                  {processes.map((process: any) => (
                    <option key={process.process_id} value={process.process_id}>
                      {process.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                {...form.register('notes')}
                placeholder="Enter any additional notes..."
                rows={3}
              />
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Issued Findings for Receipt */}
      {receiptItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Finding Receipt Details</CardTitle>
            <CardDescription>
              Update the received weights and note any condition changes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Finding Details</TableHead>
                    <TableHead>Embedded Stones</TableHead>
                    <TableHead>Issued Gross (g)</TableHead>
                    <TableHead>Received Gross (g)</TableHead>
                    <TableHead>Gross Loss (g)</TableHead>
                    <TableHead>Issued Net (g)</TableHead>
                    <TableHead>Received Net (g)</TableHead>
                    <TableHead>Net Loss (g)</TableHead>
                    <TableHead>Condition Notes</TableHead>
                    <TableHead>Damage Description</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {receiptItems.map((item, index) => (
                    <TableRow key={item.finding_transaction_id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{item.finding_code}</div>
                          <div className="text-sm text-muted-foreground">{item.finding_name}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {item.stone_details && item.stone_details.length > 0 ? (
                            item.stone_details.map((stone, stoneIndex) => (
                              <Badge key={stoneIndex} variant="secondary" className="text-xs">
                                {stone.stone_type?.type_name || 'Unknown'}: {stone.quantity}
                              </Badge>
                            ))
                          ) : (
                            <span className="text-muted-foreground text-sm">No stones</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {item.gross_weight_grams_issued.toFixed(3)}
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          step="0.001"
                          min="0"
                          max={item.gross_weight_grams_issued}
                          value={item.gross_weight_grams_received}
                          onChange={(e) => updateReceiptItem(index, 'gross_weight_grams_received', parseFloat(e.target.value) || 0)}
                          className="w-24"
                        />
                      </TableCell>
                      <TableCell>
                        <Badge variant={item.gross_weight_loss_grams > 0 ? "destructive" : "secondary"}>
                          {item.gross_weight_loss_grams.toFixed(3)}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">
                        {item.net_weight_grams_issued.toFixed(3)}
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          step="0.001"
                          min="0"
                          max={item.net_weight_grams_issued}
                          value={item.net_weight_grams_received}
                          onChange={(e) => updateReceiptItem(index, 'net_weight_grams_received', parseFloat(e.target.value) || 0)}
                          className="w-24"
                        />
                      </TableCell>
                      <TableCell>
                        <Badge variant={item.net_weight_loss_grams > 0 ? "destructive" : "secondary"}>
                          {item.net_weight_loss_grams.toFixed(3)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Textarea
                          placeholder="Condition notes"
                          value={item.condition_notes || ''}
                          onChange={(e) => updateReceiptItem(index, 'condition_notes', e.target.value)}
                          className="w-32 min-h-[60px]"
                          rows={2}
                        />
                      </TableCell>
                      <TableCell>
                        <Textarea
                          placeholder="Damage description"
                          value={item.damage_description || ''}
                          onChange={(e) => updateReceiptItem(index, 'damage_description', e.target.value)}
                          className="w-32 min-h-[60px]"
                          rows={2}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Receipt Summary */}
      {receiptItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Receipt Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Total Findings</div>
                <div className="text-2xl font-bold">{receiptItems.length}</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Issued Gross Weight</div>
                <div className="text-2xl font-bold">{totalIssuedGrossWeight.toFixed(3)}g</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Received Gross Weight</div>
                <div className="text-2xl font-bold text-green-600">{totalReceivedGrossWeight.toFixed(3)}g</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Gross Weight Loss</div>
                <div className={`text-2xl font-bold ${hasWeightLoss ? 'text-red-600' : 'text-green-600'}`}>
                  {totalGrossWeightLoss.toFixed(3)}g
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Issued Net Weight</div>
                <div className="text-2xl font-bold">{totalIssuedNetWeight.toFixed(3)}g</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Received Net Weight</div>
                <div className="text-2xl font-bold text-green-600">{totalReceivedNetWeight.toFixed(3)}g</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Net Weight Loss</div>
                <div className={`text-2xl font-bold ${hasWeightLoss ? 'text-red-600' : 'text-green-600'}`}>
                  {totalNetWeightLoss.toFixed(3)}g
                </div>
              </div>
            </div>

            {/* Alerts */}
            <div className="space-y-2 mt-4">
              {hasWeightLoss && (
                <Alert variant="destructive">
                  <AlertTriangleIcon className="h-4 w-4" />
                  <AlertDescription>
                    Weight loss detected: Gross: {totalGrossWeightLoss.toFixed(3)}g, Net: {totalNetWeightLoss.toFixed(3)}g
                  </AlertDescription>
                </Alert>
              )}

              {hasDamageReports && (
                <Alert variant="destructive">
                  <AlertTriangleIcon className="h-4 w-4" />
                  <AlertDescription>
                    Damage reported on one or more findings. Please review damage descriptions.
                  </AlertDescription>
                </Alert>
              )}

              {!hasWeightLoss && !hasDamageReports && receiptItems.length > 0 && (
                <Alert>
                  <CheckCircleIcon className="h-4 w-4" />
                  <AlertDescription>
                    All findings received in good condition with no weight loss.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Weight Loss Explanation */}
            {hasWeightLoss && (
              <Alert className="mt-4">
                <InfoIcon className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <div className="font-medium">Weight Loss Notes:</div>
                    <div className="text-sm">
                      • Gross weight includes embedded stones and metal
                    </div>
                    <div className="text-sm">
                      • Net weight is the metal weight excluding stones
                    </div>
                    <div className="text-sm">
                      • Weight loss may indicate stone loss, metal filing, or damage
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}
            
            <Separator className="my-4" />
            
            <div className="flex justify-end space-x-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  form.reset();
                  setSelectedCustomerId('');
                  setSelectedOrderId('');
                  setSelectedWorkerId('');
                  setSelectedProcessId('');
                  setReceiptItems([]);
                }}
              >
                Clear Form
              </Button>
              <Button 
                onClick={form.handleSubmit(onSubmit)}
                disabled={isSubmitting || receiptItems.length === 0}
              >
                {isSubmitting ? 'Processing...' : 'Receive Findings'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Issued Findings Message */}
      {selectedCustomerId && selectedOrderId && selectedWorkerId && selectedProcessId && 
       issuedFindings.length === 0 && !isLoading && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                No findings found issued to this worker for the selected order and process.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
