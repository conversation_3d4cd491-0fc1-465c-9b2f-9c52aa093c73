import {
  LayoutDashboard,
  ClipboardList,
  Settings,
  Users,
  Box,
  Gem,
  Palette,
  Tag,
  Brush,
  Building,
  Users2,
} from 'lucide-react';

export const navigation = {
  masters: {
    title: "Masters",
    items: [
      {
        title: "Customers",
        href: "/masters/customers"
      },
      {
        title: "Third Party Customers",
        href: "/masters/third-party-customers"
      },
      {
        title: "Processes",
        href: "/masters/processes"
      },
      {
        title: "Item Types",
        href: "/masters/item-types"
      },
      {
        title: "Karats",
        href: "/masters/karats"
      },
      {
        title: "Metal Colors",
        href: "/masters/metal-colors"
      },
      {
        title: "Order Categories",
        href: "/masters/order-categories"
      },
      {
        title: "Styles",
        href: "/masters/styles"
      },
    ]
  },
  dashboard: {
    title: "Dashboard",
    items: [
      {
        title: "Dashboard",
        href: "/"
      },
      {
        title: "Orders",
        href: "/orders"
      }
    ]
  },
  processManagement: {
    title: "Process Management",
    items: [
      {
        title: "Active Processes",
        href: "/processes/active"
      },
      {
        title: "Process History",
        href: "/processes/history"
      },
      {
        title: "Worker Assignments",
        href: "/processes/assignments"
      }
    ]
  },
  userManagement: {
    title: "User Management",
    items: [
      {
        title: "Workers",
        href: "/workers"
      },
    ]
  },
};
