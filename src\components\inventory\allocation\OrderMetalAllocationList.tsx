/**
 * OrderMetalAllocationList Component
 * Displays and manages order metal allocations
 * 
 * @module components/inventory/allocation
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, FileEdit, Trash2, Eye } from 'lucide-react';
import { OrderMetalAllocation } from '@/types/inventory';
import { useToast } from '@/hooks/useToast';
import { formatDate } from '@/lib/utils';

/**
 * Props for OrderMetalAllocationList component
 */
interface OrderMetalAllocationListProps {
  orderId?: string;
}

/**
 * OrderMetalAllocationList Component
 * Lists and manages metal allocations for orders
 */
export const OrderMetalAllocationList: React.FC<OrderMetalAllocationListProps> = ({ orderId }) => {
  const [allocations, setAllocations] = useState<OrderMetalAllocation[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { showToast } = useToast();

  useEffect(() => {
    // Set a timeout to prevent infinite loading state
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
        setError('Loading timed out. Please check your network connection or try again later.');
      }
    }, 10000); // 10 second timeout
    
    fetchAllocations();
    
    return () => clearTimeout(loadingTimeout);
  }, [orderId]);

  /**
   * Fetches metal allocations from the API
   */
  const fetchAllocations = async () => {
    try {
      setLoading(true);
      const url = orderId
        ? `/api/inventory/order-allocations?orderId=${orderId}`
        : '/api/inventory/order-allocations';
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch metal allocations');
      }
      
      const data = await response.json();
      setAllocations(data);
      setError(null);
    } catch (err) {
      setError('Error loading metal allocations. Please try again later.');
      console.error('Error fetching metal allocations:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handles navigation to create a new metal allocation
   */
  const handleCreateNew = () => {
    if (orderId) {
      router.push(`/orders/${orderId}/materials/allocate`);
    } else {
      router.push('/inventory/order-allocations/create');
    }
  };

  /**
   * Handles navigation to edit a metal allocation
   * @param id - The allocation ID to edit
   */
  const handleEdit = (id: string) => {
    router.push(`/inventory/order-allocations/${id}/edit`);
  };

  /**
   * Handles deletion of a metal allocation
   * @param id - The allocation ID to delete
   */
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this metal allocation? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/inventory/order-allocations?id=${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete metal allocation');
      }
      
      await fetchAllocations();
      showToast({
        title: 'Success',
        description: 'Metal allocation deleted successfully',
        type: 'success',
      });
    } catch (err) {
      console.error('Error deleting metal allocation:', err);
      showToast({
        title: 'Error',
        description: 'Error deleting metal allocation',
        type: 'destructive',
      });
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">
          {orderId ? 'Metal Allocations for this Order' : 'All Order Metal Allocations'}
        </h2>
        <button
          onClick={handleCreateNew}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Plus className="w-5 h-5 mr-2" />
          Allocate Metal
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
          <p>{error}</p>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-48">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                {!orderId && (
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Order
                  </th>
                )}
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Metal Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Purity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Weight
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Allocation Date
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {allocations.length === 0 ? (
                <tr>
                  <td colSpan={orderId ? 5 : 6} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    No metal allocations found
                  </td>
                </tr>
              ) : (
                allocations.map((allocation) => (
                  <tr key={allocation.allocation_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    {!orderId && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                        {allocation.order_id ? allocation.order_id.substring(0, 8) + '...' : '-'}
                      </td>
                    )}
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {allocation.metal_pool?.metal_type?.name || 'Unknown'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {allocation.metal_pool?.karat?.purity ? `${allocation.metal_pool.karat.purity}%` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {allocation.allocated_weight} {allocation.uom?.code || ''}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(allocation.allocation_date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleEdit(allocation.allocation_id)}
                        className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-4"
                        title="Edit Allocation"
                      >
                        <FileEdit className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(allocation.allocation_id)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        title="Delete Allocation"
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};
