'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { usePathname } from 'next/navigation';
import {
    LayoutDashboard,
    ClipboardList,
    Settings,
    Users,
    Box,
    Gem,
    Palette,
    ListChecks,
    Calendar,
    Factory,
    GraduationCap,
    UserCog,
    Building2,
    Timer,
    ClipboardCheck,
    LineChart,
    User,
    Shapes,
    CircleDollarSign,
    BookOpen,
    ScrollText,
    Plus,
    ChevronRight,
    ChevronDown,
    Database,
    Circle,
    Scissors,
    Scale,
    BarChart,
    PieChart,
    Layers,
    Workflow
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavItem {
    label: string;
    href: string;
    icon: React.ReactNode;
    subItems?: NavItem[];
}

const navigation: NavItem[] = [
    {
        label: 'Dashboard',
        href: '/',
        icon: <LayoutDashboard className="w-5 h-5" />,
    },
    {
        label: 'Orders',
        href: '/orders',
        icon: <ClipboardList className="w-5 h-5" />,
        subItems: [
            {
                label: 'All Orders',
                href: '/orders',
                icon: <ScrollText className="w-4 h-4" />,
            },
            {
                label: 'Create Order',
                href: '/orders/create',
                icon: <Plus className="w-4 h-4" />,
            }
        ]
    },
    {
        label: 'Process Management',
        href: '/processes',
        icon: <Settings className="w-5 h-5" />,
        subItems: [
            {
                label: 'Active Processes',
                href: '/processes/active',
                icon: <Timer className="w-4 h-4" />,
            },
            {
                label: 'Process Batches',
                href: '/process/batches',
                icon: <Layers className="w-4 h-4" />,
            },
            {
                label: 'Process Flow',
                href: '/process/flow',
                icon: <Workflow className="w-4 h-4" />,
            },
            {
                label: 'Process History',
                href: '/processes/history',
                icon: <ClipboardList className="w-4 h-4" />,
            },
            {
                label: 'Process Assignments',
                href: '/processes/assignments',
                icon: <Users className="w-4 h-4" />,
            },
            {
                label: 'Scheduling',
                href: '/processes/scheduling',
                icon: <Calendar className="w-4 h-4" />,
            },
            {
                label: 'Process Analytics',
                href: '/processes/analytics',
                icon: <LineChart className="w-4 h-4" />,
            }
        ]
    },
    {
        label: 'Materials',
        href: '/materials',
        icon: <Gem className="w-5 h-5" />,
        subItems: [
            {
                label: 'Issue Materials',
                href: '/materials/issue',
                icon: <Plus className="w-4 h-4" />,
            },
            {
                label: 'Receive Materials',
                href: '/materials/receipt',
                icon: <ClipboardCheck className="w-4 h-4" />,
            },
            {
                label: 'Material Status',
                href: '/materials/status',
                icon: <ListChecks className="w-4 h-4" />,
            },
            {
                label: 'Dust Management',
                href: '/materials/dust',
                icon: <Circle className="w-4 h-4" />,
            },
            {
                label: 'Location Transfers',
                href: '/materials/transfers',
                icon: <Workflow className="w-4 h-4" />,
            }
        ]
    },
    {
        label: 'Inventory',
        href: '/inventory',
        icon: <Box className="w-5 h-5" />,
        subItems: [
            {
                label: 'Customer Receipts',
                href: '/inventory/material-receipt',
                icon: <ClipboardCheck className="w-4 h-4" />,
            },
            {
                label: 'Stock Overview',
                href: '/inventory/stock',
                icon: <Database className="w-4 h-4" />,
            },
            {
                label: 'Metal Pools',
                href: '/inventory/metal-pools',
                icon: <CircleDollarSign className="w-4 h-4" />,
            },
            {
                label: 'Location Inventory',
                href: '/inventory/locations',
                icon: <Building2 className="w-4 h-4" />,
            }
        ]
    },
    {
        label: 'Reports',
        href: '/reports',
        icon: <BarChart className="w-5 h-5" />,
        subItems: [
            {
                label: 'Loss Analysis',
                href: '/reports/loss',
                icon: <PieChart className="w-4 h-4" />,
            },
            {
                label: 'Process Performance',
                href: '/reports/process-performance',
                icon: <LineChart className="w-4 h-4" />,
            }
        ]
    },
    {
        label: 'Masters',
        href: '/masters',
        icon: <Settings className="w-5 h-5" />,
        subItems: [
            {
                label: 'Customer Management',
                href: '/masters/customers',
                icon: <User className="w-4 h-4" />,
            },
            {
                label: 'Third Party Customers',
                href: '/masters/third-party-customers',
                icon: <Users className="w-4 h-4" />,
            },
            {
                label: 'Item Types',
                href: '/masters/item-types',
                icon: <Shapes className="w-4 h-4" />,
            },
            {
                label: 'Order Categories',
                href: '/masters/order-categories',
                icon: <BookOpen className="w-4 h-4" />,
            },
            {
                label: 'Metal Colors',
                href: '/masters/metal-colors',
                icon: <Palette className="w-4 h-4" />,
            },
            {
                label: 'Karats',
                href: '/masters/karats',
                icon: <CircleDollarSign className="w-4 h-4" />,
            },
            {
                label: 'Process Master',
                href: '/masters/processes',
                icon: <Settings className="w-4 h-4" />,
            },
            {
                label: 'Worker Management',
                href: '/masters/workers',
                icon: <UserCog className="w-4 h-4" />,
            },
            {
                label: 'Worker Skills',
                href: '/masters/workers/skills',
                icon: <GraduationCap className="w-4 h-4" />,
            },
            {
                label: 'Workshop Config',
                href: '/masters/workshop/config',
                icon: <Factory className="w-4 h-4" />,
            },
            {
                label: 'Holidays',
                href: '/masters/holidays',
                icon: <Calendar className="w-4 h-4" />,
            },
            {
                label: 'Styles',
                href: '/masters/styles',
                icon: <Gem className="w-4 h-4" />,
            },
            // Added Inventory Masters below
            {
                label: 'Metal Types',
                href: '/inventory/masters?view=metal-types',
                icon: <Database className="w-4 h-4" />,
            },
            {
                label: 'Stone Types',
                href: '/inventory/masters?view=stone-types',
                icon: <Circle className="w-4 h-4" />,
            },
            {
                label: 'Stone Shapes',
                href: '/inventory/masters?view=stone-shapes',
                icon: <Box className="w-4 h-4" />,
            },
            {
                label: 'Diamond Cuts',
                href: '/inventory/masters?view=diamond-cuts',
                icon: <Scissors className="w-4 h-4" />,
            },
            {
                label: 'Polki Sizes',
                href: '/inventory/masters?view=polki-sizes',
                icon: <Circle className="w-4 h-4" />, // Reusing Circle icon
            },
            {
                label: 'Units of Measure',
                href: '/inventory/masters?view=uoms',
                icon: <Scale className="w-4 h-4" />,
            }
        ]
    }
];

interface SidebarProps {
    isOpen: boolean;
    onClose: () => void;
}

export function Sidebar({ isOpen, onClose }: SidebarProps) {
    const pathname = usePathname();
    const [expandedItems, setExpandedItems] = useState<string[]>([]);

    const isActive = (href: string) => {
        if (href === '/') {
            return pathname === href;
        }
        return pathname.startsWith(href);
    };

    const toggleExpanded = (href: string) => {
        setExpandedItems(prev => 
            prev.includes(href) 
                ? prev.filter(item => item !== href)
                : [...prev, href]
        );
    };

    const isExpanded = (href: string) => {
        return expandedItems.includes(href) || isActive(href);
    };

    const renderNavItems = (items: NavItem[], level = 0) => {
        return items.map((item) => {
            const hasSubItems = item.subItems && item.subItems.length > 0;
            const isItemExpanded = isExpanded(item.href);
            const active = isActive(item.href);

            return (
                <div key={item.href} className={`${level > 0 ? 'ml-4' : ''}`}>
                    <div
                        className={cn(
                            'group flex items-center justify-between rounded-md px-3 py-2.5 text-sm font-medium transition-all',
                            {
                                'bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-foreground': active,
                                'text-gray-700 hover:bg-gray-100/50 dark:text-gray-300 dark:hover:bg-gray-800/50': !active,
                                'cursor-pointer hover:bg-gray-100/50 dark:hover:bg-gray-800/50': hasSubItems
                            }
                        )}
                        onClick={() => {
                            if (hasSubItems) {
                                toggleExpanded(item.href);
                            } else if (level === 0) {
                                onClose();
                            }
                        }}
                    >
                        <Link
                            href={item.href}
                            className={cn(
                                'flex items-center gap-3 flex-1',
                                { 'pointer-events-none': hasSubItems }
                            )}
                            onClick={(e) => {
                                if (hasSubItems) {
                                    e.preventDefault();
                                } else if (level === 0) {
                                    onClose();
                                }
                            }}
                        >
                            <div className={cn(
                                'flex items-center justify-center w-8 h-8 rounded-md transition-colors',
                                {
                                    'text-primary bg-primary/10 dark:bg-primary/20': active,
                                    'text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300': !active
                                }
                            )}>
                                {item.icon}
                            </div>
                            <span className={cn(
                                'font-medium transition-colors',
                                {
                                    'text-primary dark:text-primary-foreground': active,
                                    'text-gray-700 group-hover:text-gray-900 dark:text-gray-300 dark:group-hover:text-gray-100': !active
                                }
                            )}>{item.label}</span>
                        </Link>
                        {hasSubItems && (
                            <div className={cn(
                                'flex items-center justify-center w-6 h-6 rounded-md transition-all duration-200',
                                {
                                    'rotate-90 text-primary dark:text-primary-foreground': isItemExpanded,
                                    'text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-400': !isItemExpanded
                                }
                            )}>
                                <ChevronRight className="h-4 w-4" />
                            </div>
                        )}
                    </div>
                    {hasSubItems && (
                        <div
                            className={cn(
                                'overflow-hidden transition-all duration-200 ease-in-out pl-4',
                                {
                                    'max-h-[1000px] opacity-100 mt-1': isItemExpanded,
                                    'max-h-0 opacity-0': !isItemExpanded
                                }
                            )}
                        >
                            <div className="relative before:absolute before:left-4 before:top-0 before:h-full before:w-px before:bg-gray-200 dark:before:bg-gray-700">
                                {item.subItems && renderNavItems(item.subItems, level + 1)}
                            </div>
                        </div>
                    )}
                </div>
            );
        });
    };

    return (
        <>
            {/* Desktop Sidebar */}
            <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
                <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white dark:bg-gray-900 dark:border-gray-800 px-6 pb-4">
                    <div className="flex h-16 shrink-0 items-center">
                        <Link href="/" className="flex items-center gap-3 px-2">
                            <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-primary/10 dark:bg-primary/20">
                                <Building2 className="h-5 w-5 text-primary dark:text-primary-foreground" />
                            </div>
                            <span className="font-semibold text-lg text-gray-900 dark:text-white">JWL Process</span>
                        </Link>
                    </div>
                    <nav className="flex flex-1 flex-col gap-1">
                        <ul role="list" className="flex flex-1 flex-col gap-1">
                            {renderNavItems(navigation)}
                        </ul>
                    </nav>
                </div>
            </div>

            {/* Mobile Sidebar */}
            <div
                className={`lg:hidden ${
                    isOpen ? 'fixed inset-0 z-50 bg-gray-900/80 backdrop-blur-sm' : 'hidden'
                }`}
                onClick={onClose}
            >
                <div 
                    className={`fixed inset-y-0 left-0 z-50 w-full overflow-y-auto bg-white dark:bg-gray-900 px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10 transform ${
                        isOpen ? 'translate-x-0' : '-translate-x-full'
                    } transition-transform duration-300 ease-in-out`}
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex items-center justify-between">
                        <Link
                            href="/"
                            className="flex items-center gap-3 px-2"
                            onClick={onClose}
                        >
                            <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-primary/10 dark:bg-primary/20">
                                <Building2 className="h-5 w-5 text-primary dark:text-primary-foreground" />
                            </div>
                            <span className="font-semibold text-lg text-gray-900 dark:text-white">JWL Process</span>
                        </Link>
                        <button
                            type="button"
                            className="flex items-center justify-center w-8 h-8 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100/50 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-800/50"
                            onClick={onClose}
                        >
                            <span className="sr-only">Close menu</span>
                            <svg
                                className="h-5 w-5"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth="2"
                                stroke="currentColor"
                                aria-hidden="true"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M6 18L18 6M6 6l12 12"
                                />
                            </svg>
                        </button>
                    </div>
                    <div className="mt-6 flow-root">
                        <div className="space-y-1">
                            {renderNavItems(navigation)}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
