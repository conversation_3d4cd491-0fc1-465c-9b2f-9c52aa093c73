@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 217 33% 17%;
    --foreground: 210 40% 98%;
    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;
    --border: 216 34% 17%;
    --input: 217 33% 17%;
    --primary: 230 48% 47%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --ring: 224.3 76.3% 48%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-[#2a2f3a];
  }
  body {
    @apply bg-[#1a1f2b] text-foreground;
  }
}

@layer components {
  .main-content {
    @apply pt-16 lg:pl-64 min-h-screen bg-[#1a1f2b];
  }

  .content-container {
    @apply p-6;
  }

  .sidebar-link {
    @apply flex items-center gap-3 px-4 py-2.5 text-gray-300 hover:bg-[#3e4784] hover:text-white transition-colors relative;
  }

  .sidebar-link.active {
    @apply bg-[#3e4784] text-white;
  }

  .sidebar-link.active::before {
    content: '';
    @apply absolute inset-0 bg-[#3e4784];
  }

  .sidebar-group {
    @apply space-y-0.5 relative;
  }

  .sidebar-submenu {
    @apply ml-4 space-y-0.5 relative;
  }

  .logo-section {
    @apply flex items-center h-16 px-4 border-b border-[#2a2f3a];
  }

  .logo-text {
    @apply text-white font-semibold;
  }

  .section-title {
    @apply text-2xl font-semibold text-white mb-4;
  }

  .card {
    @apply bg-[#1e2330] p-6;
  }

  .welcome-banner {
    @apply mb-8 p-6 bg-[#1e2330];
  }

  .welcome-title {
    @apply text-2xl font-semibold text-white mb-2;
  }

  .welcome-subtitle {
    @apply text-gray-400;
  }

  .quick-links {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
  }

  .quick-link-card {
    @apply flex items-center p-4 bg-[#1e2330] hover:bg-[#252a3a] transition-colors;
  }

  /* Form styles */
  .form-input {
    @apply mt-1 block w-full rounded-none border-[#2a2f3a] bg-[#1e2330] text-white focus:border-[#3e4784] focus:ring-[#3e4784];
  }

  .form-label {
    @apply block text-sm font-medium text-gray-300;
  }

  .form-select {
    @apply mt-1 block w-full rounded-none border-[#2a2f3a] bg-[#1e2330] text-white focus:border-[#3e4784] focus:ring-[#3e4784];
  }

  .form-button {
    @apply inline-flex items-center px-4 py-2 bg-[#3e4784] text-white hover:bg-[#4a559e] focus:outline-none focus:ring-2 focus:ring-[#3e4784] focus:ring-offset-[#1a1f2b];
  }

  /* Table styles */
  .table-container {
    @apply bg-[#1e2330] overflow-hidden;
  }

  .table-header {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider bg-[#252a3a];
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-300;
  }

  /* Navigation styles */
  .nav-item {
    @apply flex items-center px-4 py-2 text-sm font-medium transition-colors;
  }

  .nav-item-active {
    @apply text-white bg-[#3e4784];
  }

  .nav-item-inactive {
    @apply text-gray-300 hover:text-white hover:bg-[#3e4784];
  }

  .nav-group {
    @apply space-y-0.5;
  }

  .nav-group-header {
    @apply flex items-center w-full px-4 py-2 text-sm font-medium text-gray-300;
  }

  .nav-child-item {
    @apply flex items-center pl-12 pr-4 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-[#3e4784];
  }

  /* Mobile menu button */
  .mobile-menu-button {
    @apply text-gray-300 hover:text-white focus:outline-none;
  }
}
