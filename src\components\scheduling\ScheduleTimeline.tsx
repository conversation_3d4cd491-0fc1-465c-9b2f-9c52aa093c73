'use client';

import React, { useState } from 'react';
import {
  DndContext,
  MouseSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers';
import { useProcessStore, useWorkerStore } from '@/store';
import { ProcessTracking } from '@/types/process';
import { Process } from '@/types/process';
import { Worker } from '@/types/worker';
import { format, addDays, subDays } from 'date-fns';

interface ScheduleTimelineProps {
  selectedDate: Date;
  onDateChange: (date: Date) => void;
}

export function ScheduleTimeline({
  selectedDate,
  onDateChange,
}: ScheduleTimelineProps) {
  const [activeId, setActiveId] = useState<string | null>(null);
  const { processes, loading: processLoading, error: processError, updateProcess } = useProcessStore();
  const { workers, loading: workerLoading } = useWorkerStore();

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const hours = Array.from({ length: 12 }, (_, i) => i + 8); // 8 AM to 8 PM

  const handlePrevDay = () => {
    onDateChange(subDays(selectedDate, 1));
  };

  const handleNextDay = () => {
    onDateChange(addDays(selectedDate, 1));
  };

  const handleDragStart = (event: any) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = async (event: any) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const processId = active.id;
      const newTime = over.id;
      
      try {
        // Use 'planned_start_time' as defined in ProcessTracking type
        // TODO: Fix field name after database schema update
        await updateProcess(processId, {
          // planned_start_time: newTime, 
        });
      } catch (error) {
        console.error('Error updating process schedule:', error);
      }
    }
    
    setActiveId(null);
  };

  if (processLoading || workerLoading) {
    return <div>Loading schedule...</div>;
  }

  if (processError) {
    return <div>Error: {String(processError)}</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <button
          onClick={handlePrevDay}
          className="btn btn-secondary"
        >
          Previous Day
        </button>
        <h2 className="text-xl font-semibold">
          {format(selectedDate, 'MMMM d, yyyy')}
        </h2>
        <button
          onClick={handleNextDay}
          className="btn btn-secondary"
        >
          Next Day
        </button>
      </div>

      <DndContext
        sensors={sensors}
        modifiers={[restrictToHorizontalAxis]}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="overflow-x-auto">
          <div className="min-w-max">
            {/* Time slots header */}
            <div className="grid grid-cols-12 gap-4 mb-4">
              {hours.map((hour) => (
                <div
                  key={hour}
                  className="text-center font-medium"
                >
                  {hour}:00
                </div>
              ))}
            </div>

            {/* Worker rows */}
            {workers.map((worker) => (
              <div
                key={worker.worker_id}
                className="grid grid-cols-12 gap-4 mb-4"
              >
                <div className="col-span-12 grid grid-cols-12 gap-4">
                  <div className="col-span-2 font-medium">
                    {worker.name}
                  </div>
                  {/* Time slots */}
                  {hours.map((hour) => {
                    // TEMPORARY: Using type assertion to address mismatched types
                    // Per PROJECT_RULES.md Section 1.1, types should have a single source of truth
                    const timeSlotProcesses = processes.filter(
                      (p) => {
                        // Type assertion to access ProcessTracking properties on Process objects
                        const processTracking = p as unknown as ProcessTracking & { scheduled_time?: string };
                        return processTracking.worker_id === worker.worker_id && 
                          processTracking.scheduled_time && 
                          new Date(processTracking.scheduled_time).getHours() === hour;
                      }
                    );

                    return (
                      <div
                        key={`${worker.worker_id}-${hour}`}
                        className="col-span-1 min-h-[40px] border border-gray-200 rounded"
                      >
                        {timeSlotProcesses.map((process) => {
                          // Type assertion to access ProcessTracking properties on Process
                          const processTracking = process as unknown as ProcessTracking & { 
                            process_id: string, 
                            status: string,
                            scheduled_time?: string
                          };
                          
                          return (
                            <div
                              key={processTracking.process_id}
                              className={`
                                p-2 m-1 rounded text-sm
                                ${processTracking.process_id === activeId ? 'opacity-50' : ''}
                                ${processTracking.status === 'COMPLETED' ? 'bg-green-100' :
                                  processTracking.status === 'IN_PROGRESS' ? 'bg-blue-100' :
                                  'bg-gray-100'}
                              `}
                            >
                              {process.description}
                            </div>
                          );
                        })}
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </DndContext>
    </div>
  );
}
