/**
 * Worker Management Store
 * Manages state for workshop workers using Zustand
 * @module store/workerStore
 */

import { create } from 'zustand';
import type { WorkerStore } from './types';
import { WorkerRepository } from '@/db/repositories/WorkerRepository';
import { Worker } from '@/types/domain/worker.types';

/**
 * Worker repository instance for database operations
 */
const workerRepo = new WorkerRepository();

/**
 * Worker store using Zustand
 * Handles worker state management and CRUD operations
 * 
 * @example
 * ```typescript
 * // Using the worker store in a component
 * const { workers, addWorker, updateWorker } = useWorkerStore();
 * 
 * // Add a new worker
 * await addWorker({
 *   name: '<PERSON>',
 *   skill_level: 4,
 *   efficiency_factor: 1.2
 * });
 * 
 * // Update a worker
 * await updateWorker('W123', {
 *   efficiency_factor: 1.3
 * });
 * ```
 */
export const useWorkerStore = create<WorkerStore>((set) => ({
  workers: [],
  loading: false,
  error: null,

  /**
   * Fetches all workers from the database
   * Updates the store with the fetched workers
   * 
   * @throws {Error} If worker fetching fails
   * 
   * @example
   * ```typescript
   * await fetchWorkers();
   * // Store now contains updated worker list
   * ```
   */
  fetchWorkers: async () => {
    set({ loading: true });
    try {
      const workers = await workerRepo.findAll();
      set({ workers, loading: false });
    } catch (error) {
      set({ error: error as Error, loading: false });
    }
  },

  /**
   * Adds a new worker to the system
   * 
   * @param {Worker} worker - Worker data to create
   * @throws {Error} If worker creation fails
   * 
   * @example
   * ```typescript
   * await addWorker({
   *   name: 'Jane Smith',
   *   skill_level: 3,
   *   efficiency_factor: 1.1,
   *   is_active: true
   * });
   * ```
   */
  // Add explicit type for worker parameter based on WorkerStore interface
  addWorker: async (worker: Omit<Worker, 'worker_id'>) => { 
    set({ loading: true });
    try {
      // Apply double type assertion for the create call
      await workerRepo.create(worker as unknown as Omit<Worker, 'id'>); 
      const workers = await workerRepo.findAll();
      set({ workers, loading: false });
    } catch (error) {
      set({ error: error as Error, loading: false });
    }
  },

  /**
   * Updates an existing worker's information
   * 
   * @param {string} id - ID of the worker to update
   * @param {Partial<Worker>} data - Updated worker data
   * @throws {Error} If worker update fails
   * 
   * @example
   * ```typescript
   * await updateWorker('W123', {
   *   skill_level: 5,
   *   efficiency_factor: 1.4
   * });
   * ```
   */
  // Add explicit types for id and data based on WorkerStore interface
  updateWorker: async (id: string, data: Partial<Worker>) => { 
    set({ loading: true });
    try {
      await workerRepo.update(id, data);
      const workers = await workerRepo.findAll();
      set({ workers, loading: false });
    } catch (error) {
      set({ error: error as Error, loading: false });
    }
  },

  /**
   * Deletes a worker from the system safely
   * This method handles all foreign key constraints by first cleaning up related records
   * 
   * @param {string} id - ID of the worker to delete
   * @throws {Error} If worker deletion fails or has dependencies
   * 
   * @example
   * ```typescript
   * await deleteWorker('W123');
   * ```
   */
  // Add explicit type for id
  deleteWorker: async (id: string) => { 
    set({ loading: true });
    try {
      // Use our safe delete method that handles foreign key constraints
      await workerRepo.safeDelete(id);
      const workers = await workerRepo.findAll();
      set({ workers, loading: false });
    } catch (error) {
      set({ error: error as Error, loading: false });
    }
  },
}));
