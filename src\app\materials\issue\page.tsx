/**
 * Material Issue Hub Page
 * Unified page for all material issue operations
 * 
 * @module app/materials/issue
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  GemIcon, 
  CircleIcon, 
  ScaleIcon,
  PlusIcon,
  ClockIcon,
  UserIcon
} from 'lucide-react';
import { StoneIssueForm } from '@/components/material-issue/StoneIssueForm';
import { FindingIssueForm } from '@/components/material-issue/FindingIssueForm';
import { MetalIssueForm } from '@/components/material-issue/MetalIssueForm';

interface RecentIssue {
  id: string;
  type: 'stone' | 'finding' | 'metal';
  description: string;
  worker: string;
  process: string;
  timestamp: string;
  status: 'completed' | 'pending';
}

interface InventorySummary {
  stones: {
    total_types: number;
    total_carats: number;
    available_locations: number;
  };
  findings: {
    total_assemblies: number;
    total_weight_grams: number;
    available_locations: number;
  };
  metals: {
    total_weight_grams: number;
    available_karats: number;
    available_locations: number;
  };
}

export default function MaterialIssuePage() {
  const [activeTab, setActiveTab] = useState('stones');
  const [recentIssues] = useState<RecentIssue[]>([
    {
      id: '1',
      type: 'stone',
      description: '50 pcs Diamond - Round - 2mm',
      worker: 'John Doe',
      process: 'Setting',
      timestamp: '2 hours ago',
      status: 'completed'
    },
    {
      id: '2',
      type: 'metal',
      description: '25.5g Gold - 18K',
      worker: 'Jane Smith',
      process: 'Filing',
      timestamp: '3 hours ago',
      status: 'completed'
    },
    {
      id: '3',
      type: 'finding',
      description: 'Polki Assembly - 15.2g',
      worker: 'Mike Johnson',
      process: 'Setting',
      timestamp: '4 hours ago',
      status: 'completed'
    }
  ]);

  const [inventorySummary] = useState<InventorySummary>({
    stones: {
      total_types: 25,
      total_carats: 1250.5,
      available_locations: 4
    },
    findings: {
      total_assemblies: 45,
      total_weight_grams: 680.3,
      available_locations: 3
    },
    metals: {
      total_weight_grams: 2450.8,
      available_karats: 6,
      available_locations: 4
    }
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'stone':
        return <GemIcon className="w-4 h-4" />;
      case 'finding':
        return <CircleIcon className="w-4 h-4" />;
      case 'metal':
        return <ScaleIcon className="w-4 h-4" />;
      default:
        return <PlusIcon className="w-4 h-4" />;
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'stone':
        return 'bg-blue-100 text-blue-800';
      case 'finding':
        return 'bg-purple-100 text-purple-800';
      case 'metal':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Issue Materials</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Issue stones, findings, and metals to workers for processing
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Main Issue Forms */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PlusIcon className="w-5 h-5" />
                Material Issue Forms
              </CardTitle>
              <CardDescription>
                Select the type of material to issue to workers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="stones" className="flex items-center gap-2">
                    <GemIcon className="w-4 h-4" />
                    Stones
                  </TabsTrigger>
                  <TabsTrigger value="findings" className="flex items-center gap-2">
                    <CircleIcon className="w-4 h-4" />
                    Findings
                  </TabsTrigger>
                  <TabsTrigger value="metals" className="flex items-center gap-2">
                    <ScaleIcon className="w-4 h-4" />
                    Metals
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="stones" className="mt-6">
                  <StoneIssueForm />
                </TabsContent>

                <TabsContent value="findings" className="mt-6">
                  <FindingIssueForm />
                </TabsContent>

                <TabsContent value="metals" className="mt-6">
                  <MetalIssueForm />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Inventory Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Available Inventory</CardTitle>
              <CardDescription>
                Current stock summary
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-3 rounded-lg border">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <GemIcon className="w-4 h-4 text-blue-600" />
                    <span className="font-medium">Stones</span>
                  </div>
                  <Badge variant="secondary">{inventorySummary.stones.total_types} types</Badge>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>Total: {inventorySummary.stones.total_carats} carats</div>
                  <div>Locations: {inventorySummary.stones.available_locations}</div>
                </div>
              </div>

              <div className="p-3 rounded-lg border">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <CircleIcon className="w-4 h-4 text-purple-600" />
                    <span className="font-medium">Findings</span>
                  </div>
                  <Badge variant="secondary">{inventorySummary.findings.total_assemblies} items</Badge>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>Total: {inventorySummary.findings.total_weight_grams}g</div>
                  <div>Locations: {inventorySummary.findings.available_locations}</div>
                </div>
              </div>

              <div className="p-3 rounded-lg border">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <ScaleIcon className="w-4 h-4 text-yellow-600" />
                    <span className="font-medium">Metals</span>
                  </div>
                  <Badge variant="secondary">{inventorySummary.metals.available_karats} karats</Badge>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>Total: {inventorySummary.metals.total_weight_grams}g</div>
                  <div>Locations: {inventorySummary.metals.available_locations}</div>
                </div>
              </div>

              <Button variant="outline" className="w-full">
                View Full Inventory
              </Button>
            </CardContent>
          </Card>

          {/* Recent Issues */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <ClockIcon className="w-4 h-4" />
                Recent Issues
              </CardTitle>
              <CardDescription>
                Latest material issues
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentIssues.map((issue) => (
                  <div key={issue.id} className="p-3 rounded-lg border">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getTypeIcon(issue.type)}
                        <Badge className={getTypeBadgeColor(issue.type)}>
                          {issue.type}
                        </Badge>
                      </div>
                      <span className="text-xs text-gray-500">{issue.timestamp}</span>
                    </div>
                    <div className="text-sm font-medium mb-1">{issue.description}</div>
                    <div className="flex items-center gap-2 text-xs text-gray-600">
                      <UserIcon className="w-3 h-3" />
                      <span>{issue.worker}</span>
                      <span>•</span>
                      <span>{issue.process}</span>
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="outline" className="w-full mt-4">
                View All Issues
              </Button>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <PlusIcon className="w-4 h-4 mr-2" />
                Bulk Issue
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <ClockIcon className="w-4 h-4 mr-2" />
                Pending Issues
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <UserIcon className="w-4 h-4 mr-2" />
                Worker Assignments
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
