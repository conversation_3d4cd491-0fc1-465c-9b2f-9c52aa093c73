'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Search, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/lib/db';

interface ProcessMaster {
  process_id: string;
  name: string;
  description: string | null;
  standard_time: number;
  is_active: boolean | null;
  requires_skill: boolean | null;
  can_be_outsourced: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

export default function ProcessesPage() {
  const [processes, setProcesses] = useState<ProcessMaster[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingProcess, setEditingProcess] = useState<ProcessMaster | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    standard_time: 0,
    is_active: true,
    requires_skill: true,
    can_be_outsourced: false,
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchProcesses();
  }, []);

  const fetchProcesses = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('process_mast')
        .select('*')
        .order('name');

      if (error) throw error;
      setProcesses(data || []);
    } catch (error) {
      console.error('Error fetching processes:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch processes',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingProcess) {
        // Update existing process
        const { error } = await supabase
          .from('process_mast')
          .update({
            ...formData,
            updated_at: new Date().toISOString(),
          })
          .eq('process_id', editingProcess.process_id);

        if (error) throw error;
        toast({
          title: 'Success',
          description: 'Process updated successfully',
        });
      } else {
        // Create new process
        const { error } = await supabase
          .from('process_mast')
          .insert([{
            ...formData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }]);

        if (error) throw error;
        toast({
          title: 'Success',
          description: 'Process created successfully',
        });
      }

      setShowForm(false);
      setEditingProcess(null);
      setFormData({
        name: '',
        description: '',
        standard_time: 0,
        is_active: true,
        requires_skill: true,
        can_be_outsourced: false,
      });
      fetchProcesses();
    } catch (error) {
      console.error('Error saving process:', error);
      toast({
        title: 'Error',
        description: 'Failed to save process',
        variant: 'destructive',
      });
    }
  };

  const handleEdit = (process: ProcessMaster) => {
    setEditingProcess(process);
    setFormData({
      name: process.name,
      description: process.description || '',
      standard_time: process.standard_time,
      is_active: process.is_active ?? true,
      requires_skill: process.requires_skill ?? true,
      can_be_outsourced: process.can_be_outsourced ?? false,
    });
    setShowForm(true);
  };

  const handleDelete = async (processId: string) => {
    if (!confirm('Are you sure you want to delete this process?')) return;

    try {
      const { error } = await supabase
        .from('process_mast')
        .delete()
        .eq('process_id', processId);

      if (error) throw error;
      toast({
        title: 'Success',
        description: 'Process deleted successfully',
      });
      fetchProcesses();
    } catch (error) {
      console.error('Error deleting process:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete process',
        variant: 'destructive',
      });
    }
  };

  const filteredProcesses = processes.filter(process =>
    process.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (process.description && process.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Process Management</h1>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Process
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search processes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingProcess ? 'Edit Process' : 'Add New Process'}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Name *</label>
                  <Input
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Standard Time (minutes) *</label>
                  <Input
                    type="number"
                    value={formData.standard_time}
                    onChange={(e) => setFormData({ ...formData, standard_time: Number(e.target.value) })}
                    required
                    min="0"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <Input
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </div>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    className="mr-2"
                  />
                  Active
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.requires_skill}
                    onChange={(e) => setFormData({ ...formData, requires_skill: e.target.checked })}
                    className="mr-2"
                  />
                  Requires Skill
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.can_be_outsourced}
                    onChange={(e) => setFormData({ ...formData, can_be_outsourced: e.target.checked })}
                    className="mr-2"
                  />
                  Can be Outsourced
                </label>
              </div>
              <div className="flex space-x-2">
                <Button type="submit">
                  {editingProcess ? 'Update' : 'Create'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowForm(false);
                    setEditingProcess(null);
                    setFormData({
                      name: '',
                      description: '',
                      standard_time: 0,
                      is_active: true,
                      requires_skill: true,
                      can_be_outsourced: false,
                    });
                  }}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4">
        {filteredProcesses.map((process) => (
          <Card key={process.process_id}>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-lg font-semibold">{process.name}</h3>
                    <Badge variant={process.is_active ? 'default' : 'secondary'}>
                      {process.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                    {process.requires_skill && (
                      <Badge variant="outline">Skill Required</Badge>
                    )}
                    {process.can_be_outsourced && (
                      <Badge variant="outline">Outsourceable</Badge>
                    )}
                  </div>
                  {process.description && (
                    <p className="text-gray-600 mb-2">{process.description}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    Standard Time: {process.standard_time} minutes
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(process)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(process.process_id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredProcesses.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No processes found</p>
        </div>
      )}
    </div>
  );
}
