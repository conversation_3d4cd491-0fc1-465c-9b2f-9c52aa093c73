/**
 * @page reports/loss-analysis
 * @description Loss Analysis Report Page - Core business reporting dashboard
 */

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LossAnalysisDashboard } from '@/components/reports/LossAnalysisDashboard';
import { 
  ArrowLeftIcon,
  TrendingDownIcon,
  BarChart3Icon,
  CalendarIcon,
  TargetIcon
} from 'lucide-react';
import Link from 'next/link';

export default function LossAnalysisPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/materials">
            <Button variant="ghost" size="sm">
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              Back to Materials
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Loss Analysis Reports</h1>
            <p className="text-gray-600 mt-1">
              Comprehensive material loss tracking and analysis by order, worker, and process
            </p>
          </div>
        </div>
        <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
          <TrendingDownIcon className="w-3 h-3 mr-1" />
          Core Business
        </Badge>
      </div>

      {/* Business Value Highlights */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <CalendarIcon className="w-8 h-8 text-blue-600" />
              <div>
                <h3 className="font-semibold">Monthly Reports</h3>
                <p className="text-sm text-gray-600">
                  Total loss by month
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TargetIcon className="w-8 h-8 text-green-600" />
              <div>
                <h3 className="font-semibold">Order Tracking</h3>
                <p className="text-sm text-gray-600">
                  Loss per specific order
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <BarChart3Icon className="w-8 h-8 text-purple-600" />
              <div>
                <h3 className="font-semibold">Worker Performance</h3>
                <p className="text-sm text-gray-600">
                  Loss by worker analysis
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950 dark:to-red-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingDownIcon className="w-8 h-8 text-orange-600" />
              <div>
                <h3 className="font-semibold">Process Efficiency</h3>
                <p className="text-sm text-gray-600">
                  Loss by process type
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard */}
      <LossAnalysisDashboard />

      {/* Business Insights */}
      <Card className="bg-gray-50 dark:bg-gray-900">
        <CardHeader>
          <CardTitle className="text-lg">Key Business Insights</CardTitle>
          <CardDescription>
            Understanding your material loss patterns for better decision making
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold">Monthly Loss Analysis:</h4>
              <ul className="text-sm space-y-2">
                <li><strong>Total Loss Tracking:</strong> Monitor overall material loss for the month</li>
                <li><strong>Trend Analysis:</strong> Compare with previous months to identify patterns</li>
                <li><strong>Cost Impact:</strong> Calculate financial impact of material losses</li>
                <li><strong>Target Setting:</strong> Set realistic loss reduction targets</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold">Order-Specific Tracking:</h4>
              <ul className="text-sm space-y-2">
                <li><strong>Order Loss %:</strong> Track loss for specific customer orders</li>
                <li><strong>Process Breakdown:</strong> See which processes cause most loss per order</li>
                <li><strong>Customer Impact:</strong> Understand loss impact on customer profitability</li>
                <li><strong>Quality Control:</strong> Identify orders with excessive loss</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Items */}
      <Card className="border-l-4 border-l-red-500">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <TargetIcon className="w-5 h-5" />
            Actionable Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <h4 className="font-medium text-red-700">🚨 High Priority:</h4>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Orders with {'>'}5% loss require immediate review</li>
                <li>• Workers with declining trends need retraining</li>
                <li>• Processes exceeding expected loss need optimization</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-yellow-700">⚠️ Medium Priority:</h4>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Monthly loss trends above 3% average</li>
                <li>• Worker performance variations {'>'}1%</li>
                <li>• Process efficiency scores below 90</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-green-700">✅ Best Practices:</h4>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Recognize workers with {'<'}2% average loss</li>
                <li>• Document processes with {'>'}100 efficiency</li>
                <li>• Share successful loss reduction techniques</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Quick Actions</CardTitle>
          <CardDescription>
            Common tasks for loss analysis and management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Link href="/materials/universal-receipt">
              <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <TargetIcon className="w-4 h-4 text-green-600" />
                </div>
                <div className="text-center">
                  <div className="font-medium">Process Receipt</div>
                  <div className="text-xs text-gray-500">Record new loss data</div>
                </div>
              </Button>
            </Link>

            <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <CalendarIcon className="w-4 h-4 text-blue-600" />
              </div>
              <div className="text-center">
                <div className="font-medium">Monthly Report</div>
                <div className="text-xs text-gray-500">Generate full report</div>
              </div>
            </Button>

            <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <BarChart3Icon className="w-4 h-4 text-purple-600" />
              </div>
              <div className="text-center">
                <div className="font-medium">Worker Analysis</div>
                <div className="text-xs text-gray-500">Performance review</div>
              </div>
            </Button>

            <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <TrendingDownIcon className="w-4 h-4 text-orange-600" />
              </div>
              <div className="text-center">
                <div className="font-medium">Process Review</div>
                <div className="text-xs text-gray-500">Efficiency analysis</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
