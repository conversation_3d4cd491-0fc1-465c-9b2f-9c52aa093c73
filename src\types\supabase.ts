export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      activity_log: {
        Row: {
          log_id: string
          user_id: string | null
          activity_time: string | null
          activity_type: string
          entity_type: string
          entity_id: string
          old_value: Json | null
          new_value: Json | null
          ip_address: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          log_id?: string
          user_id: string
          activity_time?: string | null
          activity_type: string
          entity_type: string
          entity_id: string
          old_value: Json
          new_value: Json
          ip_address: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          log_id?: string
          user_id?: string | null
          activity_time?: string | null
          activity_type?: string
          entity_type?: string
          entity_id?: string
          old_value?: Json | null
          new_value?: Json | null
          ip_address?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      customer_mast: {
        Row: {
          customer_id: string
          description: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          customer_id?: string
          description: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          customer_id?: string
          description?: string
          created_at?: string | null
          updated_at?: string | null
        }
      }
      customer_material_receipt: {
        Row: {
          receipt_id: string
          customer_id: string | null
          order_id: string | null
          receipt_date: string | null
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          receipt_id?: string
          customer_id: string
          order_id: string
          receipt_date?: string | null
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          receipt_id?: string
          customer_id?: string | null
          order_id?: string | null
          receipt_date?: string | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      diamond_cut_mast: {
        Row: {
          cut_id: string
          name: string
          description: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          cut_id?: string
          name: string
          description: string
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          cut_id?: string
          name?: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      external_processor_mast: {
        Row: {
          processor_id: string
          name: string
          contact_person: string | null
          phone: string | null
          email: string | null
          address: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          processor_id?: string
          name: string
          contact_person: string
          phone: string
          email: string
          address: string
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          processor_id?: string
          name?: string
          contact_person?: string | null
          phone?: string | null
          email?: string | null
          address?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      findings_inventory: {
        Row: {
          finding_id: string
          name: string
          description: string | null
          quantity_on_hand: number
          uom_id: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          finding_id?: string
          name: string
          description: string
          quantity_on_hand?: number
          uom_id: string
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          finding_id?: string
          name?: string
          description?: string | null
          quantity_on_hand?: number
          uom_id?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      gold_colour_mast: {
        Row: {
          gold_colour_id: string
          description: string
          processing_complexity_factor: number
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          gold_colour_id?: string
          description: string
          processing_complexity_factor: number
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          gold_colour_id?: string
          description?: string
          processing_complexity_factor?: number
          created_at?: string | null
          updated_at?: string | null
        }
      }
      holiday_calendar: {
        Row: {
          holiday_id: string
          holiday_date: string
          description: string | null
          holiday_type: string
          working_hours: number | null
          is_recurring: boolean | null
          recurring_rule: Json | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          holiday_id?: string
          holiday_date: string
          description: string
          holiday_type: string
          working_hours: number
          is_recurring?: boolean | null
          recurring_rule: Json
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          holiday_id?: string
          holiday_date?: string
          description?: string | null
          holiday_type?: string
          working_hours?: number | null
          is_recurring?: boolean | null
          recurring_rule?: Json | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      inventory_locations: {
        Row: {
          location_id: string
          location_name: string
          location_type: string
          is_active: boolean | null
          parent_location_id: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          location_id?: string
          location_name: string
          location_type: string
          is_active?: boolean | null
          parent_location_id: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          location_id?: string
          location_name?: string
          location_type?: string
          is_active?: boolean | null
          parent_location_id?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      item_type_mast: {
        Row: {
          item_type_id: string
          description: string
          average_processing_time: number
          created_at: string | null
          updated_at: string | null
          suffix: string
        }
        Insert: {
          item_type_id?: string
          description: string
          average_processing_time: number
          created_at?: string | null
          updated_at?: string | null
          suffix: string
        }
        Update: {
          item_type_id?: string
          description?: string
          average_processing_time?: number
          created_at?: string | null
          updated_at?: string | null
          suffix?: string
        }
      }
      karat_mast: {
        Row: {
          karat_id: string
          description: string
          purity: number
          standard_wastage: number
          created_at: string | null
          updated_at: string | null
          density: number
        }
        Insert: {
          karat_id?: string
          description: string
          purity: number
          standard_wastage: number
          created_at?: string | null
          updated_at?: string | null
          density?: number
        }
        Update: {
          karat_id?: string
          description?: string
          purity?: number
          standard_wastage?: number
          created_at?: string | null
          updated_at?: string | null
          density?: number
        }
      }
      metal_inventory_transactions: {
        Row: {
          transaction_id: string
          transaction_date: string | null
          transaction_type: string
          from_location_id: string | null
          to_location_id: string | null
          karat_id: string
          gold_colour_id: string | null
          weight_in_grams: number
          reference_number: string | null
          order_id: string | null
          notes: string | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          transaction_id?: string
          transaction_date?: string | null
          transaction_type: string
          from_location_id: string
          to_location_id: string
          karat_id: string
          gold_colour_id: string
          weight_in_grams: number
          reference_number: string
          order_id: string
          notes: string
          created_by: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          transaction_id?: string
          transaction_date?: string | null
          transaction_type?: string
          from_location_id?: string | null
          to_location_id?: string | null
          karat_id?: string
          gold_colour_id?: string | null
          weight_in_grams?: number
          reference_number?: string | null
          order_id?: string | null
          notes?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      metal_location_balances: {
        Row: {
          balance_id: string
          location_id: string
          karat_id: string
          gold_colour_id: string | null
          current_weight: number
          last_updated: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          balance_id?: string
          location_id: string
          karat_id: string
          gold_colour_id: string
          current_weight?: number
          last_updated?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          balance_id?: string
          location_id?: string
          karat_id?: string
          gold_colour_id?: string | null
          current_weight?: number
          last_updated?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      metal_pool: {
        Row: {
          pool_id: string
          metal_type_id: string | null
          karat_id: string | null
          initial_weight: number
          current_weight: number
          customer_id: string | null
          uom_id: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          pool_id?: string
          metal_type_id: string
          karat_id: string
          initial_weight: number
          current_weight: number
          customer_id: string
          uom_id: string
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          pool_id?: string
          metal_type_id?: string | null
          karat_id?: string | null
          initial_weight?: number
          current_weight?: number
          customer_id?: string | null
          uom_id?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      metal_transaction_type_mast: {
        Row: {
          type_id: string
          code: string
          name: string
          description: string | null
          affects_pool: boolean
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          type_id?: string
          code: string
          name: string
          description: string
          affects_pool: boolean
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          type_id?: string
          code?: string
          name?: string
          description?: string | null
          affects_pool?: boolean
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      metal_transactions: {
        Row: {
          transaction_id: string
          transaction_type_id: string | null
          pool_id: string | null
          related_transaction_id: string | null
          order_id: string | null
          process_id: string | null
          external_entity_id: string | null
          weight: number
          fine_weight: number | null
          uom_id: string | null
          transaction_date: string | null
          notes: string | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          transaction_id?: string
          transaction_type_id: string
          pool_id: string
          related_transaction_id: string
          order_id: string
          process_id: string
          external_entity_id: string
          weight: number
          fine_weight: number
          uom_id: string
          transaction_date?: string | null
          notes: string
          created_by: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          transaction_id?: string
          transaction_type_id?: string | null
          pool_id?: string | null
          related_transaction_id?: string | null
          order_id?: string | null
          process_id?: string | null
          external_entity_id?: string | null
          weight?: number
          fine_weight?: number | null
          uom_id?: string | null
          transaction_date?: string | null
          notes?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      metal_transformation: {
        Row: {
          transformation_id: string
          source_pool_id: string | null
          target_pool_id: string | null
          source_weight: number
          target_weight: number
          transformation_date: string | null
          process_id: string | null
          notes: string | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          transformation_id?: string
          source_pool_id: string
          target_pool_id: string
          source_weight: number
          target_weight: number
          transformation_date?: string | null
          process_id: string
          notes: string
          created_by: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          transformation_id?: string
          source_pool_id?: string | null
          target_pool_id?: string | null
          source_weight?: number
          target_weight?: number
          transformation_date?: string | null
          process_id?: string | null
          notes?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      metal_type_mast: {
        Row: {
          metal_type_id: string
          name: string
          description: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          metal_type_id?: string
          name: string
          description: string
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          metal_type_id?: string
          name?: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      order_category_mast: {
        Row: {
          order_category_id: string
          description: string
          base_processing_time: number
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          order_category_id?: string
          description: string
          base_processing_time: number
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          order_category_id?: string
          description?: string
          base_processing_time?: number
          created_at?: string | null
          updated_at?: string | null
        }
      }
      order_color_stones: {
        Row: {
          stone_id: string
          order_id: string | null
          receipt_id: string | null
          stone_type_id: string | null
          shape_id: string | null
          quantity: number
          weight: number | null
          size_mm: number | null
          uom_id: string | null
          status: string
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          stone_id?: string
          order_id: string
          receipt_id: string
          stone_type_id: string
          shape_id: string
          quantity: number
          weight: number
          size_mm: number
          uom_id: string
          status: string
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          stone_id?: string
          order_id?: string | null
          receipt_id?: string | null
          stone_type_id?: string | null
          shape_id?: string | null
          quantity?: number
          weight?: number | null
          size_mm?: number | null
          uom_id?: string | null
          status?: string
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      order_diamonds: {
        Row: {
          diamond_id: string
          order_id: string | null
          receipt_id: string | null
          cut_id: string | null
          shape_id: string | null
          quantity: number
          weight: number | null
          size_mm: number | null
          uom_id: string | null
          status: string
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          diamond_id?: string
          order_id: string
          receipt_id: string
          cut_id: string
          shape_id: string
          quantity: number
          weight: number
          size_mm: number
          uom_id: string
          status: string
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          diamond_id?: string
          order_id?: string | null
          receipt_id?: string | null
          cut_id?: string | null
          shape_id?: string | null
          quantity?: number
          weight?: number | null
          size_mm?: number | null
          uom_id?: string | null
          status?: string
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      order_metal_allocation: {
        Row: {
          allocation_id: string
          order_id: string | null
          pool_id: string | null
          allocated_weight: number
          actual_used_weight: number | null
          expected_return_weight: number | null
          actual_return_weight: number | null
          loss_weight: number | null
          uom_id: string | null
          status: string
          allocation_date: string | null
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          allocation_id?: string
          order_id: string
          pool_id: string
          allocated_weight: number
          actual_used_weight: number
          expected_return_weight: number
          actual_return_weight: number
          loss_weight: number
          uom_id: string
          status: string
          allocation_date?: string | null
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          allocation_id?: string
          order_id?: string | null
          pool_id?: string | null
          allocated_weight?: number
          actual_used_weight?: number | null
          expected_return_weight?: number | null
          actual_return_weight?: number | null
          loss_weight?: number | null
          uom_id?: string | null
          status?: string
          allocation_date?: string | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      order_polki: {
        Row: {
          polki_id: string
          order_id: string | null
          receipt_id: string | null
          size_id: string | null
          quantity: number
          weight: number | null
          uom_id: string | null
          status: string
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          polki_id?: string
          order_id: string
          receipt_id: string
          size_id: string
          quantity: number
          weight: number
          uom_id: string
          status: string
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          polki_id?: string
          order_id?: string | null
          receipt_id?: string | null
          size_id?: string | null
          quantity?: number
          weight?: number | null
          uom_id?: string | null
          status?: string
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      orders: {
        Row: {
          party_order_no: string | null
          style_code: string | null
          item_type_id: string
          karat_id: string
          gold_colour_id: string
          third_party_cust_id: string | null
          order_category_id: string
          issue_date: string
          party_delivery_date: string | null
          expected_delivery_date: string
          polki_quality: string | null
          diamond_quality: string | null
          diamond_wt_expected: number | null
          gold_wt_expected: number | null
          reference_image_url: string | null
          metal_received: boolean | null
          diamonds_received: boolean | null
          polki_received: boolean | null
          tags: string[] | null
          remarks: string | null
          order_status: string | null
          qr_code: string | null
          created_at: string | null
          updated_at: string | null
          order_id: string
          customer_id: string
        }
        Insert: {
          party_order_no: string
          style_code: string
          item_type_id: string
          karat_id: string
          gold_colour_id: string
          third_party_cust_id: string
          order_category_id: string
          issue_date: string
          party_delivery_date: string
          expected_delivery_date: string
          polki_quality: string
          diamond_quality: string
          diamond_wt_expected: number
          gold_wt_expected: number
          reference_image_url: string
          metal_received?: boolean | null
          diamonds_received?: boolean | null
          polki_received?: boolean | null
          tags: string[]
          remarks: string
          order_status?: string | null
          qr_code: string
          created_at?: string | null
          updated_at?: string | null
          order_id: string
          customer_id: string
        }
        Update: {
          party_order_no?: string | null
          style_code?: string | null
          item_type_id?: string
          karat_id?: string
          gold_colour_id?: string
          third_party_cust_id?: string | null
          order_category_id?: string
          issue_date?: string
          party_delivery_date?: string | null
          expected_delivery_date?: string
          polki_quality?: string | null
          diamond_quality?: string | null
          diamond_wt_expected?: number | null
          gold_wt_expected?: number | null
          reference_image_url?: string | null
          metal_received?: boolean | null
          diamonds_received?: boolean | null
          polki_received?: boolean | null
          tags?: string[] | null
          remarks?: string | null
          order_status?: string | null
          qr_code?: string | null
          created_at?: string | null
          updated_at?: string | null
          order_id?: string
          customer_id?: string
        }
      }
      polki_size_mast: {
        Row: {
          size_id: string
          size_name: string
          min_size: number | null
          max_size: number | null
          description: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          size_id?: string
          size_name: string
          min_size: number
          max_size: number
          description: string
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          size_id?: string
          size_name?: string
          min_size?: number | null
          max_size?: number | null
          description?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      process_diamond_consumption: {
        Row: {
          consumption_id: string
          process_id: string | null
          order_id: string | null
          diamond_id: string | null
          expected_quantity: number
          actual_quantity: number
          loss_quantity: number | null
          consumed_at: string | null
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          consumption_id?: string
          process_id: string
          order_id: string
          diamond_id: string
          expected_quantity: number
          actual_quantity: number
          loss_quantity: number
          consumed_at?: string | null
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          consumption_id?: string
          process_id?: string | null
          order_id?: string | null
          diamond_id?: string | null
          expected_quantity?: number
          actual_quantity?: number
          loss_quantity?: number | null
          consumed_at?: string | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      process_history: {
        Row: {
          history_id: string
          process_id: string | null
          worker_id: string | null
          order_id: string | null
          started_at: string | null
          completed_at: string | null
          estimated_duration: number | null
          actual_duration: number | null
          complexity: number | null
          efficiency_factor: number | null
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          history_id?: string
          process_id: string
          worker_id: string
          order_id: string
          started_at: string
          completed_at: string
          estimated_duration: number
          actual_duration: number
          complexity: number
          efficiency_factor: number
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          history_id?: string
          process_id?: string | null
          worker_id?: string | null
          order_id?: string | null
          started_at?: string | null
          completed_at?: string | null
          estimated_duration?: number | null
          actual_duration?: number | null
          complexity?: number | null
          efficiency_factor?: number | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      process_mast: {
        Row: {
          process_id: string
          name: string
          description: string | null
          standard_time: number
          is_active: boolean | null
          requires_skill: boolean | null
          can_be_outsourced: boolean | null
          quality_checkpoints: Json | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          process_id?: string
          name: string
          description: string
          standard_time: number
          is_active?: boolean | null
          requires_skill?: boolean | null
          can_be_outsourced?: boolean | null
          quality_checkpoints: Json
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          process_id?: string
          name?: string
          description?: string | null
          standard_time?: number
          is_active?: boolean | null
          requires_skill?: boolean | null
          can_be_outsourced?: boolean | null
          quality_checkpoints?: Json | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      process_metal_consumption: {
        Row: {
          consumption_id: string
          process_id: string | null
          order_id: string | null
          pool_id: string | null
          expected_weight: number
          actual_weight: number
          loss_weight: number | null
          uom_id: string | null
          consumed_at: string | null
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          consumption_id?: string
          process_id: string
          order_id: string
          pool_id: string
          expected_weight: number
          actual_weight: number
          loss_weight: number
          uom_id: string
          consumed_at?: string | null
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          consumption_id?: string
          process_id?: string | null
          order_id?: string | null
          pool_id?: string | null
          expected_weight?: number
          actual_weight?: number
          loss_weight?: number | null
          uom_id?: string | null
          consumed_at?: string | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      process_polki_consumption: {
        Row: {
          consumption_id: string
          process_id: string | null
          order_id: string | null
          polki_id: string | null
          expected_quantity: number
          actual_quantity: number
          loss_quantity: number | null
          consumed_at: string | null
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          consumption_id?: string
          process_id: string
          order_id: string
          polki_id: string
          expected_quantity: number
          actual_quantity: number
          loss_quantity: number
          consumed_at?: string | null
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          consumption_id?: string
          process_id?: string | null
          order_id?: string | null
          polki_id?: string | null
          expected_quantity?: number
          actual_quantity?: number
          loss_quantity?: number | null
          consumed_at?: string | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      process_stone_consumption: {
        Row: {
          consumption_id: string
          process_id: string | null
          order_id: string | null
          stone_id: string | null
          expected_quantity: number
          actual_quantity: number
          loss_quantity: number | null
          consumed_at: string | null
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          consumption_id?: string
          process_id: string
          order_id: string
          stone_id: string
          expected_quantity: number
          actual_quantity: number
          loss_quantity: number
          consumed_at?: string | null
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          consumption_id?: string
          process_id?: string | null
          order_id?: string | null
          stone_id?: string | null
          expected_quantity?: number
          actual_quantity?: number
          loss_quantity?: number | null
          consumed_at?: string | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      process_templates: {
        Row: {
          template_id: string
          item_type_id: string | null
          process_id: string | null
          sequence_number: number
          standard_hours: number
          dependencies: string[] | null
          skill_requirements: Json | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          template_id?: string
          item_type_id: string
          process_id: string
          sequence_number: number
          standard_hours: number
          dependencies: string[]
          skill_requirements: Json
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          template_id?: string
          item_type_id?: string | null
          process_id?: string | null
          sequence_number?: number
          standard_hours?: number
          dependencies?: string[] | null
          skill_requirements?: Json | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      process_tracking: {
        Row: {
          tracking_id: string
          order_id: string | null
          process_id: string | null
          worker_id: string | null
          planned_start_time: string | null
          planned_end_time: string | null
          actual_start_time: string | null
          actual_end_time: string | null
          status: string
          priority: number | null
          progress_percentage: number | null
          deviation_reason: string | null
          quality_checks: Json | null
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          tracking_id?: string
          order_id: string
          process_id: string
          worker_id: string
          planned_start_time: string
          planned_end_time: string
          actual_start_time: string
          actual_end_time: string
          status: string
          priority?: number | null
          progress_percentage?: number | null
          deviation_reason: string
          quality_checks: Json
          notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          tracking_id?: string
          order_id?: string | null
          process_id?: string | null
          worker_id?: string | null
          planned_start_time?: string | null
          planned_end_time?: string | null
          actual_start_time?: string | null
          actual_end_time?: string | null
          status?: string
          priority?: number | null
          progress_percentage?: number | null
          deviation_reason?: string | null
          quality_checks?: Json | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      resource_capacity: {
        Row: {
          process_id: string
          work_date: string
          total_minutes_available: number
          allocated_minutes: number
          buffer_minutes: number
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          process_id: string
          work_date: string
          total_minutes_available: number
          allocated_minutes?: number
          buffer_minutes?: number
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          process_id?: string
          work_date?: string
          total_minutes_available?: number
          allocated_minutes?: number
          buffer_minutes?: number
          created_at?: string | null
          updated_at?: string | null
        }
      }
      stone_shape_mast: {
        Row: {
          shape_id: string
          name: string
          description: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          shape_id?: string
          name: string
          description: string
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          shape_id?: string
          name?: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      stone_type_mast: {
        Row: {
          stone_type_id: string
          name: string
          description: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          stone_type_id?: string
          name: string
          description: string
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          stone_type_id?: string
          name?: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      styles_mast: {
        Row: {
          style_id: string
          item_type_id: string | null
          party_id: string | null
          net_wt: number
          net_wt_kt: string | null
          estimated_processing_time: number
          processing_notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          style_id?: string
          item_type_id: string
          party_id: string
          net_wt: number
          net_wt_kt: string
          estimated_processing_time: number
          processing_notes: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          style_id?: string
          item_type_id?: string | null
          party_id?: string | null
          net_wt?: number
          net_wt_kt?: string | null
          estimated_processing_time?: number
          processing_notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      third_party_cust_mast: {
        Row: {
          party_cust_id: string
          description: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          party_cust_id?: string
          description: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          party_cust_id?: string
          description?: string
          created_at?: string | null
          updated_at?: string | null
        }
      }
      uom_mast: {
        Row: {
          uom_id: string
          code: string
          name: string
          description: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          uom_id?: string
          code: string
          name: string
          description: string
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          uom_id?: string
          code?: string
          name?: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      user_roles: {
        Row: {
          user_id: string
          role: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          user_id: string
          role: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          user_id?: string
          role?: string
          created_at?: string | null
          updated_at?: string | null
        }
      }
      worker_mast: {
        Row: {
          worker_id: string
          name: string
          is_active: boolean | null
          is_vendor: boolean | null
          shift_start: string | null
          shift_end: string | null
          created_at: string | null
          updated_at: string | null
          worker_type: string | null
          available_from: string | null
          available_to: string | null
          skills: Json | null
          efficiency_factor: number | null
        }
        Insert: {
          worker_id?: string
          name: string
          is_active?: boolean | null
          is_vendor?: boolean | null
          shift_start: string
          shift_end: string
          created_at?: string | null
          updated_at?: string | null
          worker_type: string
          available_from: string
          available_to: string
          skills: Json
          efficiency_factor?: number | null
        }
        Update: {
          worker_id?: string
          name?: string
          is_active?: boolean | null
          is_vendor?: boolean | null
          shift_start?: string | null
          shift_end?: string | null
          created_at?: string | null
          updated_at?: string | null
          worker_type?: string | null
          available_from?: string | null
          available_to?: string | null
          skills?: Json | null
          efficiency_factor?: number | null
        }
      }
      worker_shifts: {
        Row: {
          shift_id: string
          worker_id: string | null
          day_of_week: number | null
          start_time: string
          end_time: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          shift_id?: string
          worker_id: string
          day_of_week: number
          start_time: string
          end_time: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          shift_id?: string
          worker_id?: string | null
          day_of_week?: number | null
          start_time?: string
          end_time?: string
          created_at?: string | null
          updated_at?: string | null
        }
      }
      worker_skills: {
        Row: {
          skill_id: string
          worker_id: string | null
          process_id: string | null
          skill_level: number
          efficiency_factor: number | null
          is_primary: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          skill_id?: string
          worker_id: string
          process_id: string
          skill_level: number
          efficiency_factor?: number | null
          is_primary?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          skill_id?: string
          worker_id?: string | null
          process_id?: string | null
          skill_level?: number
          efficiency_factor?: number | null
          is_primary?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      workshop_config: {
        Row: {
          config_id: string
          working_hours_per_day: number
          working_days_per_week: number
          shift_start_time: string
          shift_end_time: string
          break_duration_minutes: number
          effective_hours_per_day: number
          weekend_days: any[]
          allow_overtime: boolean | null
          max_overtime_hours: number | null
          buffer_hours_per_day: number | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          config_id?: string
          working_hours_per_day: number
          working_days_per_week: number
          shift_start_time: string
          shift_end_time: string
          break_duration_minutes: number
          effective_hours_per_day: number
          weekend_days: any[]
          allow_overtime?: boolean | null
          max_overtime_hours: number
          buffer_hours_per_day?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          config_id?: string
          working_hours_per_day?: number
          working_days_per_week?: number
          shift_start_time?: string
          shift_end_time?: string
          break_duration_minutes?: number
          effective_hours_per_day?: number
          weekend_days?: any[]
          allow_overtime?: boolean | null
          max_overtime_hours?: number | null
          buffer_hours_per_day?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
