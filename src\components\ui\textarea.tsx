/**
 * Textarea component for form fields
 *
 * A styled, accessible textarea component for use in forms.
 *
 * @param {React.TextareaHTMLAttributes<HTMLTextAreaElement>} props - Standard textarea props
 * @returns {JSX.Element} The rendered textarea element
 *
 * @example
 * <Textarea value={value} onChange={handleChange} />
 */
import * as React from 'react';

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

export const Textarea: React.FC<TextareaProps> = ({ className, ...props }) => {
  return (
    <textarea
      className={[
        'block w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm',
        className
      ].filter(Boolean).join(' ')}
      {...props}
    />
  );
};
