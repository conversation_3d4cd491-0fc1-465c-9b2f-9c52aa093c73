import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { WorkerAssignment } from '@/types/process';
import { toast } from 'react-hot-toast';

export function useWorkerAssignments() {
  const queryClient = useQueryClient();

  // Fetch worker assignments
  const {
    data: assignments,
    isLoading,
    error,
  } = useQuery<WorkerAssignment[]>({
    queryKey: ['worker-assignments'],
    queryFn: async () => {
      const response = await fetch('/api/processes/assignments');
      if (!response.ok) {
        throw new Error('Failed to fetch worker assignments');
      }
      return response.json();
    },
  });

  // Add worker assignment
  const addAssignment = useMutation({
    mutationFn: async (data: Partial<WorkerAssignment>) => {
      const response = await fetch('/api/processes/assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        throw new Error('Failed to add worker assignment');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['worker-assignments'] });
      toast.success('Worker assigned successfully');
    },
    onError: (error) => {
      toast.error('Failed to assign worker');
      console.error('Error assigning worker:', error);
    },
  });

  // Remove worker assignment
  const removeAssignment = useMutation({
    mutationFn: async ({
      workerId,
      processId,
    }: {
      workerId: string;
      processId: string;
    }) => {
      const response = await fetch(
        `/api/processes/assignments?worker_id=${workerId}&process_id=${processId}`,
        {
          method: 'DELETE',
        }
      );
      if (!response.ok) {
        throw new Error('Failed to remove worker assignment');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['worker-assignments'] });
      toast.success('Worker assignment removed successfully');
    },
    onError: (error) => {
      toast.error('Failed to remove worker assignment');
      console.error('Error removing worker assignment:', error);
    },
  });

  return {
    assignments,
    isLoading,
    error,
    addAssignment,
    removeAssignment,
  };
}
